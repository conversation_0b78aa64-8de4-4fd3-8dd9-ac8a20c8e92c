<?php

class ResidentAppQuery 
{
    private $db;
    private $medooDb;

    public function __construct($db, $medooDb) {
        $this->db = $db;
        $this->medooDb = $medooDb;
    }

    // 获取过期数据
    public function getExpireData($daysBefore) {
        $sth = $this->db->prepare("select AAA.UUID as DisUUID, AAA.Account as DisAccount, AA.UUID as InsUUID, A.UUID as CommunityUUID, A.ID as CommunityID, A.TimeZone, I.Switch, F.EnableSmartHome,
                A.Location as Community,U.UnitName as Building, P.RoomNumber as AptName, R.<PERSON> as Apt, P.UUID as PersonalAccountUUID, P.ExpireTime, P.Account as PersonalAccount
                from PersonalAccount P 
                join PersonalAccountCnf F on F.Account = P.Account
                join CommunityRoom R on R.UUID = P.CommunityRoomUUID 
                join CommunityUnit U on P.CommunityUnitUUID = U.UUID 
                join Account A on A.UUID = P.ParentUUID
                join Account AA on AA.ManageGroup = A.ManageGroup
                join Account AAA on AAA.UUID = AA.ParentUUID
                join CommunityInfo I on I.AccountID = A.ID
                where TO_DAYS(P.ExpireTime) = TO_DAYS(NOW()) + :daysBefore and P.role = :role and P.Active = 1 
                and P.Special = 0 and AA.Grade = 22 order by A.UUID");

        $sth->bindValue(':daysBefore', $daysBefore, PDO::PARAM_INT);
        $sth->bindValue(':role', ACCOUNT_ROLE_COMMUNITY_MAIN, PDO::PARAM_INT);
        $sth->execute();
        $expireAppList = $sth->fetchALL(PDO::FETCH_ASSOC);
        return $expireAppList;
    }
}