<?php
require_once(dirname(__FILE__) . '/email_type.php');
require_once(dirname(__FILE__) . '/check_expire_common_v4500.php');

const PAY_BY_LOWER_LEVEL = 0;       // 下级缴费
const PAY_BY_CURRENT_LEVEL = 1;     // 本级缴费


class OldOfficeCheck
{
    public static function needNotifyPm($pay_item, $charge_mode, $before)
    {
        $pm_mode = $charge_mode["ProjectMode"];
        $insmode = $charge_mode["InstallerMode"];
        $send_type = $charge_mode["SendExpireEmailType"];
        $subdis_mode = EmailNotifyRule::PAY_MODE_NORMAL;
        return EmailNotifyRule::IfNeedNotifyPm(PROJECT_TYPE_OFFICE, $subdis_mode, $insmode, $pm_mode, $send_type, $pay_item, $before);
    }

    public static function needNotifyIns($pay_item, $charge_mode, $before)
    {
        $pm_mode = $charge_mode["ProjectMode"];
        $insmode = $charge_mode["InstallerMode"];
        $send_type = $charge_mode["SendExpireEmailType"];
        $subdis_mode = EmailNotifyRule::PAY_MODE_NORMAL;
        return EmailNotifyRule::IfNeedNotifyIns(PROJECT_TYPE_OFFICE, $subdis_mode, $insmode, $pm_mode, $send_type, $pay_item, $before);
    }
    
    public static function needNotifyDis($pay_item, $charge_mode, $before)
    {
        $pm_mode = $charge_mode["ProjectMode"];
        $insmode = $charge_mode["InstallerMode"];
        $send_type = $charge_mode["SendExpireEmailType"];
        $subdis_mode = EmailNotifyRule::PAY_MODE_NORMAL;
        return EmailNotifyRule::IfNeedNotifyDis(PROJECT_TYPE_OFFICE, $subdis_mode, $insmode, $pm_mode, $send_type, $pay_item, $before);
    }

    public static function isNew($office_info)
    {
        return $office_info['IsNew'];
    }

    
    /********************** 是否有付费权限 **********************/
    public static function ifDisHasPayPermission()
    {
        //// Dis总是有付费权限
        return true;
    }

    public static function ifInsHasPayPermission($project_uuid)
    {
        $charge_info = getAccountChargeModeByUUID($project_uuid);
        $ins_mode = $charge_info['InstallerMode'];

        // ins 查看PayType字段
        if(EmailNotifyRule::IfHasPayPermission($ins_mode)) {
            return true;
        }

        return false;
    }

    public static function ifPMHasPayPermission($project_uuid)
    {
        $charge_info = getAccountChargeModeByUUID($project_uuid);
        $ins_mode = $charge_info['InstallerMode'];
        $pm_mode = $charge_info['ProjectMode'];

        // Ins和PM不一样的时候，视为没有权限（ins修改了支付方式 项目要保留旧的值，代表没有支付权限）
        if($ins_mode != $pm_mode){
            return false;
        }

        // pm 查看项目的设置
        if(EmailNotifyRule::IfHasPayPermission($pm_mode)) {
            return true;
        }

        return false;
    }
}

class NewOfficeCheck
{
    public static function needNotifyPm($pay_item, $charge_mode, $before)
    {
        $pm_mode = $charge_mode["ProjectMode"];
        $insmode = $charge_mode["InstallerMode"];
        $send_type = $charge_mode["SendExpireEmailType"];
        $subdis_mode = EmailNotifyRule::PAY_MODE_NORMAL;
        return EmailNotifyRule::IfNeedNotifyPm(PROJECT_TYPE_NEW_OFFICE, $subdis_mode, $insmode, $pm_mode, $send_type, $pay_item, $before);
    }

    public static function needNotifyIns($pay_item, $charge_mode, $before)
    {
        $pm_mode = $charge_mode["ProjectMode"];
        $insmode = $charge_mode["InstallerMode"];
        $send_type = $charge_mode["SendExpireEmailType"];
        $subdis_mode = EmailNotifyRule::PAY_MODE_NORMAL;
        return EmailNotifyRule::IfNeedNotifyIns(PROJECT_TYPE_NEW_OFFICE, $subdis_mode, $insmode, $pm_mode, $send_type, $pay_item, $before);
    }
    
    public static function needNotifyDis($pay_item, $charge_mode, $before)
    {
        $pm_mode = $charge_mode["ProjectMode"];
        $insmode = $charge_mode["InstallerMode"];
        $send_type = $charge_mode["SendExpireEmailType"];
        $subdis_mode = EmailNotifyRule::PAY_MODE_NORMAL;
        return EmailNotifyRule::IfNeedNotifyDis(PROJECT_TYPE_NEW_OFFICE, $subdis_mode, $insmode, $pm_mode, $send_type, $pay_item, $before);
    }

    public static function isNew($office_info)
    {
        return $office_info['IsNew'];
    }

    
    /********************** 是否有付费权限 **********************/
    public static function ifDisHasPayPermission()
    {
        //// Dis总是有付费权限
        return true;
    }

    public static function ifInsHasPayPermission($project_uuid)
    {
        $charge_info = getAccountChargeModeByUUID($project_uuid);
        $ins_mode = $charge_info['InstallerMode'];

        // ins 查看PayType字段
        if(EmailNotifyRule::IfHasPayPermission($ins_mode)) {
            return true;
        }

        return false;
    }

    public static function ifPMHasPayPermission($project_uuid)
    {
        $charge_info = getAccountChargeModeByUUID($project_uuid);
        $ins_mode = $charge_info['InstallerMode'];
        $pm_mode = $charge_info['ProjectMode'];

        // Ins和PM不一样的时候，视为没有权限（ins修改了支付方式 项目要保留旧的值，代表没有支付权限）
        if($ins_mode != $pm_mode){
            return false;
        }

        // pm 查看项目的设置
        if(EmailNotifyRule::IfHasPayPermission($pm_mode)) {
            return true;
        }

        return false;
    }
}

class PersonalCheck
{
    public static function needNotifyEnduser($pay_item, $charge_mode, $before)
    {
        $pm_mode = $charge_mode["ProjectMode"];
        $insmode = $charge_mode["InstallerMode"];
        $send_type = $charge_mode["SendExpireEmailType"];
        $subdis_mode = EmailNotifyRule::PAY_MODE_NORMAL;
        return EmailNotifyRule::IfNeedNotifyEnduser(PROJECT_TYPE_PERSONAL, $subdis_mode, $insmode, $pm_mode, $send_type, $pay_item, $before);
    }

    public static function needNotifyPm($pay_item, $charge_mode, $before)
    {
        $pm_mode = $charge_mode["ProjectMode"];
        $insmode = $charge_mode["InstallerMode"];
        $send_type = $charge_mode["SendExpireEmailType"];
        $subdis_mode = EmailNotifyRule::PAY_MODE_NORMAL;
        return EmailNotifyRule::IfNeedNotifyPm(PROJECT_TYPE_PERSONAL, $subdis_mode, $insmode, $pm_mode, $send_type, $pay_item, $before);
    }

    public static function needNotifyIns($pay_item, $charge_mode, $before)
    {
        $pm_mode = $charge_mode["ProjectMode"];
        $insmode = $charge_mode["InstallerMode"];
        $send_type = $charge_mode["SendExpireEmailType"];
        $subdis_mode = EmailNotifyRule::PAY_MODE_NORMAL;
        return EmailNotifyRule::IfNeedNotifyIns(PROJECT_TYPE_PERSONAL, $subdis_mode, $insmode, $pm_mode, $send_type, $pay_item, $before);
    }

    public static function needNotifyDis($pay_item, $charge_mode, $before)
    {
        $pm_mode = $charge_mode["ProjectMode"];
        $insmode = $charge_mode["InstallerMode"];
        $send_type = $charge_mode["SendExpireEmailType"];
        $subdis_mode = EmailNotifyRule::PAY_MODE_NORMAL;
        return EmailNotifyRule::IfNeedNotifyDis(PROJECT_TYPE_PERSONAL, $subdis_mode, $insmode, $pm_mode, $send_type, $pay_item, $before);
    }

    /********************** 是否有付费权限 **********************/
    public static function ifDisHasPayPermission($dis_uuid)
    {
        //// Dis总是有付费权限
        return true;
    }

    public static function ifInsHasPayPermission($install_uuid)
    {
        $charge_info = getPersonalChargeModeByInsUUID($install_uuid);
        $ins_mode = $charge_info['InstallerMode'];

        // ins 查看PayType字段
        if(EmailNotifyRule::IfHasPayPermission($ins_mode)) {
            return true;
        }

        return false;
    }
    public static function ifSubDisHasPayPermission($sub_dis_info)
    {      
        if($sub_dis_info['ChargeMode'] == EmailNotifyRule::PAY_MODE_NO_PERMISSION)
        {
            return false;
        }
        return true;
    }
}

class CommunityCheck
{
    public static function  needNotifyEndUser($pay_item, $charge_mode, $before)
    {
        $pm_mode = $charge_mode["ProjectMode"];
        $insmode = $charge_mode["InstallerMode"];
        $send_type = $charge_mode["SendExpireEmailType"];
        $subdis_mode = EmailNotifyRule::PAY_MODE_NORMAL;
        return EmailNotifyRule::IfNeedNotifyEnduser(PROJECT_TYPE_RESIDENCE, $subdis_mode, $insmode, $pm_mode, $send_type, $pay_item, $before);
    }

    public static function needNotifyPm($pay_item, $charge_mode, $before)
    {
        $pm_mode = $charge_mode["ProjectMode"];
        $insmode = $charge_mode["InstallerMode"];
        $send_type = $charge_mode["SendExpireEmailType"];
        $subdis_mode = EmailNotifyRule::PAY_MODE_NORMAL;
        return EmailNotifyRule::IfNeedNotifyPm(PROJECT_TYPE_RESIDENCE, $subdis_mode, $insmode, $pm_mode, $send_type, $pay_item, $before);
    }

    public static function needNotifyIns($pay_item, $charge_mode, $before)
    {
        $pm_mode = $charge_mode["ProjectMode"];
        $insmode = $charge_mode["InstallerMode"];
        $send_type = $charge_mode["SendExpireEmailType"];
        $subdis_mode = EmailNotifyRule::PAY_MODE_NORMAL;
        return EmailNotifyRule::IfNeedNotifyIns(PROJECT_TYPE_RESIDENCE, $subdis_mode, $insmode, $pm_mode, $send_type, $pay_item, $before);
    }

    public static function needNotifyDis($pay_item, $charge_mode, $before)
    {
        $pm_mode = $charge_mode["ProjectMode"];
        $insmode = $charge_mode["InstallerMode"];
        $send_type = $charge_mode["SendExpireEmailType"];
        $subdis_mode = EmailNotifyRule::PAY_MODE_NORMAL;
        return EmailNotifyRule::IfNeedNotifyDis(PROJECT_TYPE_RESIDENCE, $subdis_mode, $insmode, $pm_mode, $send_type, $pay_item, $before);
    }

    /********************** 是否有付费权限 **********************/
    public static function ifDisHasPayPermission()
    {
        //// Dis总是有付费权限
        return true;
    }

    public static function ifSubDisHasPayPermission($sub_dis_info)
    {      
        if($sub_dis_info['ChargeMode'] == EmailNotifyRule::PAY_MODE_NO_PERMISSION)
        {
            return false;
        }
        return true;
    }

    public static function ifInsHasPayPermission($project_uuid)
    {
        $charge_info = getAccountChargeModeByUUID($project_uuid);
        $ins_mode = $charge_info['InstallerMode'];

        // ins 查看PayType字段
        if(EmailNotifyRule::IfHasPayPermission($ins_mode)) {
            return true;
        }

        return false;
    }

    public static function ifPMHasPayPermission($project_uuid)
    {
        $charge_info = getAccountChargeModeByUUID($project_uuid);
        $ins_mode = $charge_info['InstallerMode'];
        $pm_mode = $charge_info['ProjectMode'];

        // Ins和PM不一样的时候，视为没有权限（ins修改了支付方式 项目要保留旧的值，代表没有支付权限）
        if($ins_mode != $pm_mode){
            return false;
        }

        // pm 查看项目的设置
        if(EmailNotifyRule::IfHasPayPermission($pm_mode)) {
            return true;
        }

        return false;
    }
}
