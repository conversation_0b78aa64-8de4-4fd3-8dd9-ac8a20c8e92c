<?php

require_once(dirname(__FILE__).'/az_check_update_access_commmon.php');

$medooDb = getMedooDb();
$db = $medooDb->pdo;

//更新公维金生效/到期的用户权限组 默认只检测当天的
function UpdateUserAccess($before = 0)
{
    UpdateEffectUserAccess($before); //更新公维金起始生效的用户权限组
    UpdateExpireUserAccess($before); //更新公维金过期的用户权限组
}

//更新公维金起始生效用户的权限组
function UpdateEffectUserAccess($before)
{
    $effect_room_list = GetEffectRoomList($before);
    foreach($effect_room_list as $effect_room)
    {
        $account_uuid = $effect_room['PersonalAccountUUID']; //家庭主账号uuid
        $master_account = GetCommMasterAccountByUUID($account_uuid);
        //根据对应楼栋获取公维金绑定权限组列表
        $access_group_list = GetAccessGroupList($master_account['UnitID']);
        if(!$access_group_list)
        {
            LOG_INFO("empty access group list, no need to update");
            continue;
        }
        //根据主账号获取家庭下账号列表
        $account_list = GetAccountList($account_uuid);
        $project_info = GetCommInfo($effect_room['ProjectUUID']);
        foreach($account_list as $account)
        {
            $user_account = $account['Account'];
            foreach($access_group_list as $access_group)
            {
                $access_group_id = $access_group['AccessGroupID'];
                AddUserAccessGroup($access_group_id, $user_account);
            }
            //新增用户权限组 下发配置无需ag_id 传0即可
            AzSendAccountAccessUpdateNotify($user_account, $project_info['ID'], UPDATE_TYPE_ADD, 0);
        }
    }
}
//更新公维金过期用户的权限组
function UpdateExpireUserAccess($before)
{
    $effect_room_list = GetExpireRoomList($before);
    foreach($effect_room_list as $effect_room)
    {
        $account_uuid = $effect_room['PersonalAccountUUID']; //家庭主账号uuid
        $master_account = GetCommMasterAccountByUUID($account_uuid);
        //根据对应楼栋获取公维金绑定权限组列表
        $access_group_list = GetAccessGroupList($master_account['UnitID']);
        if(!$access_group_list)
        {
            LOG_INFO("empty access group list, no need to update");
            continue;
        }
        //根据主账号获取家庭下账号列表
        $account_list = GetAccountList($account_uuid);
        $project_info = GetCommInfo($effect_room['ProjectUUID']);
        foreach($account_list as $account)
        {
            $user_account = $account['Account'];
            foreach($access_group_list as $access_group)
            {
                $access_group_id = $access_group['AccessGroupID'];
                RemoveUserAccessGroup($access_group_id, $user_account);
                AzSendAccountAccessUpdateNotify($user_account, $project_info['ID'], UPDATE_TYPE_DEL, $access_group_id);
            }
        }
    }
}
