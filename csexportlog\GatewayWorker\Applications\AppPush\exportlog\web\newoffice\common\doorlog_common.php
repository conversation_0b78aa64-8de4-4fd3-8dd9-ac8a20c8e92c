<?php
require_once(__DIR__ . '/../define.php');
require_once(__DIR__ . '/../../../common/define.php');
require_once(__DIR__ . '/../../../common/comm.php');
require_once(__DIR__ . '/../../../common/log_db_comm.php');

/**
 *  @brief 构造csv记录行
 */
function CreateDoorLogNewOffice($rootdir, $excel_file, $data_arr, $content, $s3_cfg)
{
    global $MSGTEXT_FRONT;
    global $MSGTEXT_BACK;
    if (!is_dir($rootdir)) {
        mkdir($rootdir, 0777, true);
    }

    $timeZone = $content["time_zone"];
    $export_type = $content["export_type"];
    $customizeForm = $content["time_form"];
    $project_type = $content["project_type"];
    $operator_type = $content["operator_type"];
    $CaptureTypes = [
        "0" => $MSGTEXT_BACK["call"],
        "1" => $MSGTEXT_BACK["unlockTempKey"],
        "2" => $MSGTEXT_BACK["unlockPrivateKey"],
        "3" => $MSGTEXT_BACK["unlockCard"],
        "4" => $MSGTEXT_BACK["unlockFACE"],
        "5" => $MSGTEXT_BACK["unlockApp"],
        "6" => $MSGTEXT_BACK["unlockApp"],
        "7" => $MSGTEXT_BACK["unlockIndoor"],
        "9" => $MSGTEXT_BACK["auditCodeManuallyUnlock"],
        "10" => $MSGTEXT_BACK["automaticallyUnlock"],
        "11" => $MSGTEXT_FRONT["DeliverySystemVerification"],
        "12" => $MSGTEXT_BACK["unlockBooking"],
        "13" => $MSGTEXT_BACK["unlockIDAccess"],
        "14" => $MSGTEXT_BACK["unlockExitButton"],
        "15" => $MSGTEXT_BACK["unlockHandset"],
        "16" => $MSGTEXT_BACK["unlockLicensePlate"],
        "99" => "",
        "100" => $MSGTEXT_BACK["unlockNFC"],
        "101" => $MSGTEXT_BACK["unlockBLE"],
        "102" => $MSGTEXT_BACK["captureSmartPlus"],
        "103" => $MSGTEXT_BACK["call"],
        // "104" => $MSGTEXT_BACK["ProperAllTextTemperatureLogs"],   104是测温记录，不需要
        "105" => $MSGTEXT_BACK["auditCodeManuallyLock"]
    ];

    if ($operator_type == OPERATOR_TYPE_PM) {
        $excel_header = [
            $MSGTEXT_FRONT["CaptureListTanleCaptureTime"],
            $MSGTEXT_FRONT["SelectDoor"],
            $MSGTEXT_FRONT["CaptureListTanleInitiator"],
            $MSGTEXT_FRONT["TabelUserInHtmlCompany"],
            $MSGTEXT_FRONT["LogInHtmlType"],
            $MSGTEXT_FRONT["CaptureListTanleAction"],
            $MSGTEXT_FRONT["TmpKeyListTanleKey"],
            $MSGTEXT_FRONT["Response"],
            $MSGTEXT_FRONT["capture"]
        ];
    } else{
        $excel_header = [
            $MSGTEXT_FRONT["CaptureListTanleCaptureTime"],
            $MSGTEXT_FRONT["SelectDoor"],
            $MSGTEXT_FRONT["CaptureListTanleInitiator"],
            $MSGTEXT_FRONT["LogInHtmlType"],
            $MSGTEXT_FRONT["CaptureListTanleAction"],
            $MSGTEXT_FRONT["TmpKeyListTanleKey"],
            $MSGTEXT_FRONT["Response"],
            $MSGTEXT_FRONT["capture"]
        ];
    }

    $index = 0;
    $excel_data = [];
    array_push($excel_data, $excel_header);
    foreach ($data_arr as $key => $val) {
        $index = $index + 1;
        $time = $val['CaptureTime'];
        $file_time = date("YmdHis", strtotime($time));
        $data_dir = date("Ymd", strtotime(explode(" ", $time)[0]));
        $time = \util\time\setTimeZone($time, $timeZone, $customizeForm);

        $name = $val["Location"];
        $name = str_replace("/", "", $name);
        $name = str_replace("\\", "", $name);
        $csvname = str_replace(",", "-", $val["Location"]);

        $pic = "";
        if (strlen($val["PicUrl"]) > 0)
        {
            $pic = $file_time."+".$val["ID"]."+$name.jpg";
        }

        $resp = $val["Response"] == 0 ? $MSGTEXT_BACK["success"] : $MSGTEXT_BACK["failed"];

        // TODO: AccessMode对应的词条未更新
        if ($val["CaptureType"] == 103) {
            $logtype = $MSGTEXT_BACK["call"];
            $resp = "--";
        } else {
            $mode = $val["AccessMode"];
            if ($mode == 1) {
                $logtype = $MSGTEXT_BACK["entry"];
            } else if ($mode == 2) {
                $logtype = $MSGTEXT_BACK["exit"];
            } else if ($mode == 3) {
                $logtype = $MSGTEXT_BACK["entryViolation"];
            } else if ($mode == 4) {
                $logtype = $MSGTEXT_BACK["exitViolation"];
            } else {
                $logtype = $MSGTEXT_BACK["doorRelease"];
            }
        }
        $action = $CaptureTypes[$val["CaptureType"]];
        $passwd = $val["KeyNum"];
        if ($val["CaptureType"] == 2 || $val['CaptureType'] == 13) {
            $passwd = "****";
        }

        // 获取Door列表名称
        $door_name = getDoorNameList($val);

        $company_name = "";
        if(isset($val['CompanyName'])) {
            $company_name = $val["CompanyName"];
        }

        if($operator_type == OPERATOR_TYPE_PM){
            array_push($excel_data, array(
                $time,
                $door_name,
                $val["Initiator"],
                $company_name,
                $logtype,
                $action,
                $passwd,
                $resp,
                $pic
            ));
        } else {
            array_push($excel_data, array(
                $time,
                $door_name,
                $val["Initiator"],
                $logtype,
                $action,
                $passwd,
                $resp,
                $pic
            ));
        }
        
        // 若为导出csv+图片，则下载并保存图片到本地
        if ($export_type == EXPORTLOG_TYPE_ALL) {
            $pic_dir = $rootdir . "/" . $data_dir;
            if (!is_dir($pic_dir)) {
                mkdir($pic_dir, 0777, true);
            }
            DownloadPic($s3_cfg, $val["PicUrl"], $pic, $pic_dir, $val['CaptureTime']);

            if ($index % PER_DOWNLOAD_NUMBER_TO_SLEEP == 0) {
                sleep(PER_DOWNLOAD_NUMBER_SLEEP_SEC);
            }
        }
    }
    
    LOG_INFO("Newoffice export door log: file=$rootdir/$excel_file, count=" . count($excel_data) - 1);
    createExcelFormData($excel_data, $rootdir, $excel_file);
    $excel_data = null;
}

function getDoorNameList($val)
{
    $name = $val["Location"];
    $name = str_replace("/", "", $name);
    $name = str_replace("\\", "", $name);
    
    $door_name = "";
    if (strlen($val["DoorNameList"]) > 0) {
        $door_name = $name . " - " . $val["DoorNameList"];
    } else {
        // 解析和拼接 relay_name
        $relay_name = "";
        $relay_info = array();
        $relay_bit = $val['Relay'];
        if (isset($val['RelayInfo'])) {
            $relay_info = json_decode($val['RelayInfo'], true);
        }
        
        for ($relay_index = 0; is_array($relay_info) && $relay_index < count($relay_info) && $relay_index < 4; $relay_index++) {
            if (((1 << $relay_index)  & $relay_bit) != 0 && isset($relay_info[$relay_index]['name'])) {
                $relay_name  = $relay_name  . $relay_info[$relay_index]['name'] . ",";
            }
        }

        // 拼接 security_relay_name
        $security_relay_info = array();
        $security_relay_bit = $val['SecurityRelay'];
        if (isset($val['SecurityRelayInfo'])) {
            $security_relay_info = json_decode($val['SecurityRelayInfo'], true);
        }

        for ($relay_index = 0; is_array($security_relay_info) && $relay_index < count($security_relay_info) && $relay_index < 4; $relay_index++) {
            if (((1 << $relay_index)  & $security_relay_bit) != 0 && isset($security_relay_info[$relay_index]['name'])) {
                $relay_name  = $relay_name  . $security_relay_info[$relay_index]['name'] . ",";
            }
        }

        $door_name = $name . " - " . $relay_name;   // 拼接Doorname = DeviceName - RelayName
        $door_name = rtrim($door_name, ',- ');      // 去掉末尾的 逗号、短线、空格
    }

    // 如果拼接后的door_name为空，则使用Location
    if (strlen($door_name) == 0) {
        $door_name = $val["Location"];
    }

    return $door_name;
}

function NewofficeExportDoorLogCommon($datas, $s3_cfg, $sql_datas)
{
    $today = date("Ymd");
    $lang = $datas["language"];
    $trace_id = $datas["trace_id"];
    $timeZone = $datas["time_zone"];
    $endTime = $datas["export_endtime"];
    $export_type = $datas["export_type"];
    $record_uuid = $datas["record_uuid"];
    $customizeForm = $datas["time_form"];
    $startTime = $datas["export_startime"];
    $project_uuid = $datas["project_uuid"];
    $operator_type = $datas["operator_type"];

    global $MSGTEXT_FRONT;
    reload_language($lang);

    // 从AKCS数据库和LOG数据库 获取分表表名
    $log_tables = [];
    getSqlTables("PersonalCapture", $project_uuid, $log_tables);


    $index = 0;
    // 生成日志文件并上传到OSS并返回URL
    $file_urls = [];
    foreach ($sql_datas as $key => $value) {
        $file_start_time = \util\time\setTimeZone($startTime, $timeZone, "", "+");
        $file_end_time = \util\time\setTimeZone($endTime, $timeZone, "", "+");
        $file_start_time = explode(" ", $file_start_time)[0];
        $file_end_time = explode(" ", $file_end_time)[0];

        $file_start_time = str_replace("-", "", $file_start_time);
        $file_end_time = str_replace("-", "", $file_end_time);

        $index = $index + 1;
        $dir = getLogDir($trace_id, $MSGTEXT_FRONT["ProperAllTextDoorLogs"], $index, $file_start_time, $file_end_time);
        $zip_save_path = $dir[FILE_ZIP_PATH];
        $csvpath = $dir[FILE_CSV_PATH];
        $zipname = $dir[FILE_ZIP_NAME];
        $csvname = $dir[FILE_CSV_NAME];
        $savedir = $dir[FILE_SAVE_DIR];
        CreateDoorLogNewOffice($savedir, $csvname, $value, $datas, $s3_cfg);    // 导出数据并生成csv文件

        //压缩文件夹
        if ($operator_type == OPERATOR_TYPE_PM)
        {
            if ($export_type == EXPORTLOG_TYPE_ALL) {
                zipDir($zip_save_path, $dir[FILE_ROOT], $dir[FILE_ZIP_DIR]);
                $oss_url = upload_oss($s3_cfg, $zip_save_path, "PmExportLog/$today/doorlogs/$trace_id/$zipname");
                $url = [];
                $url[$zipname] = $oss_url;
                array_push($file_urls, $url);
            } else {
                $oss_url = upload_oss($s3_cfg, $csvpath, "PmExportLog/$today/doorlogs/$trace_id/$csvname");
                updateExportLogDownloadUrl("PmExportLogRecord", $record_uuid, $oss_url);
                break;
            }
        }
        else if ($operator_type == OPERATOR_TYPE_ADMIN)
        {
            if ($export_type == EXPORTLOG_TYPE_ALL) {
                zipDir($zip_save_path, $dir[FILE_ROOT], $dir[FILE_ZIP_DIR]);
                $oss_url = upload_oss($s3_cfg, $zip_save_path, "AdminExportLog/$today/doorlogs/$trace_id/$zipname");
                $url = [];
                $url[$zipname] = $oss_url;
                array_push($file_urls, $url);
            } else {
                $oss_url = upload_oss($s3_cfg, $csvpath, "AdminExportLog/$today/doorlogs/$trace_id/$csvname");
                updateExportLogDownloadUrl("AdminExportLogRecord", $record_uuid, $oss_url);
                break;
            }
        }
    }

    $retinfo["urls"] = $file_urls;
    $retinfo["result"] = 0;
    $retinfo["community"] = GetProjectName($project_uuid);
    LOG_INFO("Newoffice export door log: data line number=" . count($sql_datas));
    $sql_datas = null;
    return $retinfo;
}
