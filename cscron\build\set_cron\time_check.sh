#!/bin/bash

# 时间检查工具脚本
# 提供时间段检查功能

# 检查时间段函数
# 参数1: 开始小时 (0-23)
# 参数2: 结束小时 (0-23)
# 如果当前时间在指定时间段内，则退出脚本
CheckTimeRange() {
    local start_hour=$1
    local end_hour=$2
    local current_hour=$(date +%H)
    
    # 去掉小时数前面的0，避免被当作八进制处理
    current_hour=$((10#$current_hour))
    
    echo "当前时间: $(date '+%Y-%m-%d %H:%M:%S'), 当前小时: $current_hour"
    echo "检查时间段: ${start_hour}点到${end_hour}点"
    
    # 处理跨天情况（例如23点到5点）
    if [ $start_hour -le $end_hour ]; then
        # 不跨天的情况
        if [ $current_hour -ge $start_hour ] && [ $current_hour -lt $end_hour ]; then
            echo "当前时间在维护时间段内 (${start_hour}:00-${end_hour}:00)，脚本退出"
            exit 1
        fi
    else
        # 跨天的情况
        if [ $current_hour -ge $start_hour ] || [ $current_hour -lt $end_hour ]; then
            echo "当前时间在维护时间段内 (${start_hour}:00-${end_hour}:00)，脚本退出"
            exit 1
        fi
    fi
    
    echo "当前时间不在维护时间段内，继续执行脚本"
}