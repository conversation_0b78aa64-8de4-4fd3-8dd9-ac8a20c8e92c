<?php
/*
 * @Description: 操作用户
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors  : kxl
 */
date_default_timezone_set("PRC");
include_once __DIR__."/util/time.php";
require_once(__DIR__.'/../common/define.php');
require_once(__DIR__.'/../common/comm.php');
require_once(__DIR__.'/../common/log_db_comm.php');

function CreateDoorLog($rootdir, $excel_file, $data_arr, $content, $s3_cfg)
{
    global $MSGTEXT_FRONT;
    global $MSGTEXT_BACK;
    $timeZone = $content["time_zone"];
    $customizeForm = $content["time_form"];
    $export_type = $content["export_type"];
    $project_type = $content["project_type"];
    if (!is_dir($rootdir)) {
        mkdir($rootdir, 0777, true);
    }

    $CaptureTypes = [
        "0" => $MSGTEXT_BACK["call"],
        "1" => $MSGTEXT_BACK["unlockTempKey"],
        "2" => $MSGTEXT_BACK["unlockPrivateKey"],
        "3" => $MSGTEXT_BACK["unlockCard"],
        "4" => $MSGTEXT_BACK["unlockFACE"],
        "5" => $MSGTEXT_BACK["unlockApp"],
        "6" => $MSGTEXT_BACK["unlockApp"],
        "7" => $MSGTEXT_BACK["unlockIndoor"],
        "9" => $MSGTEXT_BACK["auditCodeManuallyUnlock"],
        "10" => $MSGTEXT_BACK["automaticallyUnlock"],
        "11" => $MSGTEXT_FRONT["DeliverySystemVerification"],
        "12" => $MSGTEXT_BACK["unlockBooking"],
        "13" => $MSGTEXT_BACK["unlockIDAccess"],
        "14" => $MSGTEXT_BACK["unlockExitButton"],
        "15" => $MSGTEXT_BACK["unlockHandset"],
        "16" => $MSGTEXT_BACK["unlockLicensePlate"],
        "99" => "",
        "100" => $MSGTEXT_BACK["unlockNFC"],
        "101" => $MSGTEXT_BACK["unlockBLE"],
        "102" => $MSGTEXT_BACK["captureSmartPlus"],
        "103" => $MSGTEXT_BACK["call"],
        // "104" => $MSGTEXT_BACK["ProperAllTextTemperatureLogs"],   104是测温记录，不需要
        "105" => $MSGTEXT_BACK["auditCodeManuallyLock"]
    ];

    $excel_data = [];
    $excel_header = [];
    if ($project_type) {
        $excel_header = [
            $MSGTEXT_FRONT["CaptureListTanleCaptureTime"],
            $MSGTEXT_FRONT["ProperAllTextDepartment"],
            $MSGTEXT_FRONT["DeviceLocation"],
            $MSGTEXT_FRONT["CaptureListTanleInitiator"],
            $MSGTEXT_FRONT["LogInHtmlType"],
            $MSGTEXT_FRONT["CaptureListTanleAction"] ,
            $MSGTEXT_FRONT["TmpKeyListTanleKey"],
            $MSGTEXT_FRONT["Response"] ,
            $MSGTEXT_FRONT["capture"]
        ];
    } else {
        $excel_header = [
            $MSGTEXT_FRONT["CaptureListTanleCaptureTime"],
            $MSGTEXT_FRONT["DeviceDetailDetailUnitName"],
            $MSGTEXT_FRONT["DeviceLocation"],
            $MSGTEXT_FRONT["CaptureListTanleInitiator"],
            $MSGTEXT_FRONT["RDeviceSearchLabelRoomName"],
            $MSGTEXT_FRONT["LogInHtmlType"] ,
            $MSGTEXT_FRONT["CaptureListTanleAction"],
            $MSGTEXT_FRONT["TmpKeyListTanleKey"] ,
            $MSGTEXT_FRONT["Response"],
            $MSGTEXT_FRONT["capture"]
        ];
    }
    
    $index = 0;
    array_push($excel_data, $excel_header);
    foreach ($data_arr as $key => $val) {
        $index = $index + 1;
        $time = $val['CaptureTime'];
        $file_time = date("YmdHis", strtotime($time));
        $data_dir = date("Ymd", strtotime(explode(" ", $time)[0]));
        
        $time = \util\time\setTimeZone($time, $timeZone, $customizeForm);

        $name = $val["Location"];
        $name = str_replace("/", "", $name);
        $name = str_replace("\\", "", $name);
        $csvname = str_replace(",", "-", $val["Location"]);

        $pic = "";
        if (strlen($val["PicUrl"]) > 0)
        {
            $pic = $file_time."+".$val["ID"]."+$name.jpg";
        }

        $resp = $val["Response"] == 0 ? $MSGTEXT_BACK["success"] : $MSGTEXT_BACK["failed"];
        switch($val["CaptureType"])
        {
            case 103:
                // $logtype = $MSGTEXT_BACK["call"];
                // $resp = "--"; //特殊处理
                continue;
            default:
            {
                $logtype = $MSGTEXT_BACK["doorRelease"];
            }

        }
        $action = $CaptureTypes[$val["CaptureType"]];
        $passwd = $val["KeyNum"];
        if ($val["CaptureType"] == 2 || $val['CaptureType'] == 13) {
            $passwd = "****";
        }
        
        if ($project_type) {
            array_push($excel_data, array(
                $time,
                $val["UnitName"],
                $csvname,
                $val["Initiator"],
                $logtype,
                $action,
                $passwd,
                $resp,
                $pic
            ));
        } else {
            array_push($excel_data, array(
                $time,
                $val["UnitName"],
                $csvname,
                $val["Initiator"],
                $val["RoomNum"],
                $logtype,
                $action,
                $passwd,
                $resp,
                $pic
            ));
        }
        
        // 下载图片
        if ($export_type == EXPORTLOG_TYPE_ALL) {
            $pic_dir = $rootdir."/".$data_dir;
            if (!is_dir($pic_dir)) {
                mkdir($pic_dir, 0777, true);
            }
            DownloadPic($s3_cfg, $val["PicUrl"], $pic, $pic_dir, $val['CaptureTime']);

            if ($index % PER_DOWNLOAD_NUMBER_TO_SLEEP == 0) {
                sleep(PER_DOWNLOAD_NUMBER_SLEEP_SEC);
            }
        }
    }

    LOG_INFO("Export door log: file=$rootdir/$excel_file, count=" . count($excel_data) - 1);
    createExcelFormData($excel_data, $rootdir, $excel_file);
    unset($excel_data);
}

function getTableDoorlogDatas($log_tables, $datas, $limit)
{
    $left = $limit;
    $result = [];
    $akcs_result = [];

    if ($log_tables != null) {
        $db_log_data = getLOGTableDoorlogDatas($log_tables, $datas, $left, $result);
        $result = array_merge($result, $db_log_data);
    }
    
    return $result;
}

function getLOGTableDoorlogDatas($log_tables, $datas, &$left, $result)
{
    $dbNode = getLogDbNode($datas['project_uuid']);
    $db = getLogDB($dbNode);
    $count = 0;
    foreach ($log_tables as $table) {
        if (!LOGDBTableExist($table)) {
            continue;
        }
        if ($left <= 0) {
            LOG_WARING("1.export LOG.PersonalCapture cloumn limit exceeded.");
            break;
        }

        //查询出token是否存在
        $one_table = getLOGDBDoorlogDatas($db, $table, $count, $left, $result, $datas);
        $result = array_merge($result, $one_table);
    }
    if ($left <= 0) {
        LOG_WARING("2.export LOG.PersonalCapture cloumn limit exceeded.");#不考虑刚好就是5000张得零界点
    }

    $db = null;
    return $result;
}

function getAKCSTableDoorlogDatas($akcs_tables, $datas, &$left, $result)
{
    $db = getDB();
    $count = 0;
    foreach ($akcs_tables as $table) {
        if (!logTableExist($table)) {
            continue;
        }
        if ($left <= 0) {
            LOG_WARING("1.export AKCS.PersonalCapture cloumn limit exceeded.");
            break;
        }

        //查询出token是否存在
        $one_table = getDoorlogDatas($db, $table, $count, $left, $result, $datas);
        $result = array_merge($result, $one_table);
    }
    if ($left <= 0) {
        LOG_WARING("2.export AKCS.PersonalCapture cloumn limit exceeded.");#不考虑刚好就是5000张得零界点
    }
    
    $db = null;
    return $result;
}

function getDoorlogDatas($db, $table, &$count, &$left, $result, $datas)
{
    //查询出token是否存在
    $sth = $db->prepare("select C.*,B.UnitName,D.Location from $table C left join Devices D on C.MAC = D.MAC left join CommunityUnit B on B.ID = D.UnitID where C.MngAccountID = :MngAccountID and C.CaptureTime >= :StartTime  and  C.CaptureTime <= :EndTime  and C.CaptureType < 102 order by ID limit $left");
    $sth->bindParam(':MngAccountID', $datas["communit_id"], PDO::PARAM_STR);
    $sth->bindParam(':StartTime', $datas["export_startime"], PDO::PARAM_STR);
    $sth->bindParam(':EndTime', $datas["export_endtime"], PDO::PARAM_STR);

    $ret = $sth->execute();
    $data = $sth->fetchALL(PDO::FETCH_ASSOC);
    $count = $count + count($data);
    $left = $left - $count;
    return $data;
}

function getLOGDBDoorlogDatas($db, $table, &$count, &$left, $result, $datas)
{
    $no_call_in_doorlog = $datas["no_call_in_doorlog"];
    //capture type = 103代表call截图
    $capture_type_query_str = " (CaptureType < 102 or CaptureType = 103) ";

    if ($no_call_in_doorlog)
    {
        $capture_type_query_str = " CaptureType < 102 ";
    }

    // 1.查找开门记录 （注意：这里是从LOG数据库查找的）
    $sth = $db->prepare("select " . PERSONAL_CAPTURE_KEY . " from $table where MngAccountID = :MngAccountID and CaptureTime >= :StartTime and  CaptureTime <= :EndTime  and $capture_type_query_str order by ID desc limit $left");
    $sth->bindParam(':MngAccountID', $datas["communit_id"], PDO::PARAM_STR);
    $sth->bindParam(':StartTime', $datas["export_startime"], PDO::PARAM_STR);
    $sth->bindParam(':EndTime', $datas["export_endtime"], PDO::PARAM_STR);
    $ret = $sth->execute();
    $data = $sth->fetchALL(PDO::FETCH_ASSOC);

    // 2.查找设备信息 （注意：这里是从AKCS数据库查找的）
    $akcs_db = getDB();
    $sth = $akcs_db->prepare("select D.MAC,B.UnitName,D.Location,D.Relay as RelayIndex,D.SecurityRelay as SecurityRelayIndex from Devices D left join CommunityUnit B on B.ID = D.UnitID where D.MngAccountID = :MngAccountID");
    $sth->bindParam(':MngAccountID', $datas["communit_id"], PDO::PARAM_STR);
    $sth->execute();
    $device_info = $sth->fetchALL(PDO::FETCH_ASSOC);

    // 3. 按照MAC合并数组
    $data = MergeArraysByCustomKey($data, $device_info, "MAC");
    // 处理relay获取名字
    foreach ($data as $key => $value) {
        $data[$key]['Relay'] = processRelayInfo($value['RelayIndex'], $value['Relay']);
        $data[$key]['SecurityRelay'] = processRelayInfo($value['SecurityRelayIndex'], $value['SecurityRelay']);
        
        // 用连字符拼接 Location、Relay 和 SecurityRelay
        $location = $value['Location'] ?? '';
        $relay = $data[$key]['Relay'];
        $securityRelay = $data[$key]['SecurityRelay'];
        
        $combinedInfo = $location;
        if (!empty($relay)) {
            $combinedInfo .= "-" . $relay;
        }
        if (!empty($securityRelay)) {
            $combinedInfo .= "," . $securityRelay;
        }
        
        $data[$key]['Location'] = $combinedInfo;
    }

    $count = $count + count($data);
    $left = $left - $count;
    return $data;
}

function processRelayInfo($relayJson, $relayNumber) {
    // 将JSON字符串转换为数组
    $relayArray = json_decode($relayJson, true);
    if (!$relayArray) {
        return '';
    }

    // 将数字转换为二进制字符串，并确保是4位
    $binary = str_pad(decbin($relayNumber), 4, '0', STR_PAD_LEFT);
    
    // 从右到左读取二进制位，对应relayArray的索引
    $result = [];
    for ($i = 0; $i < 4; $i++) {
        if ($binary[3 - $i] == '1' && isset($relayArray[$i])) {
            $result[] = $relayArray[$i]['name'];
        }
    }
    
    return implode(',', $result);
}

function ExportLogDoorLog($datas, $s3_cfg)
{
    $trace_id = $datas["trace_id"];
    $timeZone = $datas["time_zone"];
    $customizeForm = $datas["time_form"];
    $communit_id = $datas["communit_id"];
    $export_type = $datas["export_type"];
    $startTime = $datas["export_startime"];
    $endTime = $datas["export_endtime"];
    $lang = $datas["language"];
    $project_type = $datas["project_type"];
    $today = date("Ymd");
    reload_language($lang);
    global $MSGTEXT_FRONT;

    //$month = date("Ym", strtotime("-1 month"));
    //$tables = ["PersonalCapture_".$month, "PersonalCapture"];

    // 获取akcs和log分表表名
    $project_uuid = getProjectUUID($communit_id);
    $datas["project_uuid"] = $project_uuid;
    $log_tables = [];
    getSqlTables("PersonalCapture", $project_uuid, $log_tables);

    // 从每个分表中查找数据
    $index = 1;
    if ($export_type == EXPORTLOG_TYPE_EXCEL_ONLY) {
        $sql_data = getTableDoorlogDatas( $log_tables, $datas, DOWNLOAD_CSV_LIMIT);
        $sql_datas = array_chunk($sql_data, DOWNLOAD_CSV_LIMIT);
    } else {
        $sql_data = getTableDoorlogDatas($log_tables, $datas, DOWNLOAD_PIC_LIMIT);
        $sql_datas = array_chunk($sql_data, PER_DIR_PIC_LIMIT);
    }

    // 生成日志文件并上传到OSS并返回URL
    $file_urls = [];
    foreach ($sql_datas as $key => $value) {
        $file_start_time = \util\time\setTimeZone($startTime, $timeZone, "", "+");
        $file_end_time = \util\time\setTimeZone($endTime, $timeZone, "", "+");
        $file_start_time = explode(" ", $file_start_time)[0];
        $file_end_time = explode(" ", $file_end_time)[0];

        $file_start_time = str_replace("-", "", $file_start_time);
        $file_end_time = str_replace("-", "", $file_end_time);

        $dir = getLogDir($trace_id, $MSGTEXT_FRONT["ProperAllTextDoorLogs"], $index, $file_start_time, $file_end_time);
        $zip_save_path = $dir[FILE_ZIP_PATH];
        $csvpath = $dir[FILE_CSV_PATH];
        $zipname = $dir[FILE_ZIP_NAME];
        $csvname = $dir[FILE_CSV_NAME];
        $savedir = $dir[FILE_SAVE_DIR];
        CreateDoorLog($savedir, $csvname, $value, $datas, $s3_cfg); // 导出数据并生成csv文件

        //压缩文件夹
        $index = $index + 1;
        if ($export_type == EXPORTLOG_TYPE_ALL) {
            zipDir($zip_save_path, $dir[FILE_ROOT], $dir[FILE_ZIP_DIR]);
            $oss_url = upload_oss($s3_cfg, $zip_save_path, "PmExportLog/$today/doorlogs/$trace_id/$zipname");
            $url = [];
            $url[$zipname] = $oss_url;
            array_push($file_urls, $url);
        } else {
            $oss_url = upload_oss($s3_cfg, $csvpath, "PmExportLog/$today/doorlogs/$trace_id/$csvname");
            updateExcelUrl($trace_id, $oss_url);
            break;
        }
    }

    $sql_datas = null;
    $retinfo["urls"] = $file_urls;
    $retinfo["result"] = 0;
    return $retinfo;
}

