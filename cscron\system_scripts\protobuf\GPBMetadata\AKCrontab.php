<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: AK.Crontab.proto

namespace GPBMetadata;

class AKCrontab
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0aab0d0a10414b2e43726f6e7461622e70726f746f120a414b2e43726f6e" .
            "746162224d0a09417070457870697265120b0a0375696418012001280912" .
            "110a09757365725f6e616d65180220012809120d0a05656d61696c180320" .
            "01280912110a09636f6d6d756e69747918042001280922510a0d41707057" .
            "696c6c457870697265120b0a0375696418012001280912110a0975736572" .
            "5f6e616d65180220012809120d0a05656d61696c18032001280912110a09" .
            "636f6d6d756e69747918042001280922760a11504d41707057696c6c4265" .
            "45787069726512110a09636f6d6d756e697479180120012809120d0a0565" .
            "6d61696c180220012809120c0a046e616d6518032001280912130a0b6163" .
            "636f756e745f6e756d180420012805120c0a046c69737418052001280912" .
            "0e0a066265666f726518062001280522450a0b50686f6e65457870697265" .
            "120b0a03756964180120012809120c0a046e616d65180220012809120d0a" .
            "05656d61696c180320012809120c0a046d6f6465180420012805224b0a0f" .
            "50686f6e6557696c6c457870697265120b0a03756964180120012809120c" .
            "0a046e616d65180220012809120d0a05656d61696c180320012809120e0a" .
            "066265666f726518042001280522740a16496e7374616c6c657241707057" .
            "696c6c457870697265120c0a046e616d65180120012809120d0a05656d61" .
            "696c18022001280912100a086c6f636174696f6e180320012809120d0a05" .
            "636f756e74180420012805120c0a046c697374180520012809120e0a0662" .
            "65666f726518062001280522640a18496e7374616c6c657250686f6e6557" .
            "696c6c457870697265120c0a046e616d65180120012809120d0a05656d61" .
            "696c180220012809120d0a05636f756e74180320012805120c0a046c6973" .
            "74180420012809120e0a066265666f726518052001280522600a12496e73" .
            "74616c6c6572417070457870697265120c0a046e616d6518012001280912" .
            "0d0a05656d61696c18022001280912100a086c6f636174696f6e18032001" .
            "2809120d0a05636f756e74180420012805120c0a046c6973741805200128" .
            "0922500a14496e7374616c6c657250686f6e65457870697265120c0a046e" .
            "616d65180120012809120d0a05656d61696c180220012809120d0a05636f" .
            "756e74180320012805120c0a046c69737418042001280922540a13506d46" .
            "65617475726557696c6c457870697265120c0a046e616d65180120012809" .
            "120d0a05656d61696c18022001280912100a086c6f636174696f6e180320" .
            "012809120e0a066265666f7265180420012805225b0a1a496e7374616c6c" .
            "65724665617475726557696c6c457870697265120c0a046e616d65180120" .
            "012809120d0a05656d61696c18022001280912100a086c6f636174696f6e" .
            "180320012809120e0a066265666f726518042001280522640a1757656250" .
            "6572736f6e616c4d6f646966794e6f7469667912100a086d61635f6c6973" .
            "7418012003280912130a0b6368616e67655f7479706518022001280d120c" .
            "0a046e6f646518032001280912140a0c696e7374616c6c65725f69641804" .
            "2001280d22760a18576562436f6d6d756e6974794d6f646966794e6f7469" .
            "667912100a086d61635f6c69737418012003280912130a0b6368616e6765" .
            "5f7479706518022001280d120c0a046e6f646518032001280912140a0c63" .
            "6f6d6d756e6974795f696418042001280d120f0a07756e69745f69641805" .
            "2001280d227d0a18504d4170704163636f756e7457696c6c426545787069" .
            "726512110a09636f6d6d756e697479180120012809120d0a05656d61696c" .
            "180220012809120c0a046e616d6518032001280912130a0b6163636f756e" .
            "745f6e756d180420012805120c0a046c697374180520012809120e0a0662" .
            "65666f726518062001280522670a12504d4170704163636f756e74457870" .
            "69726512110a09636f6d6d756e697479180120012809120d0a05656d6169" .
            "6c180220012809120c0a046e616d6518032001280912130a0b6163636f75" .
            "6e745f6e756d180420012805120c0a046c69737418052001280922320a12" .
            "53656e64456d61696c4e6f746966794d7367120b0a036b65791801200128" .
            "09120f0a077061796c6f616418022001280922340a1453656e644d657373" .
            "6167654e6f746966794d7367120b0a036b6579180120012809120f0a0770" .
            "61796c6f616418022001280922620a1c43726f6e55736572416363657373" .
            "47726f75704e6f746966794d7367120f0a076163636f756e741801200128" .
            "09120c0a047479706518022001280d12140a0c636f6d6d756e6974795f69" .
            "6418032001280d120d0a0561675f696418042001280d620670726f746f33"
        ));

        static::$is_initialized = true;
    }
}

