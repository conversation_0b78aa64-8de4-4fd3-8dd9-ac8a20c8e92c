<?php
require_once(dirname(__FILE__) . '/../akcs_kafka.php');
require_once(dirname(__FILE__) . '/../common/utils.php');
require_once(dirname(__FILE__) . '/../common/define.php');
require_once(dirname(__FILE__) . '/../common/db_common.php');
require_once(dirname(__FILE__) . '/../common/email_common.php');
require_once(dirname(__FILE__) . '/../common/socket_common.php');
require_once(dirname(__FILE__) . '/../common/email_notify_rule.php');
require_once(dirname(__FILE__) . '/../protobuf/proto_crontab.php');
require_once(dirname(__FILE__) . '/../protobuf/proto_crontab_office.php');
require_once(dirname(__FILE__) . '/notify_check.php');
require_once(dirname(__FILE__) . '/check_autopay_common.php');
require_once(dirname(__FILE__) . '/medoo.php');


#迁移时候的dis列表，在新加坡服务器不能处理这些dis下的过期邮件
$jp_dis_array = array("wavedge","Link","Jnets","Glamo","IIJ","VALTEC","ODI","Moncable","DigitalPower","Daminn","JTS","DOORCOM","cool_jp","testfee_jp");
$au_dis_array = array("Advance-d","Alex","Becas","bela_dis","Fixtel","Freeway","HomeInc","inn","innihome","IOTWholesale","Lilin","LTS-AU","Middys","s2au_dis","SIETEC","Stentofon","tplus","Transtech");
$az_dis_array = array("OrkhanDist"); 
$eu_dis_array = array("Mauritius"); 

const OEM_NAME_AK = "Akuvox";
const SERVER_LOCATION = "na";

date_default_timezone_set('Asia/shanghai');
const LOG_TAG="ExpireCrontab";

//role
const ACCOUNT_ROLE_OFFICE_MAIN = 30;
const ACCOUNT_ROLE_OFFICE_ADMIN = 31;
const ACCOUNT_ROLE_OFFICE_NEW_PER = 32; //新办公Personnel
const ACCOUNT_ROLE_COMMUNITY_PM = 40;
const ACCOUNT_ROLE_PERSONNAL_MAIN = 10;        //个人终端用户主账号
const ACCOUNT_ROLE_PERSONNAL_ATTENDANT = 11;   //个人终端用户从账号
const ACCOUNT_ROLE_PERSONNAL_V_PUB = 12;       //个人虚拟账号，用于公共设备
const ACCOUNT_ROLE_COMMUNITY_MAIN = 20;        //社区用户主账号
const ACCOUNT_ROLE_COMMUNITY_ATTENDANT = 21;   //社区用户从账号
const COMMUNITY_SWITCH_SMARTHOME = 4;
const PERSONAL_FEATURE_PLAN_SWITCH = 1; //第二位

//办公新增

//office更新配置
const MSG_P2A_NOTIFY_OFFICE_INFO_UPDATE = MSG_P2A + 2000;
const MSG_P2A_NOTIFY_OFFICE_PM_ACCOUNT_WILL_EXPIRE = MSG_P2A + 2003;
const MSG_P2A_NOTIFY_OFFICE_PM_ACCOUNT_EXPIRE = MSG_P2A + 2009;
const MSG_P2A_NOTIFY_OFFICE_PM_FEATURE_WILL_EXPIRE = MSG_P2A + 2010;
const MSG_P2A_NOTIFY_OFFICE_INSTALLER_FEATURE_WILL_EXPIRE = MSG_P2A + 2011;
const MSG_P2A_NOTIFY_OFFICE_PM_FEATURE_EXPIRE = MSG_P2A + 2012;
const MSG_P2A_NOTIFY_OFFICE_INSTALLER_FEATURE_EXPIRE = MSG_P2A + 2013;

const WEB_PER_UPDATE_MAC_CONFIG = 1016;
const WEB_COMM_FEATURE_PLAN_RENEW = 5017;

//付费链接显示
const SHOW_PAYLINK_NONE = 0;
const SHOW_PAYLINK_PM = 1;
const SHOW_PAYLINK_INSTALLER = 2;
const SHOW_PAYLINK_DISTRIBUTOR = 3;

const WEB_DOMAIN = "dev.akuvox.com";

const SEND_TO_MYSELF = 1;
const SEND_TO_PM_OR_ENDUSER = 2;
const SEND_TO_PM = 3;

const PROJCET_RESIDENCE = 0;

function getMedooDb() {

    $dbhost = "127.0.0.1";
    $dbuser = "dbuser01";
    $dbpass = DB_PASSWORD;
    $dbname = "AKCS";
    $dbport = 3306;

    $database = new Medoo([
        'database_type' => 'mysql',
        'database_name' => $dbname,
        'server' => $dbhost,
        'username' => $dbuser,
        'password' => $dbpass,
        'charset' => 'utf8',
        'port' => $dbport,
    ]);
    return $database;
}

function GetServerTag()
{
    global $db;

    $sth = $db->prepare("select ServerTag from SystemSetting");
    $sth->execute();
    $sys_setting = $sth->fetch(PDO::FETCH_ASSOC);
    return $sys_setting['ServerTag'];
}

/*
App即将过期发送消息给平台
 */
class CAppWillBeExpireSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\AppExpire();
            $TempData->setUid((string)$data[0]);
            $TempData->setUserName((string)$data[1]);
            $TempData->setEmail((string)$data[2]);
            $TempData->setCommunity((string)$data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/*
有住户即将过期发送消息给pm
 */
class CPMAppWillBeExpireSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\PMAppWillBeExpire();
            $TempData->setCommunity((string)$data[0]);
            $TempData->setEmail((string)$data[1]);
            $TempData->setName((string)$data[2]);
            $TempData->setAccountNum((int)$data[3]);
            $TempData->setList((string)$data[4]);
            $TempData->setBefore((int)$data[5]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/*
end_user的账号已经过期
 */
class CAppExpireSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\AppWillExpire();
            $TempData->setUid((string)$data[0]);
            $TempData->setUserName((string)$data[1]);
            $TempData->setEmail((string)$data[2]);
            $TempData->setCommunity((string)$data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CPhoneExpireSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\PhoneExpire();
            $TempData->setUid((string)$data[0]);
            $TempData->setName((string)$data[1]);
            $TempData->setEmail((string)$data[2]);
            $TempData->setMode((int)$data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CPhoneWillBeExpireSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\PhoneWillExpire();
            $TempData->setUid((string)$data[0]);
            $TempData->setName((string)$data[1]);
            $TempData->setEmail((string)$data[2]);
            $TempData->setBefore((int)$data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CPMFeatureWillBeExpireSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\PmFeatureWillExpire();
            $TempData->setName((string)$data[0]);
            $TempData->setEmail((string)$data[1]);
            $TempData->setLocation((string)$data[2]);
            $TempData->setBefore((int)$data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CInstallerFeatureWillBeExpireSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\InstallerFeatureWillExpire();
            $TempData->setName((string)$data[0]);
            $TempData->setEmail((string)$data[1]);
            $TempData->setLocation((string)$data[2]);
            $TempData->setBefore((int)$data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CInstallerAppWillBeExpireSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\InstallerAppWillExpire();
            $TempData->setName((string)$data[0]);
            $TempData->setEmail((string)$data[1]);
            $TempData->setLocation((string)$data[2]);
            $TempData->setCount((int)$data[3]);
            $TempData->setList((string)$data[4]);
            $TempData->setBefore((int)$data[5]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CInstallerPhoneWillBeExpireSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\InstallerPhoneWillExpire();
            $TempData->setName((string)$data[0]);
            $TempData->setEmail((string)$data[1]);
            $TempData->setCount((int)$data[2]);
            $TempData->setList((string)$data[3]);
            $TempData->setBefore((int)$data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CInstallerAppExpireSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\InstallerAppExpire();
            $TempData->setName((string)$data[0]);
            $TempData->setEmail((string)$data[1]);
            $TempData->setLocation((string)$data[2]);
            $TempData->setCount((int)$data[3]);
            $TempData->setList((string)$data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CInstallerPhoneExpireSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\InstallerPhoneExpire();
            $TempData->setName((string)$data[0]);
            $TempData->setEmail((string)$data[1]);
            $TempData->setCount((int)$data[2]);
            $TempData->setList((string)$data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CWebPersonalModifyNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\WebPersonalModifyNotify();
            $macs = explode(";", $data[0]);
            $TempData->setMacList($macs);
            $TempData->setChangeType((int) $data[1]);
            $TempData->setNode((string) $data[2]);
            $TempData->setInstallerId((int) $data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CWebCommunityModifyNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\WebCommunityModifyNotify();
            $TempData->setChangeType((int) $data[0]);
            $TempData->setNode((string) $data[1]);
            $macs = explode(";", $data[2]);
            $TempData->setMacList($macs);
            $TempData->setCommunityId((int) $data[3]);
            $TempData->setUnitId((int) $data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CPMAppExpireSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\CrontabOffice\PMAppExpire();
            $TempData->setCommunity((string)$data[0]);
            $TempData->setEmail((string)$data[1]);
            $TempData->setName((string)$data[2]);
            $TempData->setAccountNum((int)$data[3]);
            $TempData->setList((string)$data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/*
有住户即将过期发送消息给pm
 */
class CPMAppAccountWillBeExpireSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\PMAppAccountWillBeExpire();
            $TempData->setCommunity((string)$data[0]);
            $TempData->setEmail((string)$data[1]);
            $TempData->setName((string)$data[2]);
            $TempData->setAccountNum((int)$data[3]);
            $TempData->setList((string)$data[4]);
            $TempData->setBefore((int)$data[5]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CPMAppAccountExpireSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\PMAppAccountExpire();
            $TempData->setCommunity((string)$data[0]);
            $TempData->setEmail((string)$data[1]);
            $TempData->setName((string)$data[2]);
            $TempData->setAccountNum((int)$data[3]);
            $TempData->setList((string)$data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CSendMessageNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\SendMessageNotifyMsg();
            $TempData->setKey((string)$data[0]);
            $TempData->setPayload((string)$data[1]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CSendAccUpdateNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\CronUserAccessGroupNotifyMsg();
            $TempData->setAccount((string)$data[0]);
            $TempData->setType((int)$data[1]);
            $TempData->setCommunityId((int)$data[2]);
            $TempData->setAgId((int)$data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

//added by chenyc,2019-07-02,主账号即将过期
function AppWillExpire($uid, $name, $email, $community)
{
    $data[]=$uid;//16
    $data[]=$name;//128
    $data[]=$email;//64
    $data[]=$community;
    $devExpireSocket = new CAppWillBeExpireSocket();
    $devExpireSocket->setMsgID(MSG_P2A_APP_WILLBE_EXPIRE);
    $devExpireSocket->copy($data);
}

function PMAppWillExpire($community, $email, $name, $account_will_expire_num, $list, $before)
{
    $data[]=$community;//128
    $data[]=$email;//64
    $data[]=$name;//128
    $data[]=$account_will_expire_num;
    $data[]=$list;
    $data[]=$before;
    $devExpireSocket = new CPMAppWillBeExpireSocket();
    $devExpireSocket->setMsgID(MSG_P2A_NOTIFY_PM_ACCOUNT_WILL_EXPIRE);
    $devExpireSocket->copy($data);
}
//end_user账号已经过期
function AppExpireMesage($uid, $user_name, $email, $community)
{
    $data[]=$uid;//16
    $data[]=$user_name;//128
    $data[]=$email;//64
    $data[]=$community;
    $devExpireSocket = new CAppExpireSocket();
    $devExpireSocket->setMsgID(MSG_P2A_APP_EXPIRE);
    $devExpireSocket->copy($data);
}

function PhoneExpireMesage($uid, $name, $email, $mode)
{
    $data[]=$uid;//16
    $data[]=$name;//128
    $data[]=$email;//64
    $data[]=$mode;
    $expireSocket = new CPhoneExpireSocket();
    $expireSocket->setMsgID(MSG_P2A_PHONE_EXPIRE);
    $expireSocket->copy($data);
}

function PhoneWillExpire($uid, $name, $email, $before)
{
    $data[]=$uid;//16
    $data[]=$name;//128
    $data[]=$email;//64
    $data[]=$before;
    $expireSocket = new CPhoneWillBeExpireSocket();
    $expireSocket->setMsgID(MSG_P2A_PHONE_WILL_EXPIRE);
    $expireSocket->copy($data);
}

function InstallerAppWillExpire($name, $email, $location, $count, $list, $before)
{
    $data[]=$name;//128
    $data[]=$email;//64
    $data[]=$location;//64
    $data[]=$count;
    $data[]=$list;
    $data[]=$before;
    $devExpireSocket = new CInstallerAppWillBeExpireSocket();
    $devExpireSocket->setMsgID(MSG_P2A_NOTIFY_INSTALLER_APP_WILL_EXPIRE);
    $devExpireSocket->copy($data);
}

function InstallerPhoneWillExpire($name, $email, $count, $list, $before)
{
    $data[]=$name;//128
    $data[]=$email;//64
    $data[]=$count;
    $data[]=$list;
    $data[]=$before;
    $devExpireSocket = new CInstallerPhoneWillBeExpireSocket();
    $devExpireSocket->setMsgID(MSG_P2A_NOTIFY_INSTALLER_PHONE_WILL_EXPIRE);
    $devExpireSocket->copy($data);
}

function InstallerAppExpire($name, $email, $location, $count, $list)
{
    $data[]=$name;//128
    $data[]=$email;//64
    $data[]=$location;//64
    $data[]=$count;
    $data[]=$list;
    $devExpireSocket = new CInstallerAppExpireSocket();
    $devExpireSocket->setMsgID(MSG_P2A_NOTIFY_INSTALLER_APP_EXPIRE);
    $devExpireSocket->copy($data);
}

function InstallerPhoneExpire($name, $email, $count, $list)
{
    $data[]=$name;//128
    $data[]=$email;//64
    $data[]=$count;
    $data[]=$list;
    $devExpireSocket = new CInstallerPhoneExpireSocket();
    $devExpireSocket->setMsgID(MSG_P2A_NOTIFY_INSTALLER_PHONE_EXPIRE);
    $devExpireSocket->copy($data);
}

function PmFeatureWillExpire($name, $email, $location, $before)
{
    $data[]=$name;//128
    $data[]=$email;//64
    $data[]=$location;
    $data[]=$before;
    $devExpireSocket = new CPMFeatureWillBeExpireSocket();
    $devExpireSocket->setMsgID(MSG_P2A_NOTIFY_PM_FEATURE_WILL_EXPIRE);
    $devExpireSocket->copy($data);
}

function InstallerFeatureWillExpire($name, $email, $location, $before)
{
    $data[]=$name;//128
    $data[]=$email;//64
    $data[]=$location;
    $data[]=$before;
    $devExpireSocket = new CInstallerFeatureWillBeExpireSocket();
    $devExpireSocket->setMsgID(MSG_P2A_NOTIFY_INSTALLER_FEATURE_WILL_EXPIRE);
    $devExpireSocket->copy($data);
}


function webPersonalModifyNotify($changeType, $node = "", $mac = "", $installer_id = 0)
{
    $data[0] = $mac;
    $data[1] = $changeType;
    $data[2] = $node;
    $data[3] = $installer_id;

    $Socket = new CWebPersonalModifyNotifySocket();
    $Socket->setMsgID(MSG_P2A_NOTIFY_PERSONAL_MESSAGE);
    $Socket->copy($data);
}

function webCommunityModifyNotify($changeType, $node = "", $mac = "", $communitid = 0, $unitid = 0)
{
    $data[0] = $changeType;
    $data[1] = $node;
    $data[2] = $mac;
    $data[3] = $communitid;
    $data[4] = $unitid;

    $Socket = new CWebCommunityModifyNotifySocket();
    $Socket->setMsgID(MSG_P2A_NOTIFY_COMMUNITY_MESSAGE);
    $Socket->copy($data);
}

function FeatureExpireNotify($communitid)
{
    webCommunityModifyNotify(WEB_COMM_FEATURE_PLAN_RENEW, "", "", $communitid);
}

function PMAppExpire($community, $email, $name, $account_will_expire_num, $list)
{
    $data[]=$community;//128
    $data[]=$email;//64
    $data[]=$name;//128
    $data[]=$account_will_expire_num;
    $data[]=$list;
    $devExpireSocket = new CPMAppExpireSocket();
    $devExpireSocket->setMsgID(MSG_P2A_NOTIFY_OFFICE_PM_ACCOUNT_EXPIRE);
    $devExpireSocket->setMsgFrom(PROJECT_TYPE_OFFICE);
    $devExpireSocket->copy($data);
}

function PMOfficeAppWillExpire($community, $email, $name, $account_will_expire_num, $list, $before)
{
    $data[]=$community;//128
    $data[]=$email;//64
    $data[]=$name;//128
    $data[]=$account_will_expire_num;
    $data[]=$list;
    $data[]=$before;
    $devExpireSocket = new CPMAppWillBeExpireSocket();
    $devExpireSocket->setMsgID(MSG_P2A_NOTIFY_OFFICE_PM_ACCOUNT_WILL_EXPIRE);
    $devExpireSocket->setMsgFrom(PROJECT_TYPE_OFFICE);
    $devExpireSocket->copy($data);
}

function OfficePmFeatureWillExpire($name, $email, $location, $before)
{
    $data[]=$name;//128
    $data[]=$email;//64
    $data[]=$location;
    $data[]=$before;
    $devExpireSocket = new CPMFeatureWillBeExpireSocket();
    $devExpireSocket->setMsgID(MSG_P2A_NOTIFY_OFFICE_PM_FEATURE_WILL_EXPIRE);
    $devExpireSocket->setMsgFrom(PROJECT_TYPE_OFFICE);
    $devExpireSocket->copy($data);
}

function OfficeInstallerFeatureWillExpire($name, $email, $location, $before)
{
    $data[]=$name;//128
    $data[]=$email;//64
    $data[]=$location;
    $data[]=$before;
    $devExpireSocket = new CInstallerFeatureWillBeExpireSocket();
    $devExpireSocket->setMsgID(MSG_P2A_NOTIFY_OFFICE_INSTALLER_FEATURE_WILL_EXPIRE);
    $devExpireSocket->setMsgFrom(PROJECT_TYPE_OFFICE);
    $devExpireSocket->copy($data);
}

function OfficePmFeatureExpire($name, $email, $location, $before)
{
    $data[]=$name;//128
    $data[]=$email;//64
    $data[]=$location;
    $data[]=$before;
    $devExpireSocket = new CPMFeatureWillBeExpireSocket();
    $devExpireSocket->setMsgID(MSG_P2A_NOTIFY_OFFICE_PM_FEATURE_EXPIRE);
    $devExpireSocket->setMsgFrom(PROJECT_TYPE_OFFICE);
    $devExpireSocket->copy($data);
}

function OfficeInstallerFeatureExpire($name, $email, $location, $before)
{
    $data[]=$name;//128
    $data[]=$email;//64
    $data[]=$location;
    $data[]=$before;
    $devExpireSocket = new CInstallerFeatureWillBeExpireSocket();
    $devExpireSocket->setMsgID(MSG_P2A_NOTIFY_OFFICE_INSTALLER_FEATURE_EXPIRE);
    $devExpireSocket->setMsgFrom(PROJECT_TYPE_OFFICE);
    $devExpireSocket->copy($data);
}

function webOfficeModifyNotify($changeType, $node = "", $mac = "", $communitid = 0, $unitid = 0)
{
    $data[0] = $changeType;
    $data[1] = $node;
    $data[2] = $mac;
    $data[3] = $communitid;
    $data[4] = $unitid;

    $Socket = new CWebCommunityModifyNotifySocket();
    $Socket->setMsgID(MSG_P2A_NOTIFY_OFFICE_INFO_UPDATE);
    $Socket->setMsgFrom(PROJECT_TYPE_OFFICE);
    $Socket->copy($data);
}
function OfficeFeatureExpireNotify($communitid)
{
    webOfficeModifyNotify(WEB_COMM_FEATURE_PLAN_RENEW, "", "", $communitid);
}

function PMAppAccountWillExpire($community, $email, $name, $account_will_expire_num, $list, $before)
{
    $data[]=$community;//128
    $data[]=$email;//64
    $data[]=$name;//128
    $data[]=$account_will_expire_num;
    $data[]=$list;
    $data[]=$before;
    $devExpireSocket = new CPMAppAccountWillBeExpireSocket();
    $devExpireSocket->setMsgID(MSG_P2A_NOTIFY_PM_APP_ACCOUNT_WILL_EXPIRE);
    $devExpireSocket->copy($data);
}

function PMAppAccountExpire($community, $email, $name, $account_will_expire_num, $list)
{
    $data[]=$community;//128
    $data[]=$email;//64
    $data[]=$name;//128
    $data[]=$account_will_expire_num;
    $data[]=$list;
    $devExpireSocket = new CPMAppAccountExpireSocket();
    $devExpireSocket->setMsgID(MSG_P2A_NOTIFY_PM_APP_ACCOUNT_EXPIRE);
    $devExpireSocket->copy($data);
}

//检查as下是不是迁移的dis
function checkAppIsMigrateDis($install_id)
{
    if (SERVER_LOCATION != "as" && SERVER_LOCATION != "eu" ) {
        return 0;
    }  

    global $db;
    global $jp_dis_array;
    global $au_dis_array;
    global $az_dis_array;
    global $eu_dis_array;

    $sth = $db->prepare("select AA.Account as DIS From Account A left join Account AA on  AA.UUID=A.ParentUUID where A.ID=:ins_id;");
    $sth->bindParam(':ins_id', $install_id, PDO::PARAM_STR);
    $sth->execute();

    $dis = $sth->fetch(PDO::FETCH_ASSOC)["DIS"];

    if (SERVER_LOCATION == "as" && in_array($dis, $au_dis_array)) {
        echo "Is aucloud Dis, xinjiapo not need handle! install_id=$install_id\n"; 
        return 1;
    }
    
    if (SERVER_LOCATION == "as" && in_array($dis, $jp_dis_array)) {
        echo "Is Jp Dis, xinjiapo not need handle! install_id=$install_id\n";
        return 1;
    }

    if (SERVER_LOCATION == "eu" && in_array($dis, $az_dis_array)) {
        echo "Is Az Dis, ecloud not need handle! install_id=$install_id\n";
        return 1;
    }
    if (SERVER_LOCATION == "eu" && in_array($dis, $eu_dis_array)) {
        echo "Is e2ucloud Dis, ecloud not need handle! install_id=$install_id\n";
        return 1;
    }
    return 0;
}

function checkAppIsMigrateDisByUUID($ins_uuid)
{
    if (SERVER_LOCATION != "as" && SERVER_LOCATION != "eu" ) {
        return 0;
    }
    global $db;
    global $jp_dis_array;
    global $au_dis_array;
    global $az_dis_array;
    global $eu_dis_array;
    $sth = $db->prepare("select AA.Account as DIS From Account A left join Account AA on  AA.UUID=A.ParentUUID where A.UUID=:ins_uuid;");
    $sth->bindParam(':ins_uuid', $ins_uuid, PDO::PARAM_STR);
    $sth->execute();

    $dis = $sth->fetch(PDO::FETCH_ASSOC)["DIS"];

    if (SERVER_LOCATION == "as" && in_array($dis, $au_dis_array)) {
        echo "Is aucloud Dis, xinjiapo not need handle! uuid=$ins_uuid\n";
        return 1;
    }

    if (SERVER_LOCATION == "as" && in_array($dis, $jp_dis_array)) {
        echo "Is Jp Dis, xinjiapo not need handle! uuid=$ins_uuid\n";
        return 1;
    }

    if (SERVER_LOCATION == "eu" && in_array($dis, $az_dis_array)) {
        echo "Is Az Dis, ecloud not need handle! install_id=$ins_uuid\n";
        return 1;
    }
    if (SERVER_LOCATION == "eu" && in_array($dis, $eu_dis_array)) {
        echo "Is e2ucloud Dis, ecloud not need handle! install_id=$ins_uuid\n";
        return 1;
    }
    return 0;
}







function switchHandle($value, $pos)
{
    return ($value >> $pos) & 1;
}

function querySList($db, $sql, $bindArray)
{
    $simt = $db->prepare($sql);
    foreach ($bindArray as $key => $value) {
        $simt->bindValue($key, $value);
    }
    $simt->execute();
    $data = $simt->fetchAll(PDO::FETCH_ASSOC);
    return $data;
}

//TODO:待CommunityInfo表补充UUID后还需要再整改
function CheckUidEnalbeSmarthome($uid)
{
    global $db;

    $community_id = 0;
    $account = querySList($db, "select Role, ParentID, EnableSmartHome,ParentUUID from PersonalAccount where Account =:Account", [":Account" => $uid])[0];
    $role = $account["Role"];
    $parent_id = $account["ParentID"];
    $enable_smarthome = $account["EnableSmartHome"];
    $parent_uuid = $account["ParentUUID"];

    if ($role == ACCOUNT_ROLE_COMMUNITY_MAIN) {
        $community_id = $parent_id;
    } elseif ($role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT) {
        $community_id = querySList($db, "SELECT ParentID FROM PersonalAccount  WHERE UUID = :ParentUUID", [":ParentUUID" => $parent_uuid])[0]["ParentID"];
    } elseif ($role == ACCOUNT_ROLE_PERSONNAL_MAIN) {
        return $enable_smarthome;
    } elseif ($role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT) {
        $enable_smarthome = querySList($db, "SELECT EnableSmartHome FROM PersonalAccount  WHERE UUID = :ParentUUID", [":ParentUUID" => $parent_uuid])[0]["EnableSmartHome"];
        return $enable_smarthome;
    }
    $switch = querySList($db, "select Switch From CommunityInfo  WHERE AccountID = :AccountID", [":AccountID" => $community_id])[0]["Switch"];
    return switchHandle($switch, COMMUNITY_SWITCH_SMARTHOME);
}

function IsEnablePersonalFeaturePlan($switch)
{
    return switchHandle($switch, PERSONAL_FEATURE_PLAN_SWITCH);
}

function ModifyExpireAccountSipInfo($account, $UUID)
{
    global $db;

    $sth = $db->prepare("select Account from PersonalAccount where Role=21 and ParentUUID=:UUID");
    $sth->bindParam(':UUID', $UUID, PDO::PARAM_STR);
    $sth->execute();
    $Alist = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($Alist as $row => $value) { //从账号进行sip禁用
        $SIP = $value['Account'];
        $sth = $db->prepare("insert into LocalSipTransaction(Sip,Message) values(:SIP,:MSG);");
        $sth->bindParam(':SIP', $SIP, PDO::PARAM_STR);
        @$sth->bindParam(':MSG', json_encode(["messageType" => "3", "sip" => strval($SIP), "sipEnable" => strval("0")]), PDO::PARAM_STR);
        $sth->execute();
    }

    $sth = $db->prepare("select SipAccount from Devices where Node = :node");
    $sth->bindParam(':node', $account, PDO::PARAM_STR);
    $sth->execute();
    $Slist = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($Slist as $row => $value) { //个人独享设备进行sip禁用
        $SIP = $value['SipAccount'];
        $sth = $db->prepare("insert into LocalSipTransaction(Sip,Message) values(:SIP,:MSG);");
        $sth->bindParam(':SIP', $SIP, PDO::PARAM_STR);
        @$sth->bindParam(':MSG', json_encode(["messageType" => "3", "sip" => strval($SIP), "sipEnable" => strval("0")]), PDO::PARAM_STR);
        $sth->execute();
    }
    $SIP = $account;
    $sth = $db->prepare("insert into LocalSipTransaction(Sip,Message) values(:SIP,:MSG);"); //主账号sip禁用
    $sth->bindParam(':SIP', $SIP, PDO::PARAM_STR);
    @$sth->bindParam(':MSG', json_encode(["messageType" => "3", "sip" => strval($SIP), "sipEnable" => strval("0")]), PDO::PARAM_STR);
    $sth->execute();
}

function getPersonalEmailByUUID($userInfoUUID)
{
    global $medooDb;
    return $medooDb->get("PersonalAccountUserInfo", ["Email"], ["UUID" => $userInfoUUID])["Email"];
/*
    $sth = $db->prepare("select Email from PersonalAccountUserInfo where UUID=:uuid");
    $sth->bindParam(':uuid', $userinfo_uuid, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return $result['Email'];
*/
}

function getPersonalUserInfoByUUID($userInfoUUID)
{
    global $medooDb;
    return $medooDb->get("PersonalAccountUserInfo", ["Email","MobileNumber"], ["UUID" => $userInfoUUID]);

/*
    $sth = $db->prepare("select Email,MobileNumber from PersonalAccountUserInfo where UUID=:uuid");
    $sth->bindParam(':uuid', $userinfo_uuid, PDO::PARAM_STR);
    $sth->execute();
    $user_info_arr = $sth->fetch(PDO::FETCH_ASSOC);
    return $user_info_arr;
*/
}

function getPMAccountEmailByAccountUUID($accountUUID)
{
    global $medooDb;
    return $medooDb->get("AccountUserInfo",
        [
            "[>]AccountMap" => ["AccountUserInfo.UUID" => "UserInfoUUID"]
        ],[
            "AccountUserInfo.Email"
        ],[
           "AccountMap.AccountUUID" => $accountUUID
        ])['Email'];

/*
    $sth = $db->prepare("select A.Email from AccountUserInfo A join AccountMap B on A.UUID = B.UserInfoUUID where B.AccountUUID=:uuid");
    $sth->bindParam(':uuid', $account_uuid, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return $result['Email'];
*/
}

function getExpirePersonalAccountInfo($before)
{
    global $medooDb;
    return $medooDb->select("PersonalAccount", ["Account","Name","UserInfoUUID","ID","ParentID","Language","ParentUUID","UUID"], 
                    Medoo::raw("where TO_DAYS(ExpireTime) = TO_DAYS(NOW()) + :before and Role = 20 and Active = 1 and Special = 0", [':before' => $before]));

/*
    $sth = $db->prepare("select Account,Name,UserInfoUUID,ID,ParentID,Language,ParentUUID,UUID from PersonalAccount where (TO_DAYS(ExpireTime) = TO_DAYS(NOW()) + :before) and Role = 20 and Active = 1 and Special = 0");
    $sth->bindParam(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $expire_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    return $expire_list;
*/
}

function getAppExpireAccountIDs($before)
{
    global $db;

    $sth = $db->prepare("select DISTINCT A.ID from PersonalAccount P left join Account A on P.ParentUUID = A.UUID where (TO_DAYS(P.ExpireTime) = TO_DAYS(NOW()) + :before) and P.Role = 20 and P.Active = 1 and P.Special = 0");
    $sth->bindParam(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $account_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    return $account_list;
}

//查找有落地过期的用户账号，以用户为粒度
function GetFeaturePlanExpirePersonalAccountInfo($before)
{
    global $medooDb;
    return $medooDb->select("PersonalAccount",
    [
        "[>]PersonalAccountSingleInfo" => ["PersonalAccount.UUID" => "PersonalAccountUUID"],
    ], [
        "PersonalAccount.ID",
        "PersonalAccount.Account",
        "PersonalAccount.Name",
        "PersonalAccount.UserInfoUUID",
        "PersonalAccount.ParentID",
        "PersonalAccount.Switch",
        "PersonalAccount.Language",
        "PersonalAccount.ParentUUID",
        "PersonalAccount.UUID",
        "PersonalAccountSingleInfo.IsNewBilling"
    ], Medoo::raw("where TO_DAYS(PersonalAccount.PhoneExpireTime) = TO_DAYS(NOW()) + :before and PersonalAccount.Role = 10", [':before' => $before])
    );
}

//查找有高级功能过期的所有ins账号，以ins账号为粒度
function GetFeaturePlanExpireInsAccountUUIDs($before)
{
    global $medooDb;
    //group by Account.UUID 去重
    return $medooDb->select("PersonalAccount",
    [
        "[>]PersonalAccountSingleInfo" => ["PersonalAccount.UUID" => "PersonalAccountUUID"],
        "[>]Account" => ["PersonalAccount.ParentUUID" => "UUID"],
    ], [
        "Account.UUID",
    ], Medoo::raw("where (TO_DAYS(PersonalAccount.PhoneExpireTime) = TO_DAYS(NOW()) + :before) and PersonalAccount.Role = 10 group by Account.UUID", [':before' => $before])
    );
}

//从账号移入群响铃
function MoveSlaveAccountsIntoLocalSipTransaction($uuid)
{
    global $db;
    $sth = $db->prepare("select Account from PersonalAccount where Role=11 and ParentUUID=:UUID");
    $sth->bindParam(':UUID', $uuid, PDO::PARAM_STR);
    $sth->execute();
    $all_slave = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($all_slave as $row => $value) {
        $SIP = $value['Account'];
        $sth = $db->prepare("insert into LocalSipTransaction(Sip,Message) values(:SIP,:MSG);");
        $sth->bindParam(':SIP', $SIP, PDO::PARAM_STR);
        @$sth->bindParam(':MSG', json_encode(["messageType" => "1", "sip" => strval($SIP), "groupring" => strval("1")]), PDO::PARAM_STR);
        $sth->execute();
    }
}

//社区的付费方式直接看社区的配置，所以：A.ChargeMode as InstallerMode
function getCommunityChargeMode($mng_id)
{
    global $db;
    // A 表示社区，B表示Dis，C表示Installer
    $sth = $db->prepare("select A.UUID as CommUUID, A.Location, A.ManageGroup as InsID, A.SendExpireEmailType, A.ChargeMode as ProjectMode, 
                        C.Language as InsLanguage, B.ChargeMode as DistributorMode, B.Account as DisAccount, B.ID as DisID, 
                        B.UUID as DisUUID, B.Language as DisLanguage, C.PayType as InstallerMode 
                        from Account A 
                        join Account C on A.ManageGroup = C.ID 
                        join Account B on A.ParentUUID = B.UUID where A.ID=:comm"
                    );

    $sth->bindParam(':comm', $mng_id, PDO::PARAM_INT);
    $sth->execute();
    return $sth->fetch(PDO::FETCH_ASSOC);
}



function getAccountChargeModeByUUID($account_uuid)
{
    global $db;
    // A 表示社区，B表示Dis，C表示Installer
    $sth = $db->prepare("SELECT A.ID, A.Location, A.ManageGroup as InsID, A.SendExpireEmailType, A.ChargeMode as ProjectMode, 
                        C.Language as InsLanguage, B.ChargeMode as DistributorMode, B.Account as DisAccount, B.ID as DisID, 
                        B.UUID as DisUUID, B.Language as DisLanguage, C.PayType as InstallerMode 
                        from Account A 
                        join Account C on A.ManageGroup = C.ID 
                        join Account B on A.ParentUUID = B.UUID where A.UUID=:uuid"
    );

    $sth->bindParam(':uuid', $account_uuid, PDO::PARAM_STR);
    $sth->execute();
    return $sth->fetch(PDO::FETCH_ASSOC);
}

function getPersonalChargeModeByInsUUID($ins_uuid){
    global $db;
    // I表示Ins记录，D表示Dis记录
    $sth = $db->prepare("SELECT I.ID as InsID,I.UUID as InsUUID,I.Account as InsAccount,I.PayType as InstallerMode, 
                        I.Location, I.SendExpireEmailType as SendExpireEmailType,
                        D.ID as DisID,D.UUID as DisUUID,D.Account as DisAccount,D.ChargeMode as DistributorMode,
                        0 as ProjectMode 
                        from Account I 
                        join Account D on I.ParentUUID = D.UUID 
                        where I.UUID=:ins_uuid"
                    );

    $sth->bindParam(':ins_uuid', $ins_uuid, PDO::PARAM_STR);
    $sth->execute();
    return $sth->fetch(PDO::FETCH_ASSOC);
}

function getWillExpireAccountCounts($comm_uuid, $before)
{
    global $db;
    $sth = $db->prepare("select count(1) as count from PersonalAccount P join Account A on P.ParentUUID = A.UUID
	where P.ParentUUID = :comm_uuid and (TO_DAYS(P.ExpireTime) = TO_DAYS(NOW()) + :before) and P.Role = 20 and P.Active = 1 and P.Special = 0");
    $sth->bindParam(':comm_uuid', $comm_uuid, PDO::PARAM_STR);
    $sth->bindParam(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);
    return $ret['count'];
}

//TODO:待CommunityInfo表补充UUID后改为用uuid查找
function getAppWillExpireEmailInfo($comm_uuid, $before, &$autopay_usernum)
{
    global $db;
    $jsonArr = [];
    $aptArr = [];
    $sth = $db->prepare("select P.RoomNumber, U.UnitName, R.RoomName,P.UUID 
                        from PersonalAccount P join CommunityUnit U on P.UnitID = U.ID join CommunityRoom R on P.RoomID = R.ID
                        where P.ParentUUID = :comm_uuid and (TO_DAYS(P.ExpireTime) = TO_DAYS(NOW()) + :before) and P.Role = 20 and P.Active = 1 and P.Special = 0");
    $sth->bindParam(':comm_uuid', $comm_uuid, PDO::PARAM_STR);
    $sth->bindParam(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $enduser_list = $sth->fetchALL(PDO::FETCH_ASSOC);

    foreach ($enduser_list as $key => $value) 
    {
        if (IsUserEnableAutoPay($value['UUID'], AUTO_PAY_ORDER_TYPE_COMMUNITY)) {
            $autopay_usernum++;
            continue; 
        }

        // 即将过期且没有开通自动扣费的用户数超过20不发
        if (count($jsonArr) == 20) {
            continue;
        }
        
        $aptArr["Building"] = $value["UnitName"];
        //历史原因num和name是相反的
        $aptArr["AptNum"] = $value["RoomName"];
        $aptArr["AptName"] = $value["RoomNumber"];
        
        array_push($jsonArr, $aptArr);
    }
    $jsonStr = json_encode($jsonArr);
    return $jsonStr;
}

function getAppExpireEmailInfo($comm_uuid, &$count)
{
    global $db;

    $jsonArr = [];
    $aptArr = [];
    $sth = $db->prepare("select P.RoomNumber, U.UnitName, R.RoomName 
                        from PersonalAccount P join CommunityUnit U on P.UnitID = U.ID join CommunityRoom R on P.RoomID = R.ID
                        where P.ParentUUID = :comm_uuid and (TO_DAYS(P.ExpireTime) < TO_DAYS(NOW())) and P.Role = 20 and P.Active = 1 and P.Special = 0");
    $sth->bindParam(':comm_uuid', $comm_uuid, PDO::PARAM_STR);
    $sth->execute();
    $enduser_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    $count = count($enduser_list);
    foreach ($enduser_list as $key => $value) {
        if ($key == 20) { //超过20的不发
            break;
        }
        $aptArr["Building"] = $value["UnitName"];
        $aptArr["AptNum"] = $value["RoomName"];
        $aptArr["AptName"] = $value["RoomNumber"];
        array_push($jsonArr, $aptArr);
    }
    $jsonStr = json_encode($jsonArr);
    return $jsonStr;
}

function GetFeaturePlanWillExpireEmailStr($feature_plan_expire_user_list, &$autopay_usernum)
{
/*
    $sth02 = $db->prepare("select UserInfoUUID,Switch,Name,UUID from PersonalAccount where ParentUUID = :UUID and (TO_DAYS(PhoneExpireTime) = TO_DAYS(NOW()) + :before) and (Role = 10) and PhoneStatus = 1");
    $sth02->bindParam(':UUID', $account_uuid, PDO::PARAM_STR);
    $sth02->bindParam(':before', $before, PDO::PARAM_INT);
    $sth02->execute();
    $enduser_list = $sth02->fetchALL(PDO::FETCH_ASSOC);
*/
    $jsonArr = [];
    $userArr = [];

    foreach ($feature_plan_expire_user_list as $row => $value) {
        if(IsUserEnableAutoPay($value['UUID'], AUTO_PAY_ORDER_TYPE_SINGLE))
        {
            $autopay_usernum++;
            continue; 
        }

        if (count($jsonArr) == 20) { //超过20的不发
            break;
        }

        $userArr["Name"] = $value["Name"];
        $user_info_arr = [];
        
        $user_info_arr = getPersonalUserInfoByUUID($value["UserInfoUUID"]);
        $userArr["Email"] = $user_info_arr["Email"];
        if ($userArr["Email"] == "") {
            $userArr["Email"] = "--";
        }

        $userArr["MobileNumber"] = $user_info_arr["MobileNumber"];
        if ($userArr["MobileNumber"] == "") {
            $userArr["MobileNumber"] = "--";
        }
        array_push($jsonArr, $userArr);
    }
    $jsonStr = json_encode($jsonArr);
    return $jsonStr;
}

function GetSingleTenantFeaturePlanWillExpireList($before, $account_new_expire_mod, $account_uuid)
{
    global $medooDb;
    $expire_end_user_list = $medooDb->select("PersonalAccount",
    [
        "[>]PersonalAccountSingleInfo" => ["PersonalAccount.UUID" => "PersonalAccountUUID"],

    ], [
        "PersonalAccount.UserInfoUUID",
        "PersonalAccount.Switch",
        "PersonalAccount.Name",
        "PersonalAccount.UUID",
    ], Medoo::raw("where PersonalAccount.ParentUUID = :parentUUID and (TO_DAYS(PersonalAccount.PhoneExpireTime) = TO_DAYS(NOW()) + :before) 
                            and PersonalAccount.Role = 10 and PersonalAccountSingleInfo.IsNewBilling = :account_expire_mod", [':before' => $before, ':parentUUID' => $account_uuid, ':account_expire_mod' => $account_new_expire_mod]));
 
    //新付费模式下，不区分是否开启了高级功能开关
    if ($account_new_expire_mod)
    {
        return $expire_end_user_list;
    }
    //旧付费模式下，开高级功能开关的才发
    $feature_will_expire_end_user_list = array();
    foreach($expire_end_user_list as $end_user)
    {
        if (IsEnablePersonalFeaturePlan($end_user['Switch']))
        {
            array_push($feature_will_expire_end_user_list, $end_user);
        }
    }
    return $feature_will_expire_end_user_list;
}

//ins的email目前只能通过id获取
function getInstallerEmailInfoByInsID($insID)
{
    global $medooDb;
    return $medooDb->get("InstallerBillingInfo", [
        "[>]Account" => ["InstallerBillingInfo.Account" => "Account"]
    ], [
        "InstallerBillingInfo.Account",
        "InstallerBillingInfo.Email",
        "Account.UUID",
        "Account.Language"
    ], [
        "Account.ID" => $insID
    ]);

/*
    $sth = $db->prepare("select I.Account,I.Email,A.UUID,A.Language from InstallerBillingInfo I left join Account A on I.Account = A.Account where A.ID = :insID ");
    //查账单中的installer邮箱
    $sth->bindParam(':insID', $insID, PDO::PARAM_INT);
    $sth->execute();
    $account_list = $sth->fetch(PDO::FETCH_ASSOC);

    // 数据合规
    $account_list['Email'] = DataConfusion::getInstance()->decrypt($account_list['Email']);
    return $account_list;
*/
}


//ins的email目前只能通过id获取
function getInstallerEmailInfoByAccountUUID($ins_uuid)
{
    global $medooDb;
    return $medooDb->get("InstallerBillingInfo", [
        "[>]Account" => ["InstallerBillingInfo.Account" => "Account"]
    ], [
        "InstallerBillingInfo.Account",
        "InstallerBillingInfo.Email",
        "Account.UUID",
        "Account.Language"
    ], [
        "Account.UUID" => $ins_uuid
    ]);

/*
    $sth = $db->prepare("select I.Account,I.Email,A.UUID,A.Language from InstallerBillingInfo I left join Account A on I.Account = A.Account where A.ID = :insID ");
    //查账单中的installer邮箱
    $sth->bindParam(':insID', $insID, PDO::PARAM_INT);
    $sth->execute();
    $account_list = $sth->fetch(PDO::FETCH_ASSOC);

    // 数据合规
    $account_list['Email'] = DataConfusion::getInstance()->decrypt($account_list['Email']);
    return $account_list;
*/
}

function getDisEmailByDisAccount($account)
{
    global $medooDb;
    return $medooDb->get("InstallerBillingInfo", "Email", ["Account" => $account]);

/*
    $sth = $db->prepare("select Email from InstallerBillingInfo where Account = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return $result['Email'];
*/
}

function getCommAndPMInfoByMngID($comm_uuid)
{
    global $db;

    $sth = $db->prepare("select B.Location, D.UUID, E.FirstName, E.LastName, D.Language from Account B 
    left join PropertyMngList C on C.CommunityID = B.ID 
    left join Account D on D.ID = C.PropertyID 
    left join PropertyInfo E on E.AccountID = C.PropertyID where B.UUID = :comm_uuid");
    $sth->bindParam(':comm_uuid', $comm_uuid, PDO::PARAM_STR);
    $sth->execute();
    $comm_and_pm_info = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $comm_and_pm_info;
}

function getOfficeAndPMInfoByMngID($office_uuid)
{
    global $db;
    $sth = $db->prepare("select D.UUID, E.FirstName, E.LastName, D.Language from Account B 
    left join PropertyMngList C on C.CommunityID = B.ID 
    left join Account D on D.ID = C.PropertyID 
    left join PropertyInfo E on E.AccountID = C.PropertyID where B.UUID = :office_uuid");
    $sth->bindParam(':office_uuid', $office_uuid, PDO::PARAM_STR);
    $sth->execute();
    $office_and_pm_info = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $office_and_pm_info;
}

function getCommExpirePmAppList($communityUUID, $before)
{
    global $medooDb;
    return $medooDb->select("PersonalAccount", ["Name", "UUID"], 
            Medoo::raw("where ParentUUID = :communityUUID and (TO_DAYS(ExpireTime) = TO_DAYS(NOW()) + :before) and Role = 40 and Active = 1",  [':before' => $before, ':communityUUID' => $communityUUID]));
/*
    $sth = $db->prepare("select P.Name,P.UUID from PersonalAccount P  
    where P.ParentUUID = :comm_uuid and (TO_DAYS(ExpireTime) = TO_DAYS(NOW()) + :before) and P.Role = 40 and P.Active = 1");
    $sth->bindParam(':comm_uuid', $comm_uuid, PDO::PARAM_STR);
    $sth->bindParam(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $expire_pm_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    return $expire_pm_list;
*/
}

// 获取pm app已经过期一天的账号列表
function GetOldOfficeAppExpireList($officeUUID)
{
    global $medooDb;
    $role_str = getOldOfficeUserRoleStr();
    return $medooDb->select("PersonalAccount", [
        "[>]CommunityUnit" => ["PersonalAccount.UnitID" => "ID"],
        "[>]PersonalAccountOfficeInfo" => ["PersonalAccount.UUID" => "PersonalAccountUUID"]
    ], [
        "PersonalAccount.Name",
        "CommunityUnit.UnitName",
        "PersonalAccount.Account",
        "PersonalAccountOfficeInfo.EmployeeID",
        "PersonalAccount.UUID"
    ], 
       Medoo::raw("where PersonalAccount.ParentUUID = :officeUUID and PersonalAccount.Role in ($role_str) and PersonalAccount.Active = 1 and (TO_DAYS(PersonalAccount.ExpireTime) = TO_DAYS(NOW()) - 1)", [':officeUUID' => $officeUUID])
    );
/*
    $sth = $db->prepare("select P.Name, U.UnitName, P.Account, O.EmployeeID, P.UUID 
    from PersonalAccount P 
    left join CommunityUnit U on P.UnitID = U.ID
    left join PersonalAccountOfficeInfo O on O.PersonalAccountUUID = P.UUID
    where P.ParentUUID = :office_uuid and P.Role in (30,31) and (TO_DAYS(ExpireTime) < TO_DAYS(NOW())) and P.Active = 1");
    $sth->bindParam(':office_uuid', $office_uuid, PDO::PARAM_STR);
    $sth->execute();
    $expire_enduser_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    return $expire_enduser_list;
*/
}

function GetOldOfficeAppWillExpireList($officeUUID, $before)
{
    global $medooDb;
    $role_str = getOldOfficeUserRoleStr();
    return $medooDb->select("PersonalAccount", 
    [
        "[>]CommunityUnit" => ["PersonalAccount.UnitID" => "ID"],
        "[>]PersonalAccountOfficeInfo" => ["PersonalAccount.UUID" => "PersonalAccountUUID"]
    ], [
        "PersonalAccount.Name",
        "CommunityUnit.UnitName",
        "PersonalAccount.Account",
        "PersonalAccountOfficeInfo.EmployeeID",
        "PersonalAccount.UUID"
    ], Medoo::raw("where PersonalAccount.ParentUUID = :officeUUID and PersonalAccount.Role in ($role_str) and (TO_DAYS(PersonalAccount.ExpireTime) = TO_DAYS(NOW()) + :before) and PersonalAccount.Active = 1",
                [':officeUUID' => $officeUUID, ':before' => $before])
    );
/*
    $sth = $db->prepare("select P.Name, U.UnitName, P.Account, O.EmployeeID, P.UUID from PersonalAccount P 
    left join CommunityUnit U on P.UnitID = U.ID
    left join PersonalAccountOfficeInfo O on O.PersonalAccountUUID = P.UUID
    where P.ParentUUID = :office_uuid and P.Role in (30,31) and (TO_DAYS(ExpireTime) = TO_DAYS(NOW()) + :before) and P.Active = 1");
    
    $sth->bindParam(':office_uuid', $office_uuid, PDO::PARAM_STR);
    $sth->bindParam(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $expire_enduser_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    return $expire_enduser_list;
*/
}

function  GetNewOfficeAppExpireList($officeUUID, $before)
{
    global $medooDb;

    $role_str = getNewOfficeUserRoleStr();
    return $medooDb->select("PersonalAccount",
    [
        "[>]OfficePersonnel" => ["PersonalAccount.UUID" => "PersonalAccountUUID"],
        "[>]OfficeCompany" => ["OfficePersonnel.OfficeCompanyUUID" => "UUID"]
    ], [
        "PersonalAccount.Name",
        "PersonalAccount.UUID",
        "OfficeCompany.Name(CompanyName)",
        "OfficePersonnel.IdNo"
    ], Medoo::raw("where PersonalAccount.ParentUUID = :officeUUID and PersonalAccount.Role in ($role_str) and (TO_DAYS(PersonalAccount.ExpireTime) = TO_DAYS(NOW()) + :before) and PersonalAccount.Active = 1",
                [':officeUUID' => $officeUUID, ':before' => $before])
    );
}

function GetOfficeAppExpireList($office, $is_new, $before)
{
    if ($is_new)
    {
        return GetNewOfficeAppExpireList($office['AccountUUID'], $before);
    }
    else
    {
        if ($before == -1) {
            return GetOldOfficeAppExpireList($office['AccountUUID']);
        } else {
            return GetOldOfficeAppWillExpireList($office['AccountUUID'], $before);
        }
    }
}

function GetOfficeExpireInfoList($is_new, $expire_user_list)
{
    $user_info = array();
    if ($is_new)
    {
        $user_info['Company'] = $expire_user_list['CompanyName'];
        $user_info['Name'] = $expire_user_list['Name'];
        $user_info['ID'] = $expire_user_list['IdNo'];
        return $user_info;
    }
    else
    {
        $user_info["Department"] = $expire_user_list["UnitName"];
        $user_info["Name"] = $expire_user_list["Name"];
        $user_info["ID"] = $expire_user_list["EmployeeID"];
        return $user_info;
    }
}

function DisableNodeAndDeviceSip($account)
{
    global $db;

    $sth = $db->prepare("select SipAccount from Devices where Node = :node");
    $sth->bindParam(':node', $account, PDO::PARAM_STR);
    $sth->execute();
    $Slist = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($Slist as $row => $value1) { //个人独享设备进行sip禁用
        $SIP = $value1['SipAccount'];
        $sth = $db->prepare("insert into LocalSipTransaction(Sip,Message) values(:SIP,:MSG);");
        $sth->bindParam(':SIP', $SIP, PDO::PARAM_STR);
        @$sth->bindParam(':MSG', json_encode(["messageType" => "3", "sip" => strval($SIP), "sipEnable" => strval("0")]), PDO::PARAM_STR);
        $sth->execute();
    }
    $SIP = $account;
    $sth = $db->prepare("insert into LocalSipTransaction(Sip,Message) values(:SIP,:MSG);"); //主账号sip禁用
    $sth->bindParam(':SIP', $SIP, PDO::PARAM_STR);
    @$sth->bindParam(':MSG', json_encode(["messageType" => "3", "sip" => strval($SIP), "sipEnable" => strval("0")]), PDO::PARAM_STR);
    $sth->execute();
}

function getSubDisAccountInfo($dis_uuid)
{
    global $db;

    $sth = $db->prepare("select Account,UUID,Language,ChargeMode as SubDisMode from Account where ParentUUID = :dis_uuid and Grade = 12");
    $sth->bindParam(':dis_uuid', $dis_uuid, PDO::PARAM_STR);
    $sth->execute();
    $sub_dis_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    return $sub_dis_list;
}

function getAccountInfo($account_uuid)
{
    global $db;

    $sth = $db->prepare("select ID,Account,Grade,Role,ParentID,Location,Info,SipPrefix,Special,Phone,TimeZone,HouseCount,CustomizeForm,ManageGroup,ChargeMode,SipType,Initialization,Language,SendExpireEmailType,Flags,CreateTime,SendRenew,ParentUUID from Account where UUID = :account_uuid");
    $sth->bindParam(':account_uuid', $account_uuid, PDO::PARAM_STR);
    $sth->execute();
    return $sth->fetch(PDO::FETCH_ASSOC);
}

function GenerateUUID($server_tag)
{
    global $db;

    $sth = $db->prepare('select uuid() as uuid');
    $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    $uuid = $data['uuid'];
    return $server_tag.'-'.str_replace('-', '', $uuid);
}

//根据UUID获取社区主账号信息
function GetCommMasterAccountByUUID($uuid)
{
    global $db;

    $sth = $db->prepare("select Account,UserInfoUUID,ParentUUID,Language,UUID,UnitID from PersonalAccount where UUID = :uuid and Role = :role");
    $sth->bindValue(':uuid', $uuid, PDO::PARAM_STR);
    $sth->bindValue(':role', ACCOUNT_ROLE_COMMUNITY_MAIN, PDO::PARAM_INT);
    $sth->execute();
    $master_account = $sth->fetch(PDO::FETCH_ASSOC);
    return $master_account;
}

function getAllCommunityList()
{
    global $db;

    $sth = $db->prepare("select AccountID as CommunityID from CommunityInfo;");
    $sth->execute();
    return $sth->fetchALL(PDO::FETCH_ASSOC);
}

function getOldOfficeUserRoleStr()
{
    return ACCOUNT_ROLE_OFFICE_MAIN . "," . ACCOUNT_ROLE_OFFICE_ADMIN;
}

function getNewOfficeUserRoleStr()
{
    return ACCOUNT_ROLE_OFFICE_NEW_PER;
}

function GetVideoStorageExpireEndUserList($expire_list)
{
    $expire_end_user_list = array();
    $autopay_type = AUTO_PAY_ORDER_TYPE_SINGLE_VIDEO_STORAGE;
    foreach ($expire_list as $end_user)
    {
        if (!IsUserEnableVideoStorageAutoPay($end_user['UUID'], $autopay_type))
        {
            array_push($expire_end_user_list, $end_user);
        }
    }
    return $expire_end_user_list;
}

// 判断 subdis 是否有权限管理 ins
function GetSubDisMngListByInstallerUUID($ins_uuid)
{
    global $db;

    $sth = $db->prepare("select ID,UUID,DistributorUUID from SubDisMngList where InstallerUUID = :ins_uuid");
    $sth->bindValue(':ins_uuid', $ins_uuid, PDO::PARAM_STR);
    $sth->execute();

    return $sth->fetch(PDO::FETCH_ASSOC);
}

function getPersonalAccountInfobyPersonalAccountUUID($personal_account_uuid)
{
    global $medooDb;
    
    $personalAccount = $medooDb->get("PersonalAccount", [
        "Language",
        "RoomNumber",
        "Name",
        "UserInfoUUID"
    ], [
        "UUID" => $personal_account_uuid
    ]);
    if (!$personalAccount) {
        return []; 
    }
    $userInfoUUID = $personalAccount['UserInfoUUID'];

    if (!empty($userInfoUUID)) {
        $personalAccountUserInfo = $medooDb->get("PersonalAccountUserInfo", [
            "Email",
            "MobileNumber"
        ], [
            "UUID" => $userInfoUUID
        ]);
    } else {
        $personalAccountUserInfo = [];
    }
    $result = array_merge($personalAccountUserInfo, $personalAccount);
    
    return $result;
    
}