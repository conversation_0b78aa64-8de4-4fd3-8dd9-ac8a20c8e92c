<?php
/*
 * @Description: 操作用户
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors  : kxl
 */
date_default_timezone_set("PRC");
include_once __DIR__."/util/time.php";
require_once(__DIR__.'/../common/define.php');
require_once(__DIR__.'/../common/comm.php');

function CreateTempLog($rootdir, $excel_file, $data_arr, $content, $s3_cfg)
{
    global $MSGTEXT_FRONT;
    global $MSGTEXT_BACK;

    $timeZone = $content["time_zone"];
    $customizeForm = $content["time_form"];
    $export_type = $content["export_type"];
    $statusTextArr = [$MSGTEXT_BACK["normal"], $MSGTEXT_BACK["abnormal"], $MSGTEXT_BACK["low"]];
    if (!is_dir($rootdir)) {
        mkdir($rootdir, 0777, true);
    }

    // 转换时区、处理数据
    // $data_arr = \util\time\setQueryTimeZone($data_arr, $timeZone, $customizeForm);

    $excel_data = [];
    $excel_header = [
        $MSGTEXT_FRONT["CaptureListTanleCaptureTime"],
        $MSGTEXT_FRONT["ProperAllTextBodyTemperature"],
        $MSGTEXT_FRONT["MulListUpdateListTanleStatus"],
        $MSGTEXT_FRONT["TabelUpdateBoxDevice"],
        $MSGTEXT_FRONT["capture"]
    ];

    array_push($excel_data, $excel_header);
    foreach ($data_arr as $key => $val) {
        $time = $val['CaptureTime'];
        $file_time = date("YmdHis", strtotime($time));
        $data_dir = date("Ymd", strtotime(explode(" ", $time)[0]));
        $time = \util\time\setTimeZone($time, $timeZone, $customizeForm);

        $name = $val["Location"];
        $name = str_replace("/", "", $name);
        $name = str_replace("\\", "", $name);
        $csvname = str_replace(",", "-", $val["Location"]);

        $pic = "";
        if (strlen($val["PicUrl"]) > 0)
        {
            $pic = $file_time."+".$val["ID"]."+$name.jpg";
        }

        $status = $statusTextArr[$val["Status"]];
        $temprature = number_format($val["Fahrenheit"], 1) . "℃ / " . number_format($val["Fahrenheit"] * 9 / 5 + 32, 1) . "℉";
        array_push($excel_data, array($time, $temprature, $status, $csvname, $pic));

        // 下载图片
        if ($export_type == EXPORTLOG_TYPE_ALL) {
            $pic_dir = $rootdir . "/" . $data_dir;
            if (!is_dir($pic_dir)) {
                mkdir($pic_dir, 0777, true);
            }
            DownloadPic($s3_cfg, $val["PicUrl"], $pic, $pic_dir, $val['CaptureTime']);
        }

    }

    LOG_INFO("Export temperature log: file=$rootdir/$excel_file, count=" . count($excel_data) - 1);
    createExcelFormData($excel_data, $rootdir, $excel_file);
    $excel_data = null;
}

function getTableTemperatureDatas($tables, $datas, $limit)
{
    $startTime = $datas["export_startime"];
    $endTime = $datas["export_endtime"];
    $timeZone = $datas["time_zone"];
    $customizeForm = $datas["time_form"];
    $communit_id = $datas["communit_id"];

    $db = getDB();
    $count = 0;
    $left = $limit;
    $result = [];
    foreach ($tables as $table) {
        if (!logTableExist($table)) {
            continue;
        }
        if ($left <= 0) {
            LOG_WARING("1.export log cloumn limit exceeded.");
            ;
            break;
        }
        //查询出token是否存在
        $sth = $db->prepare("select C.*,D.Location from $table C left join Devices D on C.MAC = D.MAC where C.MngAccountID = :MngAccountID and C.CaptureTime >= :StartTime and  C.CaptureTime <= :EndTime order by ID desc limit $left");
        $sth->bindParam(':MngAccountID', $communit_id, PDO::PARAM_STR);
        $sth->bindParam(':StartTime', $startTime, PDO::PARAM_STR);
        $sth->bindParam(':EndTime', $endTime, PDO::PARAM_STR);

        $ret = $sth->execute();
        $data = $sth->fetchALL(PDO::FETCH_ASSOC);
        $count = $count + count($data);
        $left = $left - $count;
        $result = array_merge($result, $data);
    }
    if ($left <= 0) {
        LOG_WARING("2.export log cloumn limit exceeded.");#不考虑5000张 刚好就是5000张得零界点
    }

    $db = null;
    return $result;
}


function ExportLogTemperature($datas, $s3_cfg)
{
    $trace_id = $datas["trace_id"];
    $timeZone = $datas["time_zone"];
    $communit_id = $datas["communit_id"];
    $export_type = $datas["export_type"];
    $startTime = $datas["export_startime"];
    $endTime = $datas["export_endtime"];
    $lang = $datas["language"];
    $today = date("Ymd");
    reload_language($lang);
    global $MSGTEXT_FRONT;

    $month = date("Ym", strtotime("-1 month"));
    $tables = ["Temperature"];

    $index = 1;
    if ($export_type == EXPORTLOG_TYPE_EXCEL_ONLY) {
        $sql_data = getTableTemperatureDatas($tables, $datas, DOWNLOAD_CSV_LIMIT);
        $sql_datas = array_chunk($sql_data, DOWNLOAD_CSV_LIMIT);
    } else {
        $sql_data = getTableTemperatureDatas($tables, $datas, DOWNLOAD_PIC_LIMIT);
        $sql_datas = array_chunk($sql_data, PER_DIR_PIC_LIMIT);
    }
    $file_urls = [];
    foreach ($sql_datas as $key => $value) {
        $file_start_time = \util\time\setTimeZone($startTime, $timeZone, "", "+");
        $file_end_time = \util\time\setTimeZone($endTime, $timeZone, "", "+");
        $file_start_time = explode(" ", $file_start_time)[0];
        $file_end_time = explode(" ", $file_end_time)[0];

        $file_start_time = str_replace("-", "", $file_start_time);
        $file_end_time = str_replace("-", "", $file_end_time);

        $dir = getLogDir($trace_id, $MSGTEXT_FRONT["ProperAllTextTemperatureLogs"], $index, $file_start_time, $file_end_time);
        $zip_save_path = $dir[FILE_ZIP_PATH];
        $csvpath = $dir[FILE_CSV_PATH];
        $zipname = $dir[FILE_ZIP_NAME];
        $csvname = $dir[FILE_CSV_NAME];
        $savedir = $dir[FILE_SAVE_DIR];
        CreateTempLog($savedir, $csvname, $value, $datas, $s3_cfg);

        $index = $index + 1;
        //压缩文件夹
        if ($export_type == EXPORTLOG_TYPE_ALL) {
            zipDir($zip_save_path, $dir[FILE_ROOT], $dir[FILE_ZIP_DIR]);
            $oss_url = upload_oss($s3_cfg, $zip_save_path, "PmExportLog/$today/temperature/$trace_id/$zipname");
            $url = [];
            $url[$zipname] = $oss_url;
            array_push($file_urls, $url);
        } else {
            $oss_url = upload_oss($s3_cfg, $csvpath, "PmExportLog/$today/temperature/$trace_id/$csvname");
            updateExcelUrl($trace_id, $oss_url);
        }
    }

    $sql_datas = null;
    $retinfo["urls"] = $file_urls;
    $retinfo["result"] = 0;
    return $retinfo;
}
