<?php
require_once(dirname(__FILE__) . '/expireDataCheckUtil.php');
require_once(dirname(__FILE__) . '/../model/residentAppQuery.php');

class ResidentAppControl extends ExpireDataCheckUtil
{
    public $commonQuery;
    private $residentAppQuery;

    public function __construct($db, $medooDb, $commonQuery)
    {
        parent::__construct($db, $medooDb);
        $this->commonQuery = $commonQuery;
        $this->residentAppQuery = new ResidentAppQuery($db, $medooDb);
    }

    public function getExpireData($daysBefore)
    {
        // 获取过期数据
        $expireData = $this->residentAppQuery->getExpireData($daysBefore);

        // 过滤掉迁移dis的社区
        $expireData = $this->filterMigrateDistributor($expireData);

        // 过滤掉自动扣费的用户
        $expireData = $this->filterAutoPayUser($expireData);
        
        // 获取用户的家居开关
        $expireData = $this->getSmartHomeUser($expireData);
        return $expireData;
    }

    private function getSmartHomeUser($expireData)
    {
        $smartHomeData = [];
        foreach ($expireData as $data) {
            // 判断社区家居开关和用户apt家居开关是否开启
            $communityEnableSmartHome = switchHandle($data['Switch'], COMMUNITY_SWITCH_SMARTHOME);

            // 在data中添加智能家居开启状态标识
            if ($communityEnableSmartHome && $data['EnableSmartHome']) {
                $data['EnableSmartHome'] = 1;
            } else {
                $data['EnableSmartHome'] = 0;
            }
            
            $smartHomeData[] = $data;
        }
        return $smartHomeData;
    }
}