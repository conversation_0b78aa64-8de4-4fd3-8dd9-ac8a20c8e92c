<?php

  global $MSGTEXT_BACK;

  $MSGTEXT_BACK = [ 
"unlockHandset"=>"Handset",
"entry"=>"Entry",
"failed"=>"Failed",
"doorRelease"=>"Door Release",
"success"=>"Success",
"textUpgradeMsg2"=>"Login failed",
"normal"=>"Normal",
"low"=>"Low",
"abnormal"=>"Abnormal",
"automaticallyUnlock"=>"Automatically Unlock",
"auditCodeManuallyUnlock"=>"Manually Unlock",
"auditCodeManuallyLock"=>"Manually Lock",
"unlockBLE"=>"Bluetooth Unlock",
"call"=>"Call",
"exit"=>"Exit",
"unlockNFC"=>"NFC Unlock",
"addTmpKeyFail"=>"Add TEMP key failed, please try it again",
"BindingDeviceFailed"=>"Bind device failed, the device may be bonded to other user or haven't added to MAC library ",
"chcekMacExits"=>"Adding failed, MAC address invalid or already exists",
"changePasswdFail"=>"Modifying password failed",
"importDataSuccess"=>"Import data success",
"importFailMACExit"=>"Import failed, please check whether the MAC address exists or valid:\r\n%s",
"invalidPCalltype"=>"Invalid call type %s",
"modifyAptFail"=>"Saving failed!the APT No. already exists, you should delete it firstly.",
"userMaxPLimt"=>"Create failed, you can only add up to %s family members",
"sipStatus"=>"SIP account assignment failed, please it try again",
"unlockApp"=>"SmartPlus Unlock",
"unlockIndoor"=>"Indoor Monitor Unlock",
"unlockCard"=>"RF Card Unlock",
"unlockPrivateKey"=>"PIN Code Unlock",
"unlockTempKey"=>"Temp Key Unlock",
"unlockExitButton"=>"Exit Button",
"unlockLicensePlate"=>"License Plate Unlock",
"noAnswer"=>"No Answer",
"unlockFACE"=>"Face Unlock",
"captureSmartPlus"=>"Capture on SmartPlus",
"failedImport"=>"Import failed",
"auditSetCallTypeSmartPlusIndoor"=>"Set call type smartPlus and indoor monitors: {0}&{1}",
"auditSetCallTypePhoneIndoor"=>"Set call type phone and indoor monitors: {0}&{1}",
"auditSetCallTypeSmartPlusIndoorBackup"=>"Set call type smartPlus and indoor monitors, with phone as backup: {0}&{1}",
"auditSetCallTypeSmartPlusBackup"=>"Set call type indoor monitors with SmartPlus as backup: {0}&{1}",
"auditSetCallTypeIndoorPhoneBackup"=>"Set call type indoor monitors with phone as backup: {0}&{1}",
"auditSetCallTypeIndoorSmartPlusPhone"=>"Set call type indoor monitors with SmartPlus as backup, finally the phone: {0}&{1}",
"cancelError"=>"Cancel failed.",
"importFailTooManyAdd"=>"Import failed,for single distributor only.",
"replaceBatteryWarning"=>"%s - Battery level is extremely low, please replace immediately.",
"addContactFavoriteNum"=>"Adding to favorites failed.you can only add up to 300 favorite apartments.",
"addContactBlockNum"=>"Adding to blocklist failed.you can only add up to 100 apartments to blocklist.",
"faceImportErrorLowResolution"=>"Resolution is too low",
"changeHomeFeatureInvalid"=>"Operation failed! There are installers of this distributor using the 'Home Automation' feature.",
"changeInterComFeatureInvalid"=>"Operation failed! There are installers of this distributor using the 'Intercom' feature.",
"screenSaverDevicesOffline"=>"Save failed. The device is offline.",
"saveFailed"=>"Save failed.",
"importNoActiveMasterLine"=>"Importing failed in line %s, please activate the family matser first.",
"importNoCreateMasterLine"=>"Importing failed in line %s, please create the family matser first.",
"landLineOpenToClosedFail"=>"Save failed. Please make sure the call type of the apartments in the project are \"SmartPlus and indoor monitor\" or \"Indoor monitors with SmartPlus as backup\"",
"importLatestTemplate"=>"Import failed, Please download the latest template for import.",
"projectOpenLandLine"=>"Save failed. Please make sure that the landline service of all projects under this account are off:%s",
"CancelSubscriptionFail"=>"The subscription cancellation failed",
"companyDeleteError"=>"Deletion failed. Please delete the associated project first.",
"unlockBooking"=>"Amenity Reservation",
"unlockIDAccess"=>"ID Access",
"entryViolation"=>"Entry Violation",
"exitViolation"=>"Exit Violation",
"dormakabaLowBattery"=>"%s - Lock battery is running low, please replace immediately.  ",
"textUpgradeMsg3"=>"Login failed. Please update the app to the latest version.",
"OperationFailed"=>"Operation failed!",
"iqPinVerificationFailed"=>"IQ PIN verification failed.",
"pmBindRentManagerCustomer"=>"Deleting failed! The property manager has been bound to a RentManager Customer, you should delete it firstly.",
"installerBindRentManagerCustomer"=>"Deleting failed! The installer has been bound to a RentManager Customer, you should delete it firstly.",

];