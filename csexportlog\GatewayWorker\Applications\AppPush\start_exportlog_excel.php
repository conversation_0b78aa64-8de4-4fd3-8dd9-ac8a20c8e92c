<?php
/**
 * This file is part of workerman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link http://www.workerman.net/
 * @license http://www.opensource.org/licenses/mit-license.php MIT License
 */
use \Workerman\Worker;
use \Workerman\Autoloader;

require_once __DIR__ . '/exportlog/consumer_kafka.php';
require_once __DIR__ . '/exportlog/common/log_db_comm.php';

$capture_slice_info = getLogDeliveryInfo("PersonalCapture");
$motion_slice_info = getLogDeliveryInfo("PersonalMotion");
$call_slice_info = getLogDeliveryInfo("CallHistory");

$consumer = new Worker();
$consumer->count = 1;

$consumer->onWorkerStart = function ($consumer) {
    while (1) {
        $consumer = new AkKafkaConsumer(KAFKA_EXPORTLOG_EXCEL_TOPIC, KAFKA_EXPORTLOG_EXCEL_CONSUMER);
        $consumer->Start();
    }
};

// 如果不是在根目录启动，则运行runAll方法
if (!defined('GLOBAL_START')) {
    Worker::runAll();
}
