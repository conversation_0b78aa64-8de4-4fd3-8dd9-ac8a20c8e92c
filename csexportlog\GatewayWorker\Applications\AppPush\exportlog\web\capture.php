<?php
/*
 * @Description: 操作用户
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors  : kxl
 */
date_default_timezone_set("PRC");
include_once __DIR__."/util/time.php";
require_once(__DIR__.'/../common/define.php');
require_once(__DIR__.'/../common/comm.php');
require_once(__DIR__.'/../common/log_db_comm.php');


function CreateCaptureLog($rootdir, $excel_file, $data_arr, $content, $s3_cfg)
{
    global $MSGTEXT_FRONT;
    global $MSGTEXT_BACK;

    $timeZone = $content["time_zone"];
    $customizeForm = $content["time_form"];
    $export_type = $content["export_type"];
    $lang = $content["language"];
    if (!is_dir($rootdir)) {
        mkdir($rootdir, 0777, true);
    }

    $CaptureTypes = [
        "0" => $MSGTEXT_BACK["call"],
        "1" => $MSGTEXT_BACK["unlockTempKey"],
        "2" => $MSGTEXT_BACK["unlockPrivateKey"],
        "3" => $MSGTEXT_BACK["unlockCard"],
        "4" => $MSGTEXT_BACK["unlockFACE"],
        "5" => $MSGTEXT_BACK["unlockApp"],
        "6" => $MSGTEXT_BACK["unlockApp"],
        "7" => $MSGTEXT_BACK["unlockIndoor"],
        "11" => $MSGTEXT_FRONT["DeliverySystemVerification"],
        "12" => $MSGTEXT_BACK["unlockBooking"],
        "13" => $MSGTEXT_BACK["unlockIDAccess"],
        "14" => $MSGTEXT_BACK["unlockExitButton"],
        "15" => $MSGTEXT_BACK["unlockHandset"],
        "16" => $MSGTEXT_BACK["unlockLicensePlate"],
        "99" => "",
        "100" => $MSGTEXT_BACK["unlockNFC"],
        "101" => $MSGTEXT_BACK["unlockBLE"],
        "102" => $MSGTEXT_BACK["captureSmartPlus"],
        "103" => $MSGTEXT_BACK["captureSmartPlus"]
    ];

    // 转换时区、处理数据
    // $data_arr = \util\time\setQueryTimeZone($data_arr, $timeZone, $customizeForm);

    $excel_data = [];
    $excel_header = [
        $MSGTEXT_FRONT["CaptureListTanleCaptureTime"],
        $MSGTEXT_FRONT["TabelUpdateBoxDevice"],
        $MSGTEXT_FRONT["CaptureListTanleInitiator"],
        $MSGTEXT_FRONT["CaptureListTanleAction"],
        $MSGTEXT_FRONT["capture"]
    ];

    array_push($excel_data, $excel_header);
    foreach ($data_arr as $key => $val) {
        $time = $val['CaptureTime'];
        $file_time = date("YmdHis", strtotime($time));
        $data_dir = date("Ymd", strtotime(explode(" ", $time)[0]));

        $time = \util\time\setTimeZone($time, $timeZone, $customizeForm);

        $name = $val["Location"];
        $name = str_replace("/", "", $name);
        $name = str_replace("\\", "", $name);
        $csvname = str_replace(",", "-", $val["Location"]);

        $pic = "";
        if (strlen($val["PicUrl"]) > 0)
        {
            $pic = $file_time."+".$val["ID"]."+$name.jpg";
        }
        // 下载图片
        if ($export_type == EXPORTLOG_TYPE_ALL) {
            $pic_dir = $rootdir . "/" . $data_dir;
            if (!is_dir($pic_dir)) {
                mkdir($pic_dir, 0777, true);
            }
            DownloadPic($s3_cfg, $val["PicUrl"], $pic, $pic_dir, $val['CaptureTime']);
        }
        array_push($excel_data, array($time, $csvname, $val["Initiator"], $CaptureTypes[$val['CaptureType']], $pic));
    }

    LOG_INFO("Export capture log: file=$rootdir/$excel_file, count=" . count($excel_data) - 1);
    createExcelFormData($excel_data, $rootdir, $excel_file);
    $excel_data = null;
}

function getTableCaptureDatas($log_tables, $datas, $limit)
{
    $left = $limit;
    $result = [];
    $akcs_result = [];

    if ($log_tables != null) {
        $db_log_data = getLOGTableCapturelogDatas($log_tables, $datas, $left, $result);
        $result = array_merge($result, $db_log_data);
    }
    
    return $result;
}

function getLOGTableCapturelogDatas($log_tables, $datas, &$left, $result)
{
    $dbNode = getLogDbNode($datas['project_uuid']);
    $db = getLogDB($dbNode);
    $count = 0;
    foreach ($log_tables as $table) {
        if (!LOGDBTableExist($table)) {
            continue;
        }
        if ($left <= 0) {
            LOG_WARING("1.export LOG.PersonalCapture cloumn limit exceeded.");
            break;
        }

        //查询出token是否存在
        $one_table = getLOGDBCapturelogDatas($db, $table, $count, $left, $result, $datas);
        $result = array_merge($result, $one_table);
    }
    if ($left <= 0) {
        LOG_WARING("2.export LOG.PersonalCapture cloumn limit exceeded.");#不考虑刚好就是5000张得零界点
    }
    return $result;
}

function getAKCSTableCapturelogDatas($akcs_tables, $datas, &$left, $result)
{
    $db = getDB();
    $count = 0;
    foreach ($akcs_tables as $table) {
        if (!logTableExist($table)) {
            continue;
        }
        if ($left <= 0) {
            LOG_WARING("1.export AKCS.PersonalCapture cloumn limit exceeded.");
            break;
        }

        //查询出token是否存在
        $one_table = getCapturelogDatas($db, $table, $count, $left, $result, $datas);
        $result = array_merge($result, $one_table);
    }
    if ($left <= 0) {
        LOG_WARING("2.export AKCS.PersonalCapture cloumn limit exceeded.");#不考虑刚好就是5000张得零界点
    }

    $db = null;
    return $result;
}

function getCapturelogDatas($db, $table, &$count, &$left, $result, $datas)
{
    //查询出token是否存在
    $sth = $db->prepare("select C.*,B.UnitName,D.Location from $table C left join Devices D on C.MAC = D.MAC left join CommunityUnit B on B.ID = D.UnitID where C.MngAccountID = :MngAccountID and C.CaptureTime >= :StartTime  and  C.CaptureTime <= :EndTime  and C.CaptureType = 102  order by ID limit $left");
    $sth->bindParam(':MngAccountID', $datas["communit_id"], PDO::PARAM_STR);
    $sth->bindParam(':StartTime', $datas["export_startime"], PDO::PARAM_STR);
    $sth->bindParam(':EndTime', $datas["export_endtime"], PDO::PARAM_STR);

    $ret = $sth->execute();
    $data = $sth->fetchALL(PDO::FETCH_ASSOC);
    $count = $count + count($data);
    $left = $left - $count;
    return $data;
}

function getLOGDBCapturelogDatas($db, $table, &$count, &$left, $result, $datas)
{
    //查询出token是否存在
    $sth = $db->prepare("select * from $table where MngAccountID = :MngAccountID and CaptureTime >= :StartTime  and  CaptureTime <= :EndTime  and CaptureType = 102 order by ID desc limit $left");
    $sth->bindParam(':MngAccountID', $datas["communit_id"], PDO::PARAM_STR);
    $sth->bindParam(':StartTime', $datas["export_startime"], PDO::PARAM_STR);
    $sth->bindParam(':EndTime', $datas["export_endtime"], PDO::PARAM_STR);

    $ret = $sth->execute();
    $data = $sth->fetchALL(PDO::FETCH_ASSOC);
    $akcs_db = getDB();
    foreach ($data as $key => $value){
        $sth = $akcs_db->prepare("select B.UnitName,D.Location from Devices D left join CommunityUnit B on B.ID = D.UnitID where D.MAC = :mac");
        $sth->bindParam(':mac', $value["MAC"], PDO::PARAM_STR);
        $ret = $sth->execute();
        $result = $sth->fetch(PDO::FETCH_ASSOC);
        $data[$key]['UnitName'] = $result['UnitName'];
        $data[$key]['Location'] = $result['Location'];
    }

    $akcs_db = null;
    $count = $count + count($data);
    $left = $left - $count;
    return $data;
}

function ExportLogCapture($datas, $s3_cfg)
{
    $trace_id = $datas["trace_id"];
    $timeZone = $datas["time_zone"];
    $customizeForm = $datas["time_form"];
    $communit_id = $datas["communit_id"];
    $export_type = $datas["export_type"];
    $startTime = $datas["export_startime"];
    $endTime = $datas["export_endtime"];
    $lang = $datas["language"];
    $today = date("Ymd");
    reload_language($lang);
    global $MSGTEXT_FRONT;

    //$month = date("Ym", strtotime("-1 month"));
    //$tables = ["PersonalCapture_".$month, "PersonalCapture"];

    $project_uuid = getProjectUUID($communit_id);
    $datas["project_uuid"] = $project_uuid;
    $log_tables = [];
    getSqlTables("PersonalCapture", $project_uuid, $log_tables);

    $index = 1;
    if ($export_type == EXPORTLOG_TYPE_EXCEL_ONLY) {
        $sql_data = getTableCaptureDatas($log_tables, $datas, DOWNLOAD_CSV_LIMIT);
        $sql_datas = array_chunk($sql_data, DOWNLOAD_CSV_LIMIT);
    } else {
        $sql_data = getTableCaptureDatas($log_tables, $datas, DOWNLOAD_PIC_LIMIT);
        $sql_datas = array_chunk($sql_data, PER_DIR_PIC_LIMIT);
    }
    $file_urls = [];
    foreach ($sql_datas as $key => $value) {
        $file_start_time = \util\time\setTimeZone($startTime, $timeZone, "", "+");
        $file_end_time = \util\time\setTimeZone($endTime, $timeZone, "", "+");
        $file_start_time = explode(" ", $file_start_time)[0];
        $file_end_time = explode(" ", $file_end_time)[0];

        $file_start_time = str_replace("-", "", $file_start_time);
        $file_end_time = str_replace("-", "", $file_end_time);
        $dir = getLogDir($trace_id, $MSGTEXT_FRONT["capture"], $index, $file_start_time, $file_end_time);
        $zip_save_path = $dir[FILE_ZIP_PATH];
        $csvpath = $dir[FILE_CSV_PATH];
        $zipname = $dir[FILE_ZIP_NAME];
        $csvname = $dir[FILE_CSV_NAME];
        $savedir = $dir[FILE_SAVE_DIR];
        CreateCaptureLog($savedir, $csvname, $value, $datas, $s3_cfg);

        $index = $index + 1;
        //压缩文件夹
        if ($export_type == EXPORTLOG_TYPE_ALL) {
            zipDir($zip_save_path, $dir[FILE_ROOT], $dir[FILE_ZIP_DIR]);
            $oss_url = upload_oss($s3_cfg, $zip_save_path, "PmExportLog/$today/capture/$trace_id/$zipname");

            $url[$zipname] = $oss_url;
            array_push($file_urls, $url);
        } else {
            $oss_url = upload_oss($s3_cfg, $csvpath, "PmExportLog/$today/capture/$trace_id/$csvname");
            updateExcelUrl($trace_id, $oss_url);
        }
    }

    $sql_datas = null;
    $retinfo["urls"] = $file_urls;
    $retinfo["result"] = 0;
    return $retinfo;
}
