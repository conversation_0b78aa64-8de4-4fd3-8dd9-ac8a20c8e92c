HTML.CoreModules
TYPE: lookup
VERSION: 2.0.0
--DEFAULT--
array (
  'Structure' => true,
  'Text' => true,
  'Hypertext' => true,
  'List' => true,
  'NonXMLCommonAttributes' => true,
  'XMLCommonAttributes' => true,
  'CommonAttributes' => true,
)
--DESCRIPTION--

<p>
    Certain modularized doctypes (XHTML, namely), have certain modules
    that must be included for the doctype to be an conforming document
    type: put those modules here. By default, XHTML's core modules
    are used. You can set this to a blank array to disable core module
    protection, but this is not recommended.
</p>
--# vim: et sw=4 sts=4
