<?php

date_default_timezone_set("PRC");
require_once(__DIR__ . '/define.php');
include_once __DIR__ . "/../util/time.php";
require_once(__DIR__ . '/../../common/comm.php');
require_once(__DIR__ . '/../../common/define.php');
require_once(__DIR__ . '/../../common/log_db_comm.php');
require_once(__DIR__ . '/office_admin/export_call_log.php');
require_once(__DIR__ . '/property_manager/export_call_log.php');

/**
 * @brief NewOffice导出日志
 */
function NewofficeExportCallLog($datas, $s3_cfg)
{
    $operator_type = $datas["operator_type"];

    if ($operator_type == OPERATOR_TYPE_ADMIN) {
        return newoffice\office_admin\call\NewofficeAdminExportCallHistory($datas, $s3_cfg);
    } else if ($operator_type == OPERATOR_TYPE_PM) {
        return newoffice\property_manager\call\NewofficePmExportCallHistory($datas, $s3_cfg);
    } else {
        LOG_ERROR("Unsupported operator_type: $operator_type.");
        $retinfo["result"] = -1;
        $retinfo["urls"] = [];
        return $retinfo;
    }
}
