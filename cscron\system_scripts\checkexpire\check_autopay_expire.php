<?php
require_once(dirname(__FILE__).'/check_expire_common_v4500.php');
require_once(dirname(__FILE__).'/check_autopay_common.php');
require_once(dirname(__FILE__).'/time.php');
LOG_INFO("checkAutoPayExpire begin");
function checkAutoPayExpire($before)
{
    //获取三天即将扣费且apt以及金额没有变化的订单
    $order_list = getWillChargeOrderList($before);
    foreach ($order_list as $row => $order) {
        AutoPayNotify($order, $before);
        usleep(500 * 1000); //500ms
    }
}

//自动扣费邮件通知
function AutoPayNotify($order, $before)
{
    $project_name_str = getProjectNameStrByOrderInfo($order);
    $project_type = GetProjectTypeByOrderInfo($order);
    $is_batch = $order['IsBatch'];
    $email_info = array();

    if (!$is_batch && ($project_type == PROJECT_TYPE_RESIDENCE || $project_type == PROJECT_TYPE_OFFICE)) {
        $email_info['community'] = $project_name_str;
    }
    
    $email_info['project_type'] = $project_type;
    $email_info['content_project'] = $project_name_str;
	//数据库保存的TotalPrice是实际金额*100的值，实际展示需要换算回来
    $email_info['pay_amount'] = $order['TotalPrice'] / 100;

    if ($order['NextPayTime']) {
        $next_pay_date = setTimeZone($order['NextPayTime'], $order['TimeZone'], 3, "+");
        $next_datetime = DateTime::createFromFormat("Y-m-d H:i:s", $next_pay_date);
        $next_pay_date = $next_datetime->format("Y-m-d");
    } else {
        $next_pay_date = "--";
    }

    if ($order['LastPayTime']) {
        $last_pay_date = setTimeZone($order['LastPayTime'], $order['TimeZone'], 3, "+");
        $last_datetime = DateTime::createFromFormat("Y-m-d H:i:s", $last_pay_date);
        $last_pay_date = $last_datetime->format("Y-m-d");
    } else {
        $last_pay_date = "--";
    }

    $email_info['next_pay_date'] = $next_pay_date;
    $email_info['last_pay_date'] = $last_pay_date;
    $email_info['pay_method'] = $order['PayPlatform'] == 0 ? "Paypal" : "Stripe";
    getPayerEmailInfoByUUID($order['PayerUUID'], $order['PayerType'], $email_info);
    $email_info['email_type'] = "auto_pay_remind";
    $email_info['before'] = $before;
    $email_info['project_uuid'] = $order['ProjectUUID'];
    
    sendEmailNotify($email_info);
}

$medooDb = getMedooDb();
$db = $medooDb->pdo;

checkAutoPayExpire(3);
