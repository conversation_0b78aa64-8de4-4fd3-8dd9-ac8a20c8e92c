<?php

class InstallerHandler 
{
    private $daysBefore;
    private $expireData;
    private $communityExpireInfoList;
    private $communityChargeModeMap;
    public $expireDataControl; 

    public function __construct() {
    }
    
    public function setExpireParams($daysBefore, $expireData, $communityExpireInfoList, $communityChargeModeMap, $expireDataControl) {
        $this->daysBefore = $daysBefore;
        $this->expireData = $expireData;
        $this->communityExpireInfoList = $communityExpireInfoList;
        $this->communityChargeModeMap = $communityChargeModeMap;
        $this->expireDataControl = $expireDataControl;
    }

    public function filterData() {
        // 判断社区的付费模式 是否要发送给Installer
        $filteredExpireData = $this->filterCommunityChargeMode();
        return $filteredExpireData;
    }

    // 判断社区的付费模式
    private function filterCommunityChargeMode() {
        // 判断社区的付费模式 是否要发送给Installer
        $payTypeList = array(
            EmailNotifyRule::PAY_ITEM_USER_APP,
            EmailNotifyRule::PAY_ITEM_PM_APP,
            EmailNotifyRule::PAY_ITEM_VIDEO_RECORD,
            EmailNotifyRule::PAY_ITEM_FEATURE_PLAN,
            EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT,
        );

        // 获取每种付费类型发送给Installer的社区列表
        $payTypeCommunityUUIDMap = array(); 
        foreach ($payTypeList as $payType) {
            foreach ($this->communityExpireInfoList as $communityInfo) {
                $communityChargeMode = $this->communityChargeModeMap[$communityInfo['UUID']];
                if ($communityChargeMode && CommunityCheck::needNotifyIns($payType, $communityChargeMode, $this->daysBefore)) {
                    $payTypeCommunityUUIDMap[$payType][] = $communityInfo['UUID'];
                }
            }
        }

        // 获取每种付费类型发送给Installer的社区列表
        $filteredExpireData = array();
        foreach ($this->expireData as $payType => $expireList) {
            foreach ($expireList as $expireInfo) {
                // 判断是否在payTypeCommunityUUIDMap中
                if (isset($payTypeCommunityUUIDMap[$payType]) && in_array($expireInfo['CommunityUUID'], $payTypeCommunityUUIDMap[$payType])) {
                    $filteredExpireData[$payType][] = $expireInfo;
                }
            }
        }
        return $filteredExpireData;
    }

    public function sendEmail($emailDataMap, $recevierInfoMap) {
        foreach ($emailDataMap as $insUUID => $insEmailData) {
            $insEmail = $recevierInfoMap[$insUUID]['email'];
            $loginAccount = $recevierInfoMap[$insUUID]['loginAccount'];
            if (empty($insEmail)) {
                LOG_INFO("insEmail is empty, skip, insUUID: " . $insUUID);
                continue;
            }
            
            // ins下同一个时区的社区一起发送
            $timezoneEmailDataMap = $this->getTimeZoneEmailDataMap($insEmailData);
            foreach ($timezoneEmailDataMap as $timeZone => $emailData) {
        
                $emailInfo['email'] = $insEmail;
                $emailInfo["web_domain"] = WEB_DOMAIN;
                $emailInfo['before'] = $this->daysBefore;
                $emailInfo['login_account'] = $loginAccount;
                $emailInfo['oem'] = $recevierInfoMap[$insUUID]['oem'];
                $emailInfo['user'] = $recevierInfoMap[$insUUID]['user'];
                $emailInfo['language'] = $recevierInfoMap[$insUUID]['language'];
                $emailInfo['is_show_paylink'] = SHOW_PAYLINK_INSTALLER;
    
                if ($this->daysBefore == -1) {
                    $emailInfo['email_type'] = "community_has_expired";
                } else {
                    $emailInfo['email_type'] = "community_will_expire";
                }
                $emailInfo['renew_uuid'] = $this->recordRenewPaymentList($emailData);
    
                $emailInfo['list'] = $emailData;
                sendEmailNotifyNew($emailInfo);
            }
            

        }
    }

    // ins下同一个时区的一起发送
    private function getTimeZoneEmailDataMap($insEmailData) {
        $timeZoneEmailDataMap = array();
        foreach ($insEmailData as $payType => $dataList) {
            foreach ($dataList as $data) {
                $timeZonePrefix = explode(' ', $data['TimeZone'])[0];
                $timeZoneEmailDataMap[$timeZonePrefix][$payType][] = $data;
                LOG_INFO("ins getTimeZoneEmailDataMap data: " . json_encode($data));
            }
        }
        return $timeZoneEmailDataMap;
    }

    private function recordRenewPaymentList($emailData) {
        $communityUUIDList = array();
        foreach ($emailData as $payItem => $dataList) {
            foreach ($dataList as $data) {
                $communityUUIDList[] = $data['CommunityUUID'];
            }
        }

        $renewUUID = $this->expireDataControl->commonQuery->GenerateUUID();
        $communityUUIDList = array_unique($communityUUIDList);
        foreach ($communityUUIDList as $communityUUID) {
            $this->expireDataControl->commonQuery->recordEmailRenewPayment($renewUUID, $communityUUID);
        }

        return $renewUUID;
    }




}