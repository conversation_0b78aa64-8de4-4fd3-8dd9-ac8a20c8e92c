AutoFormat.RemoveEmpty.RemoveNbsp
TYPE: bool
VERSION: 4.0.0
DEFAULT: false
--DESCRIPTION--
<p>
  When enabled, HTML Purifier will treat any elements that contain only
  non-breaking spaces as well as regular whitespace as empty, and remove
  them when %AutoFormat.RemoveEmpty is enabled.
</p>
<p>
  See %AutoFormat.RemoveEmpty.RemoveNbsp.Exceptions for a list of elements
  that don't have this behavior applied to them.
</p>
--# vim: et sw=4 sts=4
