[{"TestName": "RentManagerHasExpired", "PayMode": "INS", "ExpireDays": "-1", "SendType": "SendToIns", "TestResult": [{"EmailType": "rent_customer_has_expired_to_ins", "Email": "$INS_EMAIL"}, {"EmailType": "rent_customer_has_expired_to_pm", "Email": "$PM_EMAIL"}, {"EmailType": "rent_customer_has_expired_to_admin", "Email": "$RENT_ADMIN_EMAIL"}]}, {"TestName": "RentManagerHasExpired", "PayMode": "INS", "ExpireDays": "-1", "SendType": "SendToPM", "TestResult": [{"EmailType": "rent_customer_has_expired_to_ins", "Email": "$INS_EMAIL"}, {"EmailType": "rent_customer_has_expired_to_pm", "Email": "$PM_EMAIL"}, {"EmailType": "rent_customer_has_expired_to_admin", "Email": "$RENT_ADMIN_EMAIL"}]}]