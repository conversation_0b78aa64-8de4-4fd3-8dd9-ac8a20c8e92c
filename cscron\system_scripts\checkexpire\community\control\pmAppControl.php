<?php
require_once(dirname(__FILE__) . '/expireDataCheckUtil.php');
require_once(dirname(__FILE__) . '/../model/pmAppQuery.php');

class PmAppControl extends ExpireDataCheckUtil
{
    public $commonQuery;
    private $pmAppQuery;

    public function __construct($db, $medooDb, $commonQuery)
    {
        parent::__construct($db, $medooDb);
        $this->commonQuery = $commonQuery;
        $this->pmAppQuery = new PmAppQuery($db, $medooDb);
    }

    public function getExpireData($daysBefore)
    {
        // 获取过期数据
        $expireData = $this->pmAppQuery->getExpireData($daysBefore);

        // 过滤掉迁移dis的社区
        $expireData = $this->filterMigrateDistributor($expireData);

        // 过滤掉自动扣费的用户
        $expireData = $this->filterAutoPayUser($expireData);
        return $expireData;
    }
}