<?php


ini_set('date.timezone', 'Asia/Shanghai');
require_once(dirname(__FILE__).'/akcs_log.php');

const ALARM_HANDLE_WAITING_TOLONG = "alarm.csexportlog.handle.waiting_toolong";
const ALARM_HANDLE_HANDLED_TOLONG = "alarm.csexportlog.handle.handle_toolong";
const ALARM_HANDLE_UPLOAD_OSS_ERR = "alarm.csexportlog.handle.upload_oss_err";

function AddMonitorExportlogAlarm($key, $description)
{
    $serverinfo=parse_ini_file("/etc/ip");
    $nsqd_addr = $serverinfo["SERVER_INNER_IP"];

    $data = array();
    $data["node"] = "csexportlog";
    $data["time"] = date('Y-m-d H:i:s');
    $data["description"] = $description;
    $data["key"] = $key;
    $data["ip"] = $serverinfo["SERVERIP"];
    $data["hostname"] = $serverinfo["AKCS_HOSTNAME"];
    $json_data = json_encode($data);

    LOG_INFO("push alarm, key:$key description:$description");

    $cmd="curl -s -d '$json_data' ".'http://'.$nsqd_addr.':8513/pub?topic=akcs_alarm';
    if (!shell_exec($cmd)) {
        LOG_ERROR("push alarm error,description:".$description);
    }
}


function AddMonitorHandleWaitingTooLong($description)
{
    AddMonitorExportlogAlarm(ALARM_HANDLE_WAITING_TOLONG, $description);
}

function AddMonitorHandleTooLong($description)
{
    AddMonitorExportlogAlarm(ALARM_HANDLE_HANDLED_TOLONG, $description);
}

function AddMonitorHandleUploadOSSErr($description)
{
    AddMonitorExportlogAlarm(ALARM_HANDLE_UPLOAD_OSS_ERR, $description);
}
