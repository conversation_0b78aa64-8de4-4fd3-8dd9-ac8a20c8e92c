<?php

require_once(dirname(__FILE__).'/define.php');


#运维的工具
#使用前 Update AppPushToken SET LandlineNotifyStatus = 0
const TMPLOG = "/tmp/landline_push";
function logWrite($content)
{
	file_put_contents(TMPLOG, $content, FILE_APPEND);
	file_put_contents(TMPLOG, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = DB_PASSWORD;
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

shell_exec("touch ". TMPLOG);
chmod(TMPLOG, 0777);
if (file_exists(TMPLOG)) {
	shell_exec("echo > ". TMPLOG);
} 

$pushSocket = socket_create(AF_INET,SOCK_STREAM,SOL_TCP);
if(!$pushSocket){
  echo 'Create socket failed';
  exit;
}
socket_connect($pushSocket, '127.0.0.1', 8000);
	$db = getDB();
    $sth = $db->prepare("SELECT count(*) as cnt FROM AppPushToken WHERE Version >= 6000 And LandlineNotifyStatus = 0");
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);
	$cnt = $ret['cnt'];
	$index = 100;	
	for($i = 0; $i < $cnt; $i=$i+$index){
		$sql = "SELECT A.ID,A.AppType,A.FcmPushToken,A.IOSPushToken,A.Version,A.Language,A.Oem,A.Account,P.Role FROM AppPushToken A LEFT JOIN PersonalAccount P ON A.Account = P.Account
		  WHERE A.Version >= 6000 AND LandlineNotifyStatus = 0 limit ".$i.",".$index;
		$sth = $db->prepare($sql);
		$sth->execute();
		$list = $sth->fetchAll(PDO::FETCH_ASSOC);	
		foreach ($list as $row => $value){
			$sth1 = $db->prepare("Update AppPushToken SET LandlineNotifyStatus = 1 WHERE ID = :id");
			$sth1->bindParam(':id', $value['ID'],PDO::PARAM_INT);
			$sth1->execute();
			if($value['Oem'] != ""){
				continue;
			}
			$role = $value['Role'];
			if($role == 10)
			{
				$sth1 = $db->prepare("SELECT ID FROM PersonalAccount WHERE Account = :account AND Active = 1 AND PhoneExpireTime > now()");
			}
			else if($role == 11)
			{
				$sth1 = $db->prepare("SELECT P.ID FROM PersonalAccount P JOIN PersonalAccount P1 ON P.ID = P1.ParentID WHERE P1.Account = :account AND P.Active = 1 AND P.PhoneExpireTime > now()");
			}
			else if($role == 20)
			{
				$sth1 = $db->prepare("SELECT C.Switch FROM PersonalAccount P JOIN CommunityInfo C ON P.ParentID = C.AccountID    
				WHERE P.Account = :account AND P.Active = 1 AND P.ExpireTime > now()");
			}
			else if($role == 21)
			{
				$sth1 = $db->prepare("SELECT C.Switch FROM PersonalAccount P JOIN PersonalAccount P1 ON P.ID = P1.ParentID JOIN CommunityInfo C ON P.ParentID = C.AccountID    
				WHERE P1.Account = :account AND P.Active = 1 AND P.ExpireTime > now()");
			}
			else
			{
				continue;	
			}
			
			$sth1->bindParam(':account', $value['Account'],PDO::PARAM_STR);
			$sth1->execute();
			$ret = $sth1->fetch(PDO::FETCH_ASSOC);
			if($ret){
				if($role == 20 || $role == 21){
					$landlineSwitch = $ret['Switch'] & 1;
					if($landlineSwitch == 0){
						continue;
					}
				}
				$tmpArr = array();
				$tmpArr['ver'] = 0;	//不加密版本
				$tmpArr['OEM'] = "Akuvox";
				$tmpArr['msg_type'] = "LANDLINE";
				$tmpArr['language'] = $value['Language'];
				$tmpArr['dclient'] = $value['Version'];
				
				if($value['AppType'] == 0)
				{
					$tmpArr['app_type'] = "ios";
					$tmpArr['token'] = $value['IOSPushToken'];
				}
				else if($value['AppType'] == 4 || $value['AppType'] == 3)
				{
					$tmpArr['app_type'] = "fcm";
					$tmpArr['token'] = $value['FcmPushToken'];
				}
				else if($value['AppType'] == 1)
				{
					$tmpArr['app_type'] = "android_huawei";
					$tmpArr['token'] = $value['FcmPushToken'];
				}
				else if($value['AppType'] == 2)
				{
					$tmpArr['app_type'] = "android_xiaomi";
					$tmpArr['token'] = $value['FcmPushToken'];
				}
				else if($value['AppType'] == 5)
				{
					$tmpArr['app_type'] = "android_oppo";
					$tmpArr['token'] = $value['FcmPushToken'];
				}
				else if($value['AppType'] == 6)
				{
					$tmpArr['app_type'] = "android_vivo";
					$tmpArr['token'] = $value['FcmPushToken'];
				}
				else if($value['AppType'] == 7)
				{
					$tmpArr['app_type'] = "android_flyme";
					$tmpArr['token'] = $value['FcmPushToken'];
				}
							
				$content = json_encode($tmpArr)."\n";		
				if(!socket_write($pushSocket, $content, strlen($content))){
					logWrite("write error ID:".$value['ID']);
					exit;
				}		
				sleep(1);
			}
		}
	}
		
