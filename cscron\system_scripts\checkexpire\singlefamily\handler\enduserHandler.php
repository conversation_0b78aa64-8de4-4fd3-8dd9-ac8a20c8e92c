<?php
require_once __DIR__ . '/../../../common/email_common.php';

class EndUserHandler 
{
    private $daysBefore;
    private $expireDataMap;
    private $personalExpireInfoList;
    private $personalChargeModeMap;
    public $expireDataControl;

    public function __construct() {
    }
    
    public function setExpireParams($daysBefore, $expireDataMap, $personalExpireInfoList, $personalChargeModeMap, $expireDataControl) {
        $this->daysBefore = $daysBefore;
        $this->expireDataMap = $expireDataMap;
        $this->personalExpireInfoList = $personalExpireInfoList;
        $this->personalChargeModeMap = $personalChargeModeMap;
        $this->expireDataControl = $expireDataControl;
    }

    public function filterData() {
        // 判断社区的付费模式 是否要发送给Enduer
        $filteredExpireData = $this->filterPersonalChargeMode();
        return $filteredExpireData;
    }

    private function filterPersonalChargeMode()
    {
         // 判断单住户的付费模式 是否要发送给Enduser
         $payTypeList = array(
            EmailNotifyRule::PAY_ITEM_USER_APP,
            EmailNotifyRule::PAY_ITEM_FEATURE_PLAN,
            EmailNotifyRule::PAY_ITEM_VIDEO_RECORD,
            EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT,
        );

        // 获取每种付费类型发送给Enduser的用户列表
        $payTypePersonalAccountUUIDMap = array(); 
        foreach ($payTypeList as $payType) {
            foreach ($this->personalExpireInfoList as $personalAccountInfo) {
                $personalChargeMode = $this->personalChargeModeMap[$personalAccountInfo['PersonalAccountUUID']];
                if ($personalChargeMode && PersonalCheck::needNotifyEnduser($payType, $personalChargeMode, $this->daysBefore)) {
                    $payTypePersonalAccountUUIDMap[$payType][] = $personalAccountInfo['PersonalAccountUUID'];
                }
            }
        }

        // 获取每种付费类型发送给Enduer的用户列表
        $filteredExpireData = array();
        foreach ($this->expireDataMap as $payType => $expireData) {
            foreach ($expireData as $expireInfo) {
                if (isset($payTypePersonalAccountUUIDMap[$payType]) && in_array($expireInfo['PersonalAccountUUID'], $payTypePersonalAccountUUIDMap[$payType])) {
                    $filteredExpireData[$payType][] = $expireInfo;
                }
            }
        }
        return $filteredExpireData;
    }

    // 发送邮件给Enduser
    public function sendEmail($emailDataMap, $recevierInfoMap) {

        foreach ($emailDataMap as $endUserUUID => $emailData) {
            $ensUserEmail = $recevierInfoMap[$endUserUUID]['email'];
            if (empty($ensUserEmail)) {
                LOG_INFO("ensUserEmail is empty, skip, endUserUUID: " . $endUserUUID);
                continue;
            }

            $emailInfo['email'] = $ensUserEmail;
            $emailInfo["web_ip"] = WEB_DOMAIN;
            $emailInfo['before'] = $this->daysBefore;
            $emailInfo['oem'] = $recevierInfoMap[$endUserUUID]['oem'];
            $emailInfo['user'] = $recevierInfoMap[$endUserUUID]['user'];
            $emailInfo['language'] = $recevierInfoMap[$endUserUUID]['language'];
            $emailInfo['is_show_paylink'] = SHOW_PAYLINK_NONE;
            if ($this->daysBefore == -1) {
                $emailInfo['email_type'] = "single_family_has_expired_to_enduser";
            } else {
                $emailInfo['email_type'] = "single_family_will_expire_to_enduser";
            }

            $emailInfo['list'] = $emailData;
            sendEmailNotifyNew($emailInfo);
        }
    }
}