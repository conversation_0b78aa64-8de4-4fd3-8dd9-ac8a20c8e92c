<?php
require_once(dirname(__FILE__) . '/utils.php');
require_once(dirname(__FILE__) . '/define.php');
require_once(dirname(__FILE__) . '/db_common.php');
require_once(dirname(__FILE__) . '/socket_common.php');
require_once(dirname(__FILE__) . '/../protobuf/proto_crontab.php');
require_once(dirname(__FILE__) . '/../protobuf/proto_crontab_office.php');

class CSendEmailNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Crontab\SendEmailNotifyMsg();
            $TempData->setKey((string)$data[0]);
            $TempData->setPayload((string)$data[1]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

// 如果dis所属的oem不是hager，则使用akuvox的邮件模板
function getProjectDisOemName($emailInfo)
{
    $oemName = OEM_NAME_AKUVOX;
    if(isset($emailInfo['project_uuid'])) {
        $db = GetDB();
        $disInfo = getOemTypeByProjectUUID($db, $emailInfo['project_uuid']);
        if ($disInfo && $disInfo["OemType"] == OEM_TYPE_HAGER) {
            $oemName = OEM_NAME_HAGER;
        }
    }
    return $oemName;
}

function sendEmailNotify($emailInfo)
{
    $payload = [
        "ver" => "1",
        "OEM" => "Akuvox",
        "app_type" => "email",
        "data" => json_encode($emailInfo)
    ];

    // 获取dis所属的oem
    $payload["OEM"] = getProjectDisOemName($emailInfo);
    
    LOG_INFO("Send Email: oem={$payload["OEM"]}, email_info=" . json_encode($emailInfo));

    $data[] = $emailInfo['email'];
    $data[] = json_encode($payload);

    $sendEmailNotifySocket = new CSendEmailNotifySocket();
    $sendEmailNotifySocket->setMsgID(MSG_P2A_SEND_EMAIL_CRONTAB_NOTIFY);
    $sendEmailNotifySocket->copy($data);
}


// 发送邮件
function sendEmailNotifyNew($emailInfo)
{
    $payload = [
        "ver" => "1",
        "OEM" => $emailInfo['oem'],
        "app_type" => "email",
        "data" => json_encode($emailInfo)
    ];

    LOG_INFO("Send Email: oem={$payload["OEM"]}, email_info=" . json_encode($emailInfo));

    $data[] = $emailInfo['email'];
    $data[] = json_encode($payload);

    $sendEmailNotifySocket = new CSendEmailNotifySocket();
    $sendEmailNotifySocket->setMsgID(MSG_P2A_SEND_EMAIL_CRONTAB_NOTIFY);
    $sendEmailNotifySocket->copy($data);
}