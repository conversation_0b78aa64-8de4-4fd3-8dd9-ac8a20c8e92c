<?php

require_once __DIR__ . '/common/aliyun.phar';
require_once __DIR__ . '/common/aws.phar';
require_once __DIR__ . '/common/ukd_header.php';
require_once __DIR__ . '/common/define.php';
require_once(dirname(__FILE__).'/akcs_log.php');


use Aws\S3\S3Client;
use Aws\Exception\AwsException;
use OSS\OssClient;
use OSS\Core\OssException;

class S3Handle
{
    private $cfg;
    public function __construct()
    {
        $this->cfg = $this->InitS3Cfg();
    }
    private function InitS3Cfg()
    {
        $cfg = parse_ini_file("/etc/oss_install.conf");
        if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_OSS) {
            try {
                $oss = new OssClient($cfg['User'], $cfg['Password'], $cfg['Endpoint']);
                $cfg["oss"] = $oss;
            } catch (OssException $exception) {
                LOG_ERROR("Init OSS Client Error:" . $exception->getMessage());
            }
        } else if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_AWS) {
            try {
                $credentials = new Aws\Credentials\Credentials($cfg['User'], $cfg['Password']);
                $s3 = new Aws\S3\S3Client([
                    'version'     => 'latest',
                    'region'      => $cfg['RegionID'],
                    'credentials' => $credentials
                ]);
                $cfg["s3"] = $s3;
            } catch (Exception $exception) {
                LOG_ERROR("Init S3 Client Error:" . $exception->getMessage());
            }
        } else if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_UKD) {
            try {
                $ukd = new UFileClient($cfg['User'], $cfg['Password'], $cfg['Endpoint']);
                $cfg["ukd"] = $ukd;
            } catch (Exception $exception) {
                LOG_ERROR("Init UCloud Client Error:" . $exception->getMessage());
            }
        }
        return $cfg;
    }
    public function DownloadFile($filepath, $localfile)
    {
        if (strlen($filepath) == 0) {
            return;
        }
        $cfg = $this->cfg;
        if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_OSS) {
            $options = array(
                OssClient::OSS_FILE_DOWNLOAD => $localfile
            );
            try {
                $cfg['oss']->getObject($cfg['BucketForPic'], $filepath, $options);
            } catch (OssException $exception) {
                LOG_ERROR("Failed to download $filepath  with error: " . $exception->getMessage());
            }
        } else if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_AWS) {
            try {
                $file = $cfg['s3']->getObject([
                    'Bucket' => $cfg['BucketForPic'],
                    'Key' => $filepath
                ]);
                $body = $file->get('Body');
                file_put_contents($localfile, $body->getContents());
            } catch (Exception $exception) {
                LOG_ERROR("Failed to download $filepath  with error: " . $exception->getMessage());
            }
        } else if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_UKD) {
            try {
                $cfg['ukd']->DownloadFile($cfg['BucketForPic'], $filepath, $localfile);
            } catch (Exception $exception) {
                LOG_ERROR("Failed to download $filepath  with error: " . $exception->getMessage());
            }
        }
    }
    public function UploadFile($remote_path, $localfile, $innerFlag = 0)
    {
        $cfg = $this->cfg;
        if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_OSS) {
            try {
                $cfg['oss']->uploadFile($cfg['BucketForLog'], $remote_path, $localfile);
                $signedUrl = $cfg['oss']->signUrl($cfg['BucketForLog'], $remote_path, 604800, "GET", "");
                //oss需做内外网转换的特殊处理
                $url = str_replace("http:", "https:", $signedUrl);
                $url = str_replace("%2F", "/", $url);
                #替换为外网地址
                $url = str_replace("oss-eu-central-1-internal.aliyuncs.com", "oss-eu-central-1.aliyuncs.com", $url);
                $url = str_replace("oss-ap-southeast-1-internal.aliyuncs.com", "oss-ap-southeast-1.aliyuncs.com", $url);
                $url = str_replace("oss-us-west-1-internal.aliyuncs.com", "oss-us-west-1.aliyuncs.com", $url);
                $url = str_replace("oss-cn-shenzhen-internal.aliyuncs.com", "oss-cn-shenzhen.aliyuncs.com", $url);
                return $url;
            } catch (OssException $exception) {
                LOG_ERROR("Failed to Upload OSS File $localfile  with error: " . $exception->getMessage());
                return "";
            }
        } else if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_AWS) {
            try {
                $cfg['s3']->putObject([
                    'Bucket' => $cfg['BucketForLog'],
                    'Key' => $remote_path,
                    'SourceFile' => $localfile,
                ]);
                $cmd = $cfg['s3']->getCommand('GetObject', [
                    'Bucket' => $cfg['BucketForLog'],
                    'Key' => $remote_path
                ]);
                $request = $cfg['s3']->createPresignedRequest($cmd, '+604800 second');
                $presignedUrl = (string)$request->getUri();
                return $presignedUrl;
            } catch (Exception $exception) {
                LOG_ERROR("Failed to Upload S3 File $localfile  with error: " . $exception->getMessage());
                return "";
            }
        } else if ($cfg['STORAGE_TYPE'] == STORAGE_TYPE_UKD) {
            try {
                $res = $cfg['ukd']->UploadFile($cfg['BucketForLog'], $remote_path, $localfile);
                if ($res != null) {
                    return ""; //上传失败，返回空url
                }
                if($innerFlag)
                {
                    $expire_time_sec = 0;
                }
                else
                {
                    $expire_time_sec = 604800; // 下载url过期时间
                }
                return $cfg['ukd']->GetSignUrl($cfg['BucketForLog'], $remote_path, $expire_time_sec);
            } catch (Exception $exception) {
                LOG_ERROR("Failed to Upload UCloud File  $localfile  with error: " . $exception->getMessage());
                return "";
            }
        }
    }
}
