<?php
require_once(__DIR__ . '/../akcs_kafka.php');

class Byte
{
    //长度
    private $length = 0;

    private $byte = '';
    //操作码
    private $code;
    public function setBytePrev($content)
    {
        $this->byte = $content . $this->byte;
    }
    public function getByte()
    {
        return $this->byte;
    }
    public function getLength()
    {
        return $this->length;
    }
    public function writeChar($string, $size)
    {
        $this->byte .= pack('a' . $size, $string);
        $this->length += $size;
        return; //下面为V3.2使用的

        $strsize = strlen($string);
        if ($size <= $strsize) {
            exit('字符串超长');
        }
        $this->length += $strsize;
        $str = array_map('ord', str_split($string));
        foreach ($str as $vo) {
            $this->byte .= pack('c', $vo);
        }

        for ($i = 1; $i <= ($size - $strsize); $i++) {
            $this->byte .= pack('c', '0');
            $this->length++;
        }
    }
    public function writeInt($str)
    {
        $this->length += 4;
        $this->byte .= pack('N', $str);
    }
    public function writeShortInt($interge)
    {
        $this->length += 2;
        $this->byte .= pack('N', $interge);
    }
    public function writeProtobuf($string)
    {
        $this->byte = $string;
        $strsize = strlen($string);
        $this->length += $strsize;
    }
}

//基类
class CSocket
{
    private $socket;
    private $port = 8503;
    private $host = 'localhost';
    public $byte;
    //以下为消息头字段定义
    private $id;
    private $from = 0101;
    private $param1 = 0101;
    private $param2 = 0101;
    public $ID_LENGTH = 4;
    public $FROM_LENGTH = 4;
    public $PARAM1_LENGTH = 4;
    public $PARAM2_LENGTH = 4;
    public function __set($name, $value)
    {
        $this->$name = $value;
    }
    public function __construct()
    {
        // $this->host = 'localhost';
        // $this->port = 8503;
        // $this->socket = socket_create(AF_UNIX, SOCK_STREAM, 0);
        // if (!$this->socket) {
        //     exit('Create socket failed');
        // }
        // socket_connect($this->socket, UNIX_DOMAIN);
        $this->byte = new Byte();
    }
    public function setMsgID($id)
    {
        $this->id = $id;
        return;
    }
    public function setMsgFrom($from)
    {
        $this->from = $from;
        return;
    }
    public function setMsgOEM($oem)
    {
        $this->param1 = $oem;
        return;
    }
    public function copy($data)
    { //须由子类重载
    }
    public function recvMsg()
    {
        $buf = socket_read($this->socket, strlen($this->byte->getByte()));
        return $buf;
    }
    /*
     *构造消息头
     *消息头=length+id+from+param1+param2
     *length是总长度(4字节),id消息类型(4字节),from暂时无用(4字节),param1/param2(预留字段,均为4字节)
     */
    private function getHeader()
    {
        $length = $this->byte->getLength();
        $length = intval($length) + $this->ID_LENGTH + $this->FROM_LENGTH + $this->PARAM1_LENGTH + $this->PARAM2_LENGTH;
        return pack('N', $length);
    }
    private function getMsgId()
    {
        return pack("N", $this->id);
    }
    private function getMsgFrom()
    {
        return pack('N', $this->from);
    }
    private function getMsgParam1()
    {
        return pack('N', $this->param1);
    }
    private function getMsgParam2()
    {
        return pack('N', $this->param2);
    }
    //构造消息头
    public function setMsgHead()
    {
        $this->byte->setBytePrev($this->getHeader() . $this->getMsgId() . $this->getMsgFrom() . $this->getMsgParam1() . $this->getMsgParam2());
    }

    public function sendMsg()
    {
        // $result = socket_write($this->socket, $this->byte->getByte(), strlen($this->byte->getByte()));
        // if (!$result) {
        //     exit('sendMsg failed');
        // }
        try {
            $result = \AkKafkaProducer::sendMsg($this->byte->getByte());
            if (!$result) {
                LOG_INFO('php kafka : sendMsg failed--msgid' . $this->getMsgId());
                return;
            }
        } catch (Exception $e) {
            LOG_INFO("kafka push csroute exception error." . $e->getMessage());
        }
    }

    public function __desctruct()
    {
        // socket_close($this->socket);
    }
}
