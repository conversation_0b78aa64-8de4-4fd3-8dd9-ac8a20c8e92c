[{"TestName": "VideoRecordHasExpired", "PayMode": "DIS", "ExpireDays": "-1", "SendType": "SendToPMOrEndUser", "TestResult": [{"EmailType": "personal_video_record_has_expired", "Email": "$USER_EMAIL"}]}, {"TestName": "VideoRecordHasExpired", "PayMode": "INS", "ExpireDays": "-1", "SendType": "SendToIns", "TestResult": [{"EmailType": "personal_video_record_has_expired", "Email": "$INS_EMAIL"}]}, {"TestName": "VideoRecordHasExpired", "PayMode": "INS", "ExpireDays": "-1", "SendType": "SendToPMOrEndUser", "TestResult": [{"EmailType": "personal_video_record_has_expired", "Email": "$USER_EMAIL"}]}]