<?php

date_default_timezone_set("PRC");
include_once __DIR__."/util/time.php";
require_once(__DIR__.'/../common/define.php');
require_once(__DIR__.'/../common/comm.php');
require_once(__DIR__.'/../common/log_db_comm.php');



function CreateCallHistoryLog($rootdir, $excel_file, $timeZone, $customizeForm, $data_arr, $export_type, $lang, $s3_cfg)
{
    global $MSGTEXT_FRONT;
    global $MSGTEXT_BACK;
    if (!is_dir($rootdir)) {
        mkdir($rootdir, 0777, true);
    }
    
    // 转换时区、处理数据
    $data_arr = \util\time\setQueryTimeZone($data_arr, $timeZone, $customizeForm);

    $excel_data = [];
    $excel_header = [
        $MSGTEXT_FRONT["CaptureListTanleCaptureTime"],
        $MSGTEXT_FRONT["RecodeListTanleCallerName"],
        $MSGTEXT_FRONT["TabelMessageBoxReceiver"],
        $MSGTEXT_FRONT["RecodeListTanleCallTime"]
    ];
    if ($export_type == EXPORTLOG_TYPE_ALL)
    {
        $excel_header[] =  $MSGTEXT_FRONT["capture"];
    }    

    $index = 0;
    array_push($excel_data, $excel_header);
    foreach ($data_arr as $key => $val) {
        $index = $index + 1;
        $time = $val['StartTime'];
        $file_time = date("YmdHis", strtotime($time));
        $data_dir = date("Ymd", strtotime(explode(" ", $time)[0]));
        $name = $val["CallerName"];
        $name = str_replace("/", "", $name);
        $name = str_replace("\\", "", $name);
        
        $caller_name = str_replace(",", "-", $val["CallerName"]);
        $callee_name = str_replace(",", "-", $val["CalleeName"]);
        $is_answer  = $val['IsAnswer'] ? $val["Duration"] : $MSGTEXT_BACK["noAnswer"];
        
        // 下载图片
        $picurl = $val["PicUrl"];
        $pic = "";
        if (strlen($val["PicUrl"]) > 0)
        {
            $pic = $file_time."+".$val["ID"]."+$name.jpg";
        }

        if ($export_type == EXPORTLOG_TYPE_ALL) {
            $pic_dir = $rootdir."/".$data_dir;
            if (!is_dir($pic_dir)) {
                mkdir($pic_dir, 0777, true);
            }

            DownloadPic($s3_cfg, $picurl, $pic, $pic_dir, $val['StartTime']);

            if ($index % PER_DOWNLOAD_NUMBER_TO_SLEEP == 0) {
                sleep(PER_DOWNLOAD_NUMBER_SLEEP_SEC);
            }
        }
        array_push($excel_data, array($val["StartTime"], $caller_name, $callee_name, $is_answer, $pic));   
    }
    
    LOG_INFO("Export call log: file=$rootdir/$excel_file, count=" . count($excel_data) - 1);
    createExcelFormData($excel_data, $rootdir, $excel_file);
    $excel_data = null;
}

function getTableCallHistoryDatas($log_tables, $datas, $limit)
{
    $left = $limit;
    $result = [];
    $akcs_result = [];

    if ($log_tables != null) {
        $db_log_data = getLOGTableCallHistoryDatas($log_tables, $datas, $left, $result);
        $result = array_merge($result, $db_log_data);
    }
    
    return $result;
}

function getLOGTableCallHistoryDatas($log_tables, $datas, &$left, $result)
{
    $export_type = $datas["export_type"];
    $project_uuid = $datas["project_uuid"];
    $communit_id = $datas["communit_id"];
    $personal_capture_log_tables = [];
    getSqlTables("PersonalCapture", $project_uuid, $personal_capture_log_tables);
    $dbNode = getLogDbNode($project_uuid);
    $db = getLogDB($dbNode);
    $count = 0;
    foreach ($log_tables as $table) {
        if (!LOGDBTableExist($table)) {
            continue;
        }
        if ($left <= 0) {
            LOG_WARING("1.export LOG.CallHistory cloumn limit exceeded.");
            break;
        }

        // 获取要查询的PersonalCapture表
        $capture_tables = getCallPersonalCaptureMapTable($personal_capture_log_tables, $table);

        //查询出token是否存在
        $one_table = getCallHistorylogDatas($db, $table, $count, $left, $datas);

        $call_trace_ids = [];
        if ($export_type == EXPORTLOG_TYPE_ALL) {
            foreach ($one_table as $val) {
                if (empty($val["CallTraceID"])) {
                    continue;
                }
                $call_trace_ids[] = $val["CallTraceID"];
            }
            
            // 分批处理，每次最多1000条
            $pic_urls = [];
            $batch_size = 1000;
            $call_trace_ids_batches = array_chunk($call_trace_ids, $batch_size);
            
            foreach ($call_trace_ids_batches as $batch) {
                // 获取同CallTraceID的PersonalCapture表的PicUrl
                $batch_pic_urls = getCallPersonalCapturePicUrls($db, $capture_tables, $batch, $communit_id);
                $pic_urls = array_merge($pic_urls, $batch_pic_urls);
            }

            // 合并PicUrl到one_table
            foreach ($one_table as &$entry) {
                if (isset($pic_urls[$entry['CallTraceID']])) {
                    $entry['PicUrl'] = $pic_urls[$entry['CallTraceID']];
                }
            }
            unset($entry);
        }
        $result = array_merge($result, $one_table);
    }
    if ($left <= 0) {
        LOG_WARING("2.export LOG.CallHistory cloumn limit exceeded.");#不考虑刚好就是5000张得零界点
    }

    $db = null;
    return $result;
}

function getCallPersonalCaptureMapTable($personal_capture_log_tables, $call_log_table)
{
    $call_table_suffix = explode("_", $call_log_table)[1];
    $is_year_month_format = preg_match('/^\d{4}(0[1-9]|1[0-2])$/', $call_table_suffix);
    $capture_tables = [];
    foreach ($personal_capture_log_tables as $personal_capture_table) {
        $personal_capture_table_suffix = explode("_", $personal_capture_table)[1];
        // 判断后缀是不是同一种格式(因为可能每个月分表大小不一致)
        if ($is_year_month_format) {
            $capture_is_year_month_format = preg_match('/^\d{4}(0[1-9]|1[0-2])$/', $personal_capture_table_suffix);
            if ($capture_is_year_month_format) {
                array_push($capture_tables, $personal_capture_table);
            }
        } else {
            if ($personal_capture_table_suffix == $call_table_suffix) {
                array_push($capture_tables, $personal_capture_table);
            }
        }
    }
    return $capture_tables;
}

function getCallPersonalCapturePicUrls($db, $capture_tables, $call_trace_ids, $communit_id)
{
    $pic_urls = [];
    foreach ($capture_tables as $table) {
        $placeholders = [];
        for ($i = 0; $i < count($call_trace_ids); $i++) {
            $placeholders[] = ':id' . $i;
        }
        $sth = $db->prepare("SELECT CallTraceID, PicUrl FROM $table D WHERE D.MngAccountID = :MngAccountID AND CallTraceID IN (" . implode(',', $placeholders) . ") ORDER BY ID DESC");
        $sth->bindParam(':MngAccountID', $communit_id, PDO::PARAM_STR);
        
        // 绑定call_trace_ids参数
        for ($i = 0; $i < count($call_trace_ids); $i++) {
            $sth->bindValue(':id' . $i, $call_trace_ids[$i], PDO::PARAM_STR);
        }
        
        $sth->execute();
        while ($row = $sth->fetch(PDO::FETCH_ASSOC)) {
            $pic_urls[$row['CallTraceID']] = $row['PicUrl'];
        }
    }
    return $pic_urls;
}

function getCallHistorylogDatas($db, $table, &$count, &$left, $datas)
{
    //查询出token是否存在
    $sth = $db->prepare("select * from $table D  where D.MngAccountID = :MngAccountID and StartTime >= :StartTime and StartTime <= :EndTime and CallType != 8 order by ID desc limit $left");
    $sth->bindParam(':MngAccountID', $datas["communit_id"], PDO::PARAM_STR);
    $sth->bindParam(':StartTime', $datas["export_startime"], PDO::PARAM_STR);
    $sth->bindParam(':EndTime', $datas["export_endtime"], PDO::PARAM_STR);

    $ret = $sth->execute();
    $data = $sth->fetchALL(PDO::FETCH_ASSOC);
    $count = $count + count($data);
    $left = $left - $count;
    return $data;
}

function ExportLogCallHistory($datas, $s3_cfg)
{
    $trace_id = $datas["trace_id"];
    $timeZone = $datas["time_zone"];
    $customizeForm = $datas["time_form"];
    $communit_id = $datas["communit_id"];
    $export_type = $datas["export_type"];
    $startTime = $datas["export_startime"];
    $endTime = $datas["export_endtime"];

    $lang = $datas["language"];
    $today = date("Ymd");

    reload_language($lang);
    global $MSGTEXT_FRONT;
    global $MSGTEXT_BACK;

    //$month = date("Ym", strtotime("-1 month"));
    //$tables = ["CallHistory_".$month, "CallHistory"];

    $project_uuid = getProjectUUID($communit_id);
    $datas["project_uuid"] = $project_uuid;
    $log_tables = [];
    getSqlTables("CallHistory", $project_uuid, $log_tables);

    $index = 1;
    if ($export_type == EXPORTLOG_TYPE_EXCEL_ONLY) {
        $sql_data = getTableCallHistoryDatas($log_tables, $datas, DOWNLOAD_CSV_LIMIT);
        $sql_datas = array_chunk($sql_data, DOWNLOAD_CSV_LIMIT);
    } else {
        $sql_data = getTableCallHistoryDatas($log_tables, $datas, DOWNLOAD_PIC_LIMIT);
        $sql_datas = array_chunk($sql_data, PER_DIR_PIC_LIMIT);
    }
    $file_urls = [];
    foreach ($sql_datas as $key => $value) {
        $file_start_time = \util\time\setTimeZone($startTime, $timeZone, "", "+");
        $file_end_time = \util\time\setTimeZone($endTime, $timeZone, "", "+");
        $file_start_time = explode(" ", $file_start_time)[0];
        $file_end_time = explode(" ", $file_end_time)[0];

        $file_start_time = str_replace("-", "", $file_start_time);
        $file_end_time = str_replace("-", "", $file_end_time);

        $dir = getLogDir($trace_id, $MSGTEXT_FRONT["NavInHtmlMenuCall"], $index, $file_start_time, $file_end_time);
        $zip_save_path = $dir[FILE_ZIP_PATH];
        $csvpath = $dir[FILE_CSV_PATH];
        $zipname = $dir[FILE_ZIP_NAME];
        $csvname = $dir[FILE_CSV_NAME];
        $savedir = $dir[FILE_SAVE_DIR];
        CreateCallHistoryLog($savedir, $csvname, $timeZone, $customizeForm, $value, $export_type, $lang, $s3_cfg);

         //压缩文件夹
         $index = $index + 1;
         if ($export_type == EXPORTLOG_TYPE_ALL) {
             zipDir($zip_save_path, $dir[FILE_ROOT], $dir[FILE_ZIP_DIR]);
             $oss_url = upload_oss($s3_cfg, $zip_save_path, "PmExportLog/$today/callhistory/$trace_id/$zipname");
             $url = [];
             $url[$zipname] = $oss_url;
             array_push($file_urls, $url);
         } else {
            //上传到oss
            $oss_url = upload_oss($s3_cfg, $csvpath, "PmExportLog/$today/callhistory/$trace_id/$csvname");
            updateExcelUrl($trace_id, $oss_url);
         }
    }

    $sql_datas = null;
    $retinfo["urls"] = $file_urls;
    $retinfo["result"] = 0;
    return $retinfo;
}
