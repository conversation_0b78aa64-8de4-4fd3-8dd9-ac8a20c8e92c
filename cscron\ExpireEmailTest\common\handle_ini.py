# coding=utf-8

import ctypes
import os
import re
from xml import etree

import select
import struct
import sys

#apt-get install libxml2-dev libxslt-dev zlib1g-dev
import xml.etree.ElementTree as ET
import configparser

import common.globals

def parse_ini_file(filename):
    config = {}
    current_section = None

    with open(filename, 'r', encoding='utf-8') as file:
        for line in file:
            line = line.strip()

            if not line or line.startswith(';') or line.startswith('#'):
                continue  # 忽略空白行和注释行

            if line.startswith('[') and line.endswith(']'):
                current_section = line[1:-1]  # 提取节名
                config[current_section] = {}
            else:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()

                if value.startswith('"""') or value.startswith("'''"):
                    value = value[3:]  # 移除开头的三引号
                    while not value.endswith('"""') and not value.endswith("'''"):
                        next_line = next(file).strip()
                        next_line = next_line.split('//', 1)[0].strip()
                        value += '\n' + next_line
                    value = value[:-3]  # 移除结尾的三引号
                    # 去除多行内容中的 // 注释

                config[current_section][key] = value

    return config

def replace_variables(string, variables):
    pattern = r'\$(\w+)'
    matches = re.findall(pattern, string)
    for match in matches:
        if match in variables:
            string = string.replace('$' + match, str(variables[match]))
    return string
