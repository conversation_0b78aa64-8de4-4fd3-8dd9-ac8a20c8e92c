# coding=utf-8

import os
import json
import common.globals

def get_test_list(root_dir):
    test_list = list()
    special_list = list()
    for root, dirs, files in os.walk(root_dir):
        for file in files:
            if file.endswith(".json"):
                test_json_file = os.path.join(root, file)
                with open(test_json_file, encoding='utf-8') as json_file:
                    data = json.load(json_file)
                    test_list = test_list + data
                    
    if not common.globals.g_special_cases:
        return test_list
    else:
       # 构建哈希表
        tmp_dict = dict()
        for case in test_list:
            key = (case["TestName"], case["PayMode"], case["ExpireDays"], case["SendType"])
            tmp_dict[key] = case["TestResult"]
        
        for one_special_case in common.globals.g_special_cases:
            key = (one_special_case["TestName"],
                one_special_case["PayMode"],
                one_special_case["ExpireDays"],
                one_special_case["SendType"])
            result = tmp_dict.get(key, None)
            if result:
                one_special_case["TestResult"] = result
                special_list.append(one_special_case)
                
        return special_list
