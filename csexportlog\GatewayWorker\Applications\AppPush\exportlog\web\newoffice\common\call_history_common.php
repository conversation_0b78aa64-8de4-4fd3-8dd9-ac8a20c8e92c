<?php
require_once(__DIR__ . '/../define.php');
require_once(__DIR__ . '/../../../common/define.php');
require_once(__DIR__ . '/../../../common/comm.php');
require_once(__DIR__ . '/../../../common/log_db_comm.php');

// 创建呼叫记录csv表头
function CreateNewOfficeCallHistoryLog($rootdir, $excel_file, $timeZone, $customizeForm, $data_arr, $export_type, $lang)
{
    global $MSGTEXT_FRONT;
    global $MSGTEXT_BACK;
    if (!is_dir($rootdir)) {
        mkdir($rootdir, 0777, true);
    }

    // 转换时区、处理数据
    $data_arr = \util\time\setQueryTimeZone($data_arr, $timeZone, $customizeForm);

    $excel_data = [];
    $excel_header = [
        $MSGTEXT_FRONT["CaptureListTanleCaptureTime"],
        $MSGTEXT_FRONT["RecodeListTanleCallerName"],
        $MSGTEXT_FRONT["TabelMessageBoxReceiver"],
        $MSGTEXT_FRONT["RecodeListTanleCallTime"]
    ];

    array_push($excel_data, $excel_header);
    foreach ($data_arr as $key => $val) {
        $caller_name = str_replace(",", "-", $val["CallerName"]);
        $callee_name = str_replace(",", "-", $val["CalleeName"]);
        $is_answer  = $val['IsAnswer'] ? $val["Duration"] : $MSGTEXT_BACK["noAnswer"];
        array_push($excel_data, array($val["StartTime"], $caller_name, $callee_name, $is_answer));
    }
    
    LOG_INFO("Newoffice export call log: file=$rootdir/$excel_file, count=" . count($excel_data) - 1);
    createExcelFormData($excel_data, $rootdir, $excel_file);
    $excel_data = null;
}


function NewOfficeExportLogCallHistoryCommon($datas, $s3_cfg, $sql_datas)
{
    $today = date("Ymd");
    $lang = $datas["language"];
    $trace_id = $datas["trace_id"];
    $timeZone = $datas["time_zone"];
    $endTime = $datas["export_endtime"];
    $record_uuid = $datas["record_uuid"];
    $export_type = $datas["export_type"];
    $customizeForm = $datas["time_form"];
    $startTime = $datas["export_startime"];
    $project_uuid = $datas["project_uuid"];
    $operator_type = $datas["operator_type"];

    reload_language($lang);
    global $MSGTEXT_FRONT;
    global $MSGTEXT_BACK;

    //$month = date("Ym", strtotime("-1 month"));
    //$tables = ["CallHistory_".$month, "CallHistory"];
    $datas["account_id"] = getAccountIDByUUID($project_uuid);

    $log_tables = [];
    getSqlTables("CallHistory", $project_uuid, $log_tables);

    $index = 1;
    $file_urls = [];
    foreach ($sql_datas as $key => $value) {
        $file_start_time = \util\time\setTimeZone($startTime, $timeZone, "", "+");
        $file_end_time = \util\time\setTimeZone($endTime, $timeZone, "", "+");
        $file_start_time = explode(" ", $file_start_time)[0];
        $file_end_time = explode(" ", $file_end_time)[0];

        $file_start_time = str_replace("-", "", $file_start_time);
        $file_end_time = str_replace("-", "", $file_end_time);

        $dir = getLogDir($trace_id, $MSGTEXT_FRONT["NavInHtmlMenuCall"], $index, $file_start_time, $file_end_time);
        $zip_save_path = $dir[FILE_ZIP_PATH];
        $csvpath = $dir[FILE_CSV_PATH];
        $zipname = $dir[FILE_ZIP_NAME];
        $csvname = $dir[FILE_CSV_NAME];
        $savedir = $dir[FILE_SAVE_DIR];
        CreateNewOfficeCallHistoryLog($savedir, $csvname, $timeZone, $customizeForm, $value, $export_type, $lang);

            //上传到oss
        if ($operator_type == OPERATOR_TYPE_PM)
        {
            $oss_url = upload_oss($s3_cfg, $csvpath, "PmExportLog/$today/callhistory/$trace_id/$csvname");
            updateExcelUrl($trace_id, $oss_url);
            updateExportLogDownloadUrl("PmExportLogRecord", $record_uuid, $oss_url);
        } else if($operator_type == OPERATOR_TYPE_ADMIN){
            $oss_url = upload_oss($s3_cfg, $csvpath, "AdminExportLog/$today/callhistory/$trace_id/$csvname");
            updateExcelUrl($trace_id, $oss_url);
            updateExportLogDownloadUrl("AdminExportLogRecord", $record_uuid, $oss_url);
        }
    }

    LOG_INFO("Newoffice export call log: data line number=" . count($sql_datas));
    $retinfo["urls"] = $file_urls;
    $retinfo["result"] = 0;
    $sql_datas = null;
    return $retinfo;
}

