<?php
require_once (dirname(__FILE__).'/GPBMetadata/AKCrontab.php');
require_once (dirname(__FILE__) . '/AK/Crontab/AppExpire.php');
require_once (dirname(__FILE__) . '/AK/Crontab/AppWillExpire.php');
require_once (dirname(__FILE__) . '/AK/Crontab/CronUserAccessGroupNotifyMsg.php');
require_once (dirname(__FILE__) . '/AK/Crontab/InstallerAppExpire.php');
require_once (dirname(__FILE__) . '/AK/Crontab/InstallerAppWillExpire.php');
require_once (dirname(__FILE__) . '/AK/Crontab/InstallerFeatureWillExpire.php');
require_once (dirname(__FILE__) . '/AK/Crontab/InstallerPhoneExpire.php');
require_once (dirname(__FILE__) . '/AK/Crontab/InstallerPhoneWillExpire.php');
require_once (dirname(__FILE__) . '/AK/Crontab/PMAppAccountExpire.php');
require_once (dirname(__FILE__) . '/AK/Crontab/PMAppAccountWillBeExpire.php');
require_once (dirname(__FILE__) . '/AK/Crontab/PMAppWillBeExpire.php');
require_once (dirname(__FILE__) . '/AK/Crontab/PhoneExpire.php');
require_once (dirname(__FILE__) . '/AK/Crontab/PhoneWillExpire.php');
require_once (dirname(__FILE__) . '/AK/Crontab/PmFeatureWillExpire.php');
require_once (dirname(__FILE__) . '/AK/Crontab/SendEmailNotifyMsg.php');
require_once (dirname(__FILE__) . '/AK/Crontab/SendMessageNotifyMsg.php');
require_once (dirname(__FILE__) . '/AK/Crontab/WebCommunityModifyNotify.php');
require_once (dirname(__FILE__) . '/AK/Crontab/WebPersonalModifyNotify.php');
?>
