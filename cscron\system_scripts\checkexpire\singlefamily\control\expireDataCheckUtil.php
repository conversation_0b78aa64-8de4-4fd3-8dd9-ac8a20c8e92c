<?php

abstract class ExpireDataCheckUtil
{
    protected $db;
    protected $medooDb;
    protected $commonQuery;

    public function __construct($db, $medooDb) {
        $this->db = $db;
        $this->medooDb = $medooDb;
        $this->commonQuery = new CommonQuery($db, $medooDb);
    }

    // 获取过期数据
    abstract public function getExpireData($daysBefore);

    // 过滤迁移dis的社区
    protected function filterMigrateDistributor($expireData)
    {
        return array_filter($expireData, function($data) {
            if (empty($data['DisAccount'])) {
                return false;
            }
            return !self::checkIsMigrateDis($data['DisAccount']);
        });
    }

    protected function checkIsMigrateDis($disAccount)
    {
        if ($disAccount == null) {
            return 0;
        }

        if (SERVER_LOCATION != "as" && SERVER_LOCATION != "eu" ) {
            return 0;
        }

        global $jp_dis_array;
        global $au_dis_array;
        global $az_dis_array;
        global $eu_dis_array;

        if (SERVER_LOCATION == "as" && in_array($disAccount, $au_dis_array)) {
            LOG_INFO("Is aucloud Dis, xinjiapo not need handle! disAccount=$disAccount\n");
            return 1;
        }

        if (SERVER_LOCATION == "as" && in_array($disAccount, $jp_dis_array)) {
            LOG_INFO("Is Jp Dis, xinjiapo not need handle! disAccount=$disAccount\n");
            return 1;
        }

        if (SERVER_LOCATION == "eu" && in_array($disAccount, $az_dis_array)) {
            LOG_INFO("Is Az Dis, ecloud not need handle! disAccount=$disAccount\n");
            return 1;
        }
        if (SERVER_LOCATION == "eu" && in_array($disAccount, $eu_dis_array)) {
            LOG_INFO("Is e2ucloud Dis, ecloud not need handle! disAccount=$disAccount\n");
            return 1;
        }
        return 0;
    }
}   
