<?php

require_once(dirname(__FILE__).'/../check_expire_common_v4500.php');
require_once(dirname(__FILE__).'/../time.php');

const UPDATE_TYPE_ADD = 0;
const UPDATE_TYPE_DEL = 1;
const CHECK_TYPE_EFFECT = 0;
const CHECK_TYPE_EXPIRE = 1;

//根据UUID获取社区信息
function GetCommInfo($uuid)
{
    global $db;
    $sth = $db->prepare("select ID from Account where UUID = :uuid limit 1");
    $sth->bindValue(":uuid", $uuid, PDO::PARAM_STR);
    $sth->execute();
    $comm_info = $sth->fetch(PDO::FETCH_ASSOC);
    return $comm_info;
}

//获取公维金当天开始生效的房间列表
function GetEffectRoomList($before)
{
    global $db;
    $sql = "select R.PersonalAccountUUID,R.ProjectUUID from RoomCommunalFeeInfo R Left join Account A 
    on R.ProjectUUID = A.UUID where TO_DAYS(" . GetUserTimeNowSQL("A.Timezone") . ") + :before = TO_DAYS(R.EffectTime)";
    LOG_INFO("azer check effect, execute sql: " . $sql);
    $sth = $db->prepare($sql);
    $sth->bindValue(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $effect_room_list = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $effect_room_list;
}

//获取公维金当天过期的房间列表
function GetExpireRoomList($before)
{
    global $db;
    $sql = "select R.PersonalAccountUUID,R.ProjectUUID from RoomCommunalFeeInfo R Left join Account A 
    on R.ProjectUUID = A.UUID where TO_DAYS(" . GetUserTimeNowSQL("A.Timezone") . ") + :before = TO_DAYS(R.ExpireTime)";
    LOG_INFO("azer check expire, execute sql: " . $sql);
    $sth = $db->prepare($sql);
    $sth->bindValue(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $effect_room_list = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $effect_room_list;
}

//根据主账号UUID获取家庭用户列表
function GetAccountList($uuid)
{
    global $db;
    $sth = $db->prepare("select Account from PersonalAccount where UUID = :uuid And Role = :role_main_account
                                        union all select Account from PersonalAccount where ParentUUID = :uuid And Role = :role_sub_account");
    $sth->bindValue(":uuid", $uuid, PDO::PARAM_STR);
    $sth->bindValue(":role_main_account", ACCOUNT_ROLE_COMMUNITY_MAIN, PDO::PARAM_INT);
    $sth->bindValue(":role_sub_account", ACCOUNT_ROLE_COMMUNITY_ATTENDANT, PDO::PARAM_INT);
    $sth->execute();
    $account_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    return $account_list;
}

//根据UnitID获取对应公维金权限组
function GetAccessGroupList($unit_id)
{
    global $db;
    $sth = $db->prepare("select AccessGroupID from BuildAccessGroupMngList where UnitID = :unit_id");
    $sth->bindValue(":unit_id", $unit_id, PDO::PARAM_INT);
    $sth->execute();
    $access_group_list = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $access_group_list;
}
//添加用户权限组
function AddUserAccessGroup($acc_id, $account)
{
    global $db;
    $sth = $db->prepare("select Account,AccessGroupID from AccountAccess where Account = :account AND AccessGroupID = :acc_id");
    $sth->bindValue(":account", $account, PDO::PARAM_STR);
    $sth->bindValue(":acc_id", $acc_id, PDO::PARAM_INT);
    $sth->execute();
    $acc = $sth->fetch(PDO::FETCH_ASSOC);
    if($acc) //已有该行 则不重复插入
    {
        LOG_INFO("AccountAccess already exist, account:$account, access group id: $acc_id");
        return;
    }
    $sth = $db->prepare("insert into AccountAccess (Account,AccessGroupID) values (:account, :acc_id)");
    $sth->bindValue(":account", $account, PDO::PARAM_STR);
    $sth->bindValue(":acc_id", $acc_id, PDO::PARAM_INT);
    $sth->execute();
    LOG_INFO("exec add user access group, account: $account, access group id: $acc_id");
}

//移出用户权限组
function RemoveUserAccessGroup($acc_id, $account)
{
    global $db;
    $sth = $db->prepare("delete from AccountAccess where Account = :account AND AccessGroupID = :acc_id");
    $sth->bindValue(":account", $account, PDO::PARAM_STR);
    $sth->bindValue(":acc_id", $acc_id, PDO::PARAM_INT);
    $sth->execute();
    LOG_INFO("exec remove user access group, account: $account, access group id: $acc_id");
}

function AzSendAccountAccessUpdateNotify($account, $project_id, $change_type, $ag_id)
{
    LOG_INFO("Send Account Access Update Notify, account: $account project_id:$project_id change type: $change_type");
    $data[] = $account;
    $data[] = $change_type;
    $data[] = $project_id;
    $data[] = $ag_id;
    $sendAccountAccessUpdateSocket = new CSendAccUpdateNotifySocket();
    $sendAccountAccessUpdateSocket->setMsgID(MSG_P2A_ACCOUNT_ACCESS_UPDATE_NOTIFY);
    $sendAccountAccessUpdateSocket->copy($data);
}