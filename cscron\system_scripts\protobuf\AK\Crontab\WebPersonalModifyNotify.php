<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: AK.Crontab.proto

namespace AK\Crontab;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>AK.Crontab.WebPersonalModifyNotify</code>
 */
class WebPersonalModifyNotify extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>repeated string mac_list = 1;</code>
     */
    private $mac_list;
    /**
     * Generated from protobuf field <code>uint32 change_type = 2;</code>
     */
    private $change_type = 0;
    /**
     * Generated from protobuf field <code>string node = 3;</code>
     */
    private $node = '';
    /**
     * Generated from protobuf field <code>uint32 installer_id = 4;</code>
     */
    private $installer_id = 0;

    public function __construct() {
        \GPBMetadata\AKCrontab::initOnce();
        parent::__construct();
    }

    /**
     * Generated from protobuf field <code>repeated string mac_list = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getMacList()
    {
        return $this->mac_list;
    }

    /**
     * Generated from protobuf field <code>repeated string mac_list = 1;</code>
     * @param string[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setMacList($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->mac_list = $arr;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 change_type = 2;</code>
     * @return int
     */
    public function getChangeType()
    {
        return $this->change_type;
    }

    /**
     * Generated from protobuf field <code>uint32 change_type = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setChangeType($var)
    {
        GPBUtil::checkUint32($var);
        $this->change_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string node = 3;</code>
     * @return string
     */
    public function getNode()
    {
        return $this->node;
    }

    /**
     * Generated from protobuf field <code>string node = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setNode($var)
    {
        GPBUtil::checkString($var, True);
        $this->node = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>uint32 installer_id = 4;</code>
     * @return int
     */
    public function getInstallerId()
    {
        return $this->installer_id;
    }

    /**
     * Generated from protobuf field <code>uint32 installer_id = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setInstallerId($var)
    {
        GPBUtil::checkUint32($var);
        $this->installer_id = $var;

        return $this;
    }

}

