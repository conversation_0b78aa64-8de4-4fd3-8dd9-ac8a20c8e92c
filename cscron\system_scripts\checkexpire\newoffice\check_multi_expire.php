<?php
/* 新办公 Door/AdminApp/UserApp 多个项目的过期通知邮件 */
require_once(dirname(__FILE__).'/../time.php');
require_once(dirname(__FILE__) . '/../email_type.php');
require_once(dirname(__FILE__) . '/../check_expire_common_v4500.php');

class RowInfo
{
    public $project_name;
    public $company_name;
    public $service_type;       // 类型 door/user/admin/premium_attendance
    public $attendance_tier;
    public $personnel_name;
    public $admin_name;
    public $building_name;
    public $device_name;
    public $door_name;
    public $expire_time;

    public function __construct()
    {
        $this->project_name = '';
        $this->company_name = '';
        $this->service_type = '';
        $this->attendance_tier = '';
        $this->personnel_name = '';
        $this->admin_name = '';
        $this->building_name = '';
        $this->device_name = '';
        $this->door_name = '';
        $this->expire_time = '';
    }
}

// 获取有过期的门的公司列表
function getExpireDoorOfficeList($remain_days)
{
    global $db;

    $sth = $db->prepare("SELECT DISTINCT AccountUUID FROM DevicesDoorList 
                         WHERE Active=1 AND IsMonthlyPayed=1 AND TO_DAYS(ExpireTime) = TO_DAYS(NOW()) + :before");
    $sth->bindParam(':before', $remain_days, PDO::PARAM_INT);
    $sth->execute();

    $office_list = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $office_list;
}

// 获取过期的门列表，返回RowInfo数组
function getExpireDoorList($office_uuid, $remain_days)
{
    global $db;

    // 查找 DevicesDoorList 表的AccountUUID字段的值为office_uuid， ExpireTime字段的过期时间
    $sth = $db->prepare("SELECT L.Name as DoorName, D.Location as DeviceName, 
                        CU.UnitName AS UnitName, L.ExpireTime as ExpireTime
                        FROM DevicesDoorList L  
                        LEFT JOIN Devices D ON D.UUID=L.DevicesUUID 
                        LEFT JOIN CommunityUnit CU ON CU.UUID=D.CommunityUnitUUID
                        WHERE L.AccountUUID=:office_uuid AND L.Active=1 AND L.IsMonthlyPayed=1
                        AND TO_DAYS(L.ExpireTime) = TO_DAYS(NOW()) + :before");
    $sth->bindParam(':office_uuid', $office_uuid, PDO::PARAM_STR);
    $sth->bindParam(':before', $remain_days, PDO::PARAM_INT);
    $sth->execute();

    // 获取项目的时区
    $account_info = getAccountByUUID($db, $office_uuid);
    if ($account_info == null) {
        LOG_INFO("getExpireDoorList: account_info is null, office_uuid=" . $office_uuid);
        $account_info['TimeZone'] = '+8:00 Shanghai';
    }

    $expireDoorList = array();
    $door_info_list = $sth->fetchAll(PDO::FETCH_ASSOC);
    foreach ($door_info_list as $row => $door_info) {
        $door_row = new RowInfo();
        $door_row->service_type = 'door';
        $door_row->building_name = $door_info['UnitName'];
        $door_row->device_name = $door_info['DeviceName'];
        $door_row->door_name = $door_info['DoorName'];
        // 数据库中的都是东八区的时间，转成项目的时区
        $door_row->expire_time = ConvertTimeZone($door_info['ExpireTime'], "+8:00 Shanghai", $account_info['TimeZone']);

        array_push($expireDoorList, $door_row);
    }

    return $expireDoorList;
}

// 获取有过期的用户对讲的公司列表
function getExpireUserIntercomeOfficeList($remain_days)
{
    global $db;

    $sth = $db->prepare("SELECT DISTINCT OP.AccountUUID FROM OfficePersonnel OP
                        LEFT JOIN PersonalAccount PA ON PA.UUID=OP.PersonalAccountUUID 
                        WHERE PA.Active=1 AND OP.IsMonthlyPayed=1 
                        AND TO_DAYS(AppIntercomeExpireTime) = TO_DAYS(NOW()) + :before");
    $sth->bindParam(':before', $remain_days, PDO::PARAM_INT);
    $sth->execute();

    $office_list = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $office_list;
}

// 获取过期的用户对讲列表，返回AppInfo数组
function getExpireUserIntercomeList($office_uuid, $remain_days)
{
    global $db;

    // 查找 OfficePersonnel 表的 AccountUUID=office_uuid 的记录，ActiveTime字段的时间
    //激活付过月费才需要发过期邮件，否则应该是未订阅或者永久免费的
    $sth = $db->prepare("SELECT PA.Name AS PersonalName,OP.AppIntercomeExpireTime as ExpireTime,C.Name AS CompanyName 
                        FROM PersonalAccount PA 
                        LEFT JOIN OfficePersonnel OP ON PA.UUID=OP.PersonalAccountUUID 
                        LEFT JOIN OfficeCompany C ON C.UUID=OP.OfficeCompanyUUID 
                        WHERE PA.ParentUUID=:office_uuid AND PA.Active=1 AND OP.IsMonthlyPayed=1 
                        AND TO_DAYS(OP.AppIntercomeExpireTime) = TO_DAYS(NOW()) + :before");
    $sth->bindParam(':office_uuid', $office_uuid, PDO::PARAM_STR);
    $sth->bindParam(':before', $remain_days, PDO::PARAM_INT);
    $sth->execute();

    // 获取项目的时区
    $account_info = getAccountByUUID($db, $office_uuid);
    if ($account_info == null) {
        LOG_INFO("getExpireDoorList: account_info is null, office_uuid=" . $office_uuid);
        $account_info['TimeZone'] = '+8:00 Shanghai';
    }

    $expireUserAppList = array();
    $user_info_list = $sth->fetchAll(PDO::FETCH_ASSOC);
    foreach ($user_info_list as $row => $user_info) {
        $user_row = new RowInfo();
        $user_row->service_type = 'user';
        $user_row->company_name = $user_info['CompanyName'];
        $user_row->personnel_name = DataConfusion::getInstance()->decrypt($user_info['PersonalName']);
        // 数据库中的都是东八区的时间，转成项目的时区
        $user_row->expire_time = ConvertTimeZone($user_info['ExpireTime'], "+8:00 Shanghai", $account_info['TimeZone']);

        array_push($expireUserAppList, $user_row);
    }

    return $expireUserAppList;
}

// 获取有过期的AdminApp的公司列表
function getExpireAdminAppOfficeList($remain_days)
{
    global $db;

    $sth = $db->prepare("SELECT DISTINCT OA.OfficeUUID AS AccountUUID
                        FROM OfficeAdmin OA 
                        LEFT JOIN PersonalAccount PA ON PA.UUID=OA.PersonalAccountUUID 
                        WHERE OA.AppStatus=1 AND OA.IsMonthlyPayed=1 AND PA.Active=1 
                        AND TO_DAYS(PA.ExpireTime) = TO_DAYS(NOW()) + :before");
    $sth->bindParam(':before', $remain_days, PDO::PARAM_INT);
    $sth->execute();

    $office_list = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $office_list;
}

// 获取过期的AdminApp列表，返回AppInfo数组
function getExpireAdminAppList($office_uuid, $remain_days)
{
    global $db;

    // 查找OfficeAdmin表，对应PersonalAccount表记录的ActiveTime字段的时间
    $sth = $db->prepare("SELECT PA.Name AS AdminName,PA.ExpireTime as ExpireTime,C.Name AS CompanyName 
                        FROM OfficeAdmin OA  
                        LEFT JOIN PersonalAccount PA ON PA.UUID=OA.PersonalAccountUUID 
                        LEFT JOIN OfficeCompany C ON C.UUID=OA.OfficeCompanyUUID 
                        WHERE OA.AppStatus=1 AND OA.IsMonthlyPayed=1 AND PA.Active=1 AND PA.ParentUUID=:office_uuid 
                        AND TO_DAYS(PA.ExpireTime) = TO_DAYS(NOW()) + :before");
    $sth->bindParam(':office_uuid', $office_uuid, PDO::PARAM_STR);
    $sth->bindParam(':before', $remain_days, PDO::PARAM_INT);
    $sth->execute();

    // 获取项目的时区
    $account_info = getAccountByUUID($db, $office_uuid);
    if ($account_info == null) {
        LOG_INFO("getExpireDoorList: account_info is null, office_uuid=" . $office_uuid);
        $account_info['TimeZone'] = '+8:00 Shanghai';
    }

    $expireAdminAppList = array();
    $admin_info_list = $sth->fetchAll(PDO::FETCH_ASSOC);
    foreach ($admin_info_list as $row => $admin_info) {
        $admin_row = new RowInfo();
        $admin_row->service_type = 'admin';
        $admin_row->company_name = $admin_info['CompanyName'];
        $admin_row->admin_name = DataConfusion::getInstance()->decrypt($admin_info['AdminName']);
        // 数据库中的都是东八区的时间，转成项目的时区
        $admin_row->expire_time = ConvertTimeZone($admin_info['ExpireTime'], "+8:00 Shanghai", $account_info['TimeZone']);
        array_push($expireAdminAppList, $admin_row);
    }

    return $expireAdminAppList;
}

// 获取过期的 考勤过期 的公司列表
function getExpireAttendanceOfficeList($remain_days)
{
    global $db;

    $sth = $db->prepare("SELECT DISTINCT AccountUUID FROM OfficeInfo 
                        WHERE AttendanceTier != 0 AND IsMonthlyPayed=1 AND TO_DAYS(AttendanceFeatureExpireTime) = TO_DAYS(NOW()) + :before");
    $sth->bindParam(':before', $remain_days, PDO::PARAM_INT);
    $sth->execute();

    $office_list = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $office_list;
}

// 获取过期的 考勤过期 列表，返回AppInfo数组
//若已经缴费在切换到off（AttendanceTier=0），过期时不会发送过期邮件
function getExpireAttendanceList($office_uuid, $remain_days)
{
    global $db;

    $sth = $db->prepare("SELECT AttendanceTier,AttendanceFeatureExpireTime
                         FROM   OfficeInfo
                         WHERE  AccountUUID=:office_uuid 
                         AND AttendanceTier != 0 AND IsMonthlyPayed=1 
                         AND TO_DAYS(AttendanceFeatureExpireTime) = TO_DAYS(NOW()) + :before");
    $sth->bindParam(':office_uuid', $office_uuid, PDO::PARAM_STR);
    $sth->bindParam(':before', $remain_days, PDO::PARAM_INT);
    $sth->execute();

    $office_list = $sth->fetchAll(PDO::FETCH_ASSOC);

    // 获取项目的时区
    $account_info = getAccountByUUID($db, $office_uuid);
    if ($account_info == null) {
        LOG_INFO("getExpireDoorList: account_info is null, office_uuid=" . $office_uuid);
        $account_info['TimeZone'] = '+8:00 Shanghai';
    }

    // 构造考勤信息
    $attendance_list = array();
    foreach ($office_list as $row => $office_info) {
        $attendance_row = new RowInfo();
        $attendance_row->service_type = 'premium_attendance';
        $attendance_row->attendance_tier = strval($office_info['AttendanceTier']);
        // 数据库中的都是东八区的时间，转成项目的时区
        $attendance_row->expire_time = ConvertTimeZone($office_info['AttendanceFeatureExpireTime'], "+8:00 Shanghai", $account_info['TimeZone']);

        array_push($attendance_list, $attendance_row);
    }

    return $attendance_list;
}

// 构造邮件内容
function generateExpireEmailContent($project_info, $remain_days, $row_list)
{
    global $ins_info;

    // 构造邮件信息
    $email_type = EMAIL_TYPE_NEWOFFICE_MULTI_WILL_EXPIRE;
    if ($remain_days < 0) {
        $email_type = EMAIL_TYPE_NEWOFFICE_MULTI_HAS_EXPIRED;
    }

    $email_info = array();
    $email_info["datas"] = $row_list;
    $email_info['web_ip'] = WEB_DOMAIN;
    $email_info["email_type"] = $email_type;
    $email_info['ins_uuid'] = $ins_info['UUID'];
    $email_info['project_id'] = $project_info['ID'];
    $email_info['project_uuid'] = $project_info['UUID'];
    $email_info['project_name'] = $project_info['Location'];
    $email_info["project_type"] = PROJECT_TYPE_NEW_OFFICE;
    $email_info['remaining_days'] = strval($remain_days);

    return $email_info;
}

// 发送邮件到PM
function sendEmailToPM($project_uuid, $email_info, $remain_days)
{
    global $db;
    global $ins_info;
    global $project_info;

    $ins_pay_mode = $ins_info['PayType'];
    $send_expire_type = $project_info['SendExpireEmailType'];
    if (EmailNotifyRule::IfNeedNotifyPm(PROJECT_TYPE_NEW_OFFICE, -1, $ins_pay_mode, -1, $send_expire_type, -1, $remain_days)) {
        // 获取项目下的所有PM
        $pm_list = getPropertyManagerByProjectID($db, $project_info['ID']);

        foreach ($pm_list as $pm) {
            // 设置收件人的信息
            $email_info['user'] = $pm['FirstName'] . ' ' . $pm['LastName'];
            $email_info['grade'] = ACCOUNT_GRADE_PM;
            $email_info['email'] = $pm['Email'];
            $email_info['account'] = $pm["Email"];
            $email_info['language'] = $pm["Language"];
            $email_info['is_show_paylink'] = SHOW_PAYLINK_NONE;

            // 判断是否需要显示付费链接
            if (CommunityCheck::ifPMHasPayPermission($project_uuid)) {
                $email_info['is_show_paylink'] = SHOW_PAYLINK_PM;
            }

            // 发送邮件
            sendEmailNotify($email_info);
        }
    }
}

// 发送邮件给Ins
function sendEmailToIns($project_uuid, $email_info, $remain_days)
{
    global $ins_info;
    global $project_info;

    $ins_pay_mode = $ins_info['PayType'];
    $send_expire_type = $project_info['SendExpireEmailType'];
    if (EmailNotifyRule::IfNeedNotifyIns(PROJECT_TYPE_NEW_OFFICE, -1, $ins_pay_mode, -1, $send_expire_type, -1, $remain_days)) {
        // 获取收件人信息
        $ins_billing_info = getInstallerEmailInfoByAccountUUID($ins_info['UUID']);

        // 设置收件人的信息
        $email_info['user'] = $ins_info['Account'];
        $email_info['account'] = $ins_info["Account"];
        $email_info['language'] = $ins_info["Language"];
        $email_info['email'] = $ins_billing_info['Email'];
        $email_info['is_show_paylink'] = SHOW_PAYLINK_NONE;
        if (strlen($email_info['email']) == 0 || strlen($email_info['user']) == 0) {
            return;
        }

        // 判断是否需要显示付费链接
        if (NewOfficeCheck::ifInsHasPayPermission($project_uuid)) {
            $email_info['is_show_paylink'] = SHOW_PAYLINK_INSTALLER;
        }

        // 发送邮件
        sendEmailNotify($email_info);
    }
}

// 发送邮件给Distributor
function sendEmailToDistributor($project_uuid, $email_info, $remain_days)
{
    global $db;
    global $ins_info;
    global $project_info;

    $ins_pay_mode = $ins_info['PayType'];
    $send_expire_type = $project_info['SendExpireEmailType'];
    if (EmailNotifyRule::IfNeedNotifyDis(PROJECT_TYPE_NEW_OFFICE, -1, $ins_pay_mode, -1, $send_expire_type, -1, $remain_days)) {
        // 获取Distributor信息
        $dis_info = getDistributorByProjectUUID($db, $project_uuid);
        $dis_billing_info = getInstallerEmailInfoByAccountUUID($dis_info['UUID']);

        // 设置收件人的信息
        $email_info['user'] = $dis_info['Account'];
        $email_info['account'] = $dis_info["Account"];
        $email_info['language'] = $dis_info["Language"];
        $email_info['email'] = $dis_billing_info['Email'];
        $email_info['is_show_paylink'] = SHOW_PAYLINK_DISTRIBUTOR;
        if (strlen($email_info['email']) == 0 || strlen($email_info['user']) == 0) {
            LOG_INFO("sendEmailToDistributor failed: email_info=" . json_encode($email_info));
            return;
        }

        // 发送邮件
        sendEmailNotify($email_info);
    }
}

// 发送邮件给SubDistributor
function sendEmailToSubDistributor($project_uuid, $email_info, $remain_days)
{
    global $db;
    global $ins_info;
    global $project_info;

    $ins_pay_mode = $ins_info['PayType'];
    $send_expire_type = $project_info['SendExpireEmailType'];
    if (EmailNotifyRule::IfNeedNotifyDis(PROJECT_TYPE_NEW_OFFICE, -1, $ins_pay_mode, -1, $send_expire_type, -1, $remain_days)) {
        // 获取SubDistributor信息
        $sub_dis_info = getSubDistributorInfoByProjectUUID($db, $project_uuid);
        $dis_billing_info = getInstallerEmailInfoByAccountUUID($sub_dis_info['UUID']);

        // 设置收件人的信息
        $email_info['user'] = $sub_dis_info['Account'];
        $email_info['account'] = $sub_dis_info["Account"];
        $email_info['email'] = $dis_billing_info['Email'];
        $email_info['language'] = $sub_dis_info["Language"];
        $email_info['is_show_paylink'] = SHOW_PAYLINK_NONE;
        if(EmailNotifyRule::IfHasPayPermission($sub_dis_info['ChargeMode'])) {
            $email_info['is_show_paylink'] = SHOW_PAYLINK_DISTRIBUTOR;
        }

        if (strlen($email_info['email']) == 0 || strlen($email_info['user']) == 0) {
            LOG_INFO("sendEmailToSubDistributor failed: email_info=" . json_encode($email_info));
            return;
        }

        // 发送邮件
        sendEmailNotify($email_info);
    }
}

// 处理新办公门、用户APP、adminApp过期
function processNewOfficeMutiExpire($project_uuid, $remain_days)
{
    global $db;
    global $ins_info;
    global $project_info;

    // 获取过期的数据
    $row_list = array_merge(
        getExpireDoorList($project_uuid, $remain_days),
        getExpireUserIntercomeList($project_uuid, $remain_days),
        getExpireAdminAppList($project_uuid, $remain_days),
        getExpireAttendanceList($project_uuid, $remain_days)
    );

    LOG_INFO("project_uuid=$project_uuid, remain_days=$remain_days, checkNewOfficeMutiExpire: row_list=" .
        json_encode($row_list));

    if (count($row_list) > 0) {
        // 获取项目、ins信息
        $project_info = getAccountByUUID($db, $project_uuid);
        $ins_info = getInstallerByProjectUUID($db, $project_uuid);

        // 发送邮件到PM、Ins、Dis
        $email_info = generateExpireEmailContent($project_info, $remain_days, $row_list);

        sendEmailToPM($project_uuid, $email_info, $remain_days);
        sendEmailToIns($project_uuid, $email_info, $remain_days);
        sendEmailToDistributor($project_uuid, $email_info, $remain_days);
        sendEmailToSubDistributor($project_uuid, $email_info, $remain_days);
    }
}

function checkNewOfficeMutiExpire($remain_days)
{
    // 获取有过期的公司列表
    $office_list = array_merge(
        getExpireDoorOfficeList($remain_days),
        getExpireUserIntercomeOfficeList($remain_days),
        getExpireAdminAppOfficeList($remain_days),
        getExpireAttendanceOfficeList($remain_days)
    );

    // 提取 AccountUUID 值, 去重
    $office_uuid_list = [];
    foreach ($office_list as $item) {
        if (isset($item['AccountUUID'])) {
            $office_uuid_list[] = $item['AccountUUID'];
        }
    }

    $office_uuid_list = array_unique($office_uuid_list);
    LOG_INFO("remain_days=$remain_days, checkNewOfficeMutiExpire: office_uuid_list=" . json_encode($office_uuid_list));

    // 遍历每个公司，处理过期
    foreach ($office_uuid_list as $office_uuid) {
        processNewOfficeMutiExpire($office_uuid, $remain_days);
    }
}

$medooDb = getMedooDb();
$db = $medooDb->pdo;

$ins_info = array();
$project_info = array();

checkNewOfficeMutiExpire(15);
checkNewOfficeMutiExpire(5);
checkNewOfficeMutiExpire(3);
checkNewOfficeMutiExpire(-1);
