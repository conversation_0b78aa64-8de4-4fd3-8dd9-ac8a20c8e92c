#!/bin/bash 清理旧的cscron定时脚本

sed -i '/check_app_expire.php/d' /var/spool/cron/crontabs/root
sed -i '/check_office_expire.php/d' /var/spool/cron/crontabs/root
sed -i '/check_yale_token_expire.php/d' /var/spool/cron/crontabs/root
sed -i '/check_qrio_id_token_expire.php/d' /var/spool/cron/crontabs/root
sed -i '/clearCapture2.php/d' /var/spool/cron/crontabs/root
sed -i '/clearModel.php/d' /var/spool/cron/crontabs/root
sed -i '/check_community_dev_offline.php/d' /var/spool/cron/crontabs/root
sed -i '/check_office_dev_offline.php/d' /var/spool/cron/crontabs/root
#阿塞拜疆
sed -i '/az_script_auto_send_paylink.php/d' /var/spool/cron/crontabs/root
sed -i '/az_script_auto_update_access.php/d' /var/spool/cron/crontabs/root
#新办公
sed -i '/check_multi_expire.php/d' /var/spool/cron/crontabs/root