<?php
/*
 * @Description: 操作用户
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors  : kxl
 */

namespace newoffice\property_manager\doorlog;

use PDO;

date_default_timezone_set("PRC");
include_once __DIR__ . "/../../util/time.php";
require_once(__DIR__ . '/../../../common/define.php');
require_once(__DIR__ . '/../../../common/comm.php');
require_once(__DIR__ . '/../../../common/log_db_comm.php');
require_once(__DIR__ . '/../common/doorlog_common.php');
/**
 *  @brief 获取开门记录
 */
function getTableDoorlogDatas($log_tables, $datas, $limit)
{
    $left = $limit;
    $result = [];

    if ($log_tables != null) {
        $db_log_data = getLOGTableDoorlogDatas($log_tables, $datas, $left, $result);
        $result = array_merge($result, $db_log_data);
    }

    return $result;
}

/**
 *  @brief 从LOG数据库中查询开门记录 - 循环控制
 */
function getLOGTableDoorlogDatas($log_tables, $datas, &$left, $result)
{
    $dbNode = getLogDbNode($datas['project_uuid']);
    $db = getLogDB($dbNode);
    $count = 0;
    $result = [];
    foreach ($log_tables as $table) {
        if (!LOGDBTableExist($table)) {
            continue;
        }
        if ($left <= 0) {
            LOG_WARING("1.export LOG.PersonalCapture cloumn limit exceeded.");
            break;
        }

        //查询出token是否存在
        $one_table = getLOGDBDoorlogDatas($db, $table, $count, $left, $datas);
        $result = array_merge($result, $one_table);
    }
    if ($left <= 0) {
        LOG_WARING("2.export LOG.PersonalCapture cloumn limit exceeded."); #不考虑刚好就是5000张得零界点
    }
    
    $db = null;
    return $result;
}

/**
 *  @brief 从LOG数据库中查询开门记录 - 执行
 */
function getLOGDBDoorlogDatas($db, $table, &$count, &$left, $datas)
{
    $no_call_in_doorlog = $datas["no_call_in_doorlog"];
    //capture type = 103代表call截图
    $capture_type_query_str = " (CaptureType < 102 or CaptureType = 103) ";

    if ($no_call_in_doorlog)
    {
        $capture_type_query_str = " CaptureType < 102 ";
    }

    $project_uuid = $datas["project_uuid"];

    // 1.查找开门记录 （注意：这里是从LOG数据库查找的）
    $sth = $db->prepare("select " . PERSONAL_CAPTURE_KEY . " from $table where ProjectUUID = :ProjectUUID and CaptureTime >= :StartTime and  CaptureTime <= :EndTime and $capture_type_query_str order by ID desc limit $left");
    $sth->bindParam(':ProjectUUID', $project_uuid, PDO::PARAM_STR);
    $sth->bindParam(':StartTime', $datas["export_startime"], PDO::PARAM_STR);
    $sth->bindParam(':EndTime', $datas["export_endtime"], PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetchALL(PDO::FETCH_ASSOC);

    // 2.查找设备信息 （注意：这里是从AKCS数据库查找的）
    $akcs_db = getDB();
    $sth = $akcs_db->prepare("select MAC,Location,Relay as RelayInfo,SecurityRelay as SecurityRelayInfo from Devices where AccountUUID=:ProjectUUID");
    $sth->bindParam(':ProjectUUID', $project_uuid, PDO::PARAM_STR);
    $sth->execute();
    $device_info = $sth->fetchALL(PDO::FETCH_ASSOC);

    // 3.查找公司名称 （注意：这里是从AKCS数据库查找的）
    $akcs_db = getDB();
    $sth = $akcs_db->prepare("select Name as CompanyName, UUID as OfficeCompanyUUID from OfficeCompany where AccountUUID=:ProjectUUID");
    $sth->bindParam(':ProjectUUID', $project_uuid, PDO::PARAM_STR);
    $sth->execute();
    $company_info = $sth->fetchALL(PDO::FETCH_ASSOC);
    $akcs_db = null;
    
    // 4.合并数组
    $result = MergeArraysByCustomKey($result, $device_info, "MAC");
    $result = MergeArraysByCustomKey($result, $company_info, "OfficeCompanyUUID");

    $count = $count + count($result);
    $left = $left - $count;
    return $result;
}

/**
 * @brief NewOffice导出日志
 */
function NewofficePmExportDoorLog($datas, $s3_cfg)
{
    $export_type = $datas["export_type"];
    $project_uuid = $datas["project_uuid"];
    // 从AKCS数据库和LOG数据库 获取分表表名
    $log_tables = [];
    getSqlTables("PersonalCapture", $project_uuid, $log_tables);

    // 从每个分表中查找数据
    if ($export_type == EXPORTLOG_TYPE_EXCEL_ONLY) {
        $sql_data = getTableDoorlogDatas($log_tables, $datas, DOWNLOAD_CSV_LIMIT);
        $sql_datas = array_chunk($sql_data, DOWNLOAD_CSV_LIMIT);
    } else {
        $sql_data = getTableDoorlogDatas($log_tables, $datas, DOWNLOAD_PIC_LIMIT);
        $sql_datas = array_chunk($sql_data, PER_DIR_PIC_LIMIT);
    }

    $result = NewofficeExportDoorLogCommon($datas, $s3_cfg, $sql_datas);
    $sql_datas = null;
    return $result;
}
