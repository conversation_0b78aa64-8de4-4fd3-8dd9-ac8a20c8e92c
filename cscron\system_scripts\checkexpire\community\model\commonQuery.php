<?php

class CommonQuery 
{
    private $db;
    private $medooDb;

    public function __construct($db, $medooDb) {
        $this->db = $db;
        $this->medooDb = $medooDb;
    }

    // 获取社区的付费方式, 用于判断邮件发送给那个角色
    public function getCommunityChargeModeByUUID($communityUUID)
    {
        // A 表示社区，B表示Dis，C表示Installer
        $sth = $this->db->prepare("select A.UUID as CommUUID, A.Location, A.ManageGroup as InsID, A.SendExpireEmailType, A.ChargeMode as ProjectMode, 
                            C.Language as InsLanguage, B.ChargeMode as DistributorMode, B.Account as DisAccount, B.ID as DisID, 
                            B.UUID as DisUUID, B.Language as DisLanguage, C.PayType as InstallerMode 
                            from Account A 
                            join Account C on A.ManageGroup = C.ID 
                            join Account B on A.ParentUUID = B.UUID where A.UUID=:communityUUID"
                        );
        $sth->bindValue(':communityUUID', $communityUUID, PDO::PARAM_STR);
        $sth->execute();
        return $sth->fetch(PDO::FETCH_ASSOC);
    }
    
    public function IsUserEnableAutoPay($userUUID, $autoPayType)
    {
        //用户可能有历史订单，取用户最新的订单
        $sth = $this->db->prepare("select A.Status from SubscriptionList A left join SubscriptionEndUserList B on A.UUID = B.SubscriptionUUID
        left join PersonalAccount C on B.PersonalAccountUUID = C.UUID where C.UUID = :uuid and B.Type = :autoPayType order by A.ID desc limit 1");
        $sth->bindParam(':uuid', $userUUID, PDO::PARAM_STR);
        $sth->bindParam(':autoPayType', $autoPayType, PDO::PARAM_INT);
        $sth->execute();
        $result = $sth->fetch(PDO::FETCH_ASSOC);
        if ($result && $result['Status'] == 1)
        {
            LOG_INFO("UUID:$userUUID EnableAutoPay");
            return true;
        }
        return false;
    }

    // 判断用户是否开启三方锁自动扣费
    public function IsUserEnableThirdPartyLockAutoPay($lockUUID, $autoPayType)
    {
        //用户可能有历史订单，取用户最新的订单
        $sth = $this->db->prepare("select A.Status from SubscriptionList A left join SubscriptionEndUserList B on A.UUID = B.SubscriptionUUID
        where B.LockUUID = :lockUUID and B.Type = :autoPayType order by A.ID desc limit 1");
        $sth->bindParam(':lockUUID', $lockUUID, PDO::PARAM_STR);
        $sth->bindParam(':autoPayType', $autoPayType, PDO::PARAM_INT);
        $sth->execute();
        $result = $sth->fetch(PDO::FETCH_ASSOC);
        if ($result && $result['Status'] == 1)
        {
            return true;
        }
        return false;
    }


    // 获取BillingInfo的email
    public function getBillingInfoByAccountUUID($accountUUID)
    {
        return $this->medooDb->get("InstallerBillingInfo", [
            "[>]Account" => ["InstallerBillingInfo.Account" => "Account"]
        ], [
            "InstallerBillingInfo.Email",
            "InstallerBillingInfo.Account",
            "Account.Language",
            "Account.Account"
        ], [
            "Account.UUID" => $accountUUID
        ]);
    }

    // 获取pm的billing info
    public function getPmBillingInfoByAccountUUID($accountUUID)
    {
        return $this->medooDb->get("AccountUserInfo", [
            "[>]AccountMap" => ["AccountUserInfo.UUID" => "UserInfoUUID"],
            "[>]Account" => ["AccountMap.AccountUUID" => "UUID"],
            "[>]PropertyInfo" => ["Account.ID" => "AccountID"]
        ], [
            "PropertyInfo.FirstName",
            "PropertyInfo.LastName", 
            "Account.Account",
            "Account.Language",
            "AccountUserInfo.Email"
        ], [
            "Account.UUID" => $accountUUID
        ]);
    }

    // 获取dis oem name
    public function getDisOemType($disAcountUUID)
    {
        return $this->medooDb->get("DistributorInfo", [
            "[>]Account" => ["DistributorInfo.Account" => "Account"]
        ], [
            "DistributorInfo.OemType"
        ], [
            "Account.UUID" => $disAcountUUID
        ])['OemType'];
    }

    // 获取社区楼栋名称
    public function getCommunityUnitName($communityUnitUUID)
    {
        return $this->medooDb->get("CommunityUnit", ["UnitName"], ["UUID" => $communityUnitUUID])['UnitName'];
    }

    // 获取用户房号
    public function getCommunityAptNumber($personalAccountUUID)
    {
        return $this->medooDb->get("PersonalAccount", [
            "[>]CommunityRoom" => ["PersonalAccount.CommunityRoomUUID" => "UUID"]
        ], [
            "CommunityRoom.RoomName"
        ], [
            "PersonalAccount.UUID" => $personalAccountUUID
        ])['RoomName'];
    }

    // 获取社区和pm信息
    public function getCommunityPMUUIDListByCommunityUUID($communityUUID)
    {
        $sth = $this->db->prepare("select D.UUID from Account B 
                            left join PropertyMngList C on C.CommunityID = B.ID 
                            left join Account D on D.ID = C.PropertyID 
                            where B.UUID = :communityUUID");
        $sth->bindParam(':communityUUID', $communityUUID, PDO::PARAM_STR);
        $sth->execute();
        return $sth->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // 获取dis下的subdis列表
    public function getSubDisListInfoByDisUUID($disUUID)
    {
        $sth = $this->db->prepare("select Account, UUID, Language, ChargeMode as SubDisMode from Account where ParentUUID = :disUUID and Grade = 12");
        $sth->bindParam(':disUUID', $disUUID, PDO::PARAM_STR);
        $sth->execute();
        return $sth->fetchALL(PDO::FETCH_ASSOC);
    }

    // 获取subdis的subDisMngList
    public function getSubDisMngInstallerUUIDList($subDisUUID)
    {
        $sth = $this->db->prepare("select InstallerUUID as InsUUID from SubDisMngList where DistributorUUID = :subDisUUID");
        $sth->bindParam(':subDisUUID', $subDisUUID, PDO::PARAM_STR);
        $sth->execute();
        return $sth->fetchALL(PDO::FETCH_ASSOC);
    }

    public function GenerateUUID()
    {
        $sth = $this->db->prepare("select concat((select ServerTag from SystemSetting limit 1),'-', replace(UUID(), '-', '')) as uuid");
        $sth->execute();
        $ret = $sth->fetch(PDO::FETCH_ASSOC);
        return $ret['uuid'];
    } 

    public function recordEmailRenewPayment($renewUUID, $communityUUID)
    {
        $uuid = $this->GenerateUUID();
        $sth = $this->db->prepare("INSERT INTO EmailRenewPaymentList (UUID, PaymentUUID, ProjectUUID) VALUES (:uuid, :renewUUID, :communityUUID)");
        $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
        $sth->bindParam(':renewUUID', $renewUUID, PDO::PARAM_STR);
        $sth->bindParam(':communityUUID', $communityUUID, PDO::PARAM_STR);
        return $sth->execute();
    }

    public function getBillingInfoByPersonalAccountUUID($personalAccountUUID)
    {
        $personalAccount = $this->medooDb->get("PersonalAccount", [
            "Language", "RoomNumber", "Name", "UserInfoUUID"
        ], [
            "UUID" => $personalAccountUUID
        ]);
        $personalAccount['Account'] = $personalAccount['Name'];

        $personalAccountUserInfo = $this->medooDb->get("PersonalAccountUserInfo", [
            "Email", "MobileNumber"
        ], [
            "UUID" => $personalAccount['UserInfoUUID']
        ]);
        return array_merge($personalAccountUserInfo, $personalAccount);
    }
}