
# coding=utf-8

from enum import Enum
from enum import IntEnum

class TestingType(Enum):
    Landline = 0
    AppExpire = 1           # App功能过期检测
    Feature = 2             # 高级功能过期检测
    AutoPay = 3             # 自动续费检测
    VideoRecord = 4         # 视频存储过期检测
    RentManager = 5         # RentManager过期检测
    DeviceOffline = 6       # 设备离线检测
    
class ProjectType(Enum):
    Community = 0
    Office = 1
    Single = 2
    PMApp = 3
    
class AutoPayerType(IntEnum):
    EndUser = 0
    PM = 1
    INS = 2
    DIS = 3
    SUB_DIS = 4
