[{"TestName": "VideoRecordHasExpired", "PayMode": "DIS", "ExpireDays": "-1", "SendType": "SendToPMOrEndUser", "TestResult": [{"EmailType": "community_video_record_has_expired", "Email": "$PM_EMAIL"}]}, {"TestName": "VideoRecordHasExpired", "PayMode": "INS", "ExpireDays": "-1", "SendType": "SendToIns", "TestResult": [{"EmailType": "community_video_record_has_expired", "Email": "$INS_EMAIL"}]}, {"TestName": "VideoRecordHasExpired", "PayMode": "PM", "ExpireDays": "-1", "SendType": "SendToPMOrEndUser", "TestResult": [{"EmailType": "community_video_record_has_expired", "Email": "$PM_EMAIL"}]}]