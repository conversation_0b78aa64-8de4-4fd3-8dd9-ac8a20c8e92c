<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: AK.Crontab.proto

namespace AK\Crontab;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>AK.Crontab.InstallerPhoneWillExpire</code>
 */
class InstallerPhoneWillExpire extends \Google\Protobuf\Internal\Message
{
    /**
     *cmd id:   MSG_P2A_NOTIFY_INSTALLER_PHONE_WILL_EXPIRE
     *
     * Generated from protobuf field <code>string name = 1;</code>
     */
    private $name = '';
    /**
     * Generated from protobuf field <code>string email = 2;</code>
     */
    private $email = '';
    /**
     * Generated from protobuf field <code>int32 count = 3;</code>
     */
    private $count = 0;
    /**
     * Generated from protobuf field <code>string list = 4;</code>
     */
    private $list = '';
    /**
     * Generated from protobuf field <code>int32 before = 5;</code>
     */
    private $before = 0;

    public function __construct() {
        \GPBMetadata\AKCrontab::initOnce();
        parent::__construct();
    }

    /**
     *cmd id:   MSG_P2A_NOTIFY_INSTALLER_PHONE_WILL_EXPIRE
     *
     * Generated from protobuf field <code>string name = 1;</code>
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     *cmd id:   MSG_P2A_NOTIFY_INSTALLER_PHONE_WILL_EXPIRE
     *
     * Generated from protobuf field <code>string name = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string email = 2;</code>
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * Generated from protobuf field <code>string email = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setEmail($var)
    {
        GPBUtil::checkString($var, True);
        $this->email = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 count = 3;</code>
     * @return int
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     * Generated from protobuf field <code>int32 count = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setCount($var)
    {
        GPBUtil::checkInt32($var);
        $this->count = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string list = 4;</code>
     * @return string
     */
    public function getList()
    {
        return $this->list;
    }

    /**
     * Generated from protobuf field <code>string list = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setList($var)
    {
        GPBUtil::checkString($var, True);
        $this->list = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 before = 5;</code>
     * @return int
     */
    public function getBefore()
    {
        return $this->before;
    }

    /**
     * Generated from protobuf field <code>int32 before = 5;</code>
     * @param int $var
     * @return $this
     */
    public function setBefore($var)
    {
        GPBUtil::checkInt32($var);
        $this->before = $var;

        return $this;
    }

}

