<?php
require_once(dirname(__FILE__) . '/../time.php');
require_once(dirname(__FILE__) . '/../check_expire_common_v4500.php');

const OEM_NAME_RENT = "RentManager";

// RentManager 发送邮件通知消息
function SendRentEmailNotify($email_info)
{
    $payload = [
        "ver" => "1",
        "OEM" => OEM_NAME_AK,
        "app_type" => "email",
        "SUBOEM" => OEM_NAME_RENT,
        "data" => json_encode($email_info)
    ];

    LOG_INFO("Send RentManager Email: email info=" . json_encode($email_info));
    
    $sendEmailNotifySocket = new CSendEmailNotifySocket();
    $sendEmailNotifySocket->setMsgFrom(PROJECT_TYPE_RESIDENCE);
    $sendEmailNotifySocket->setMsgID(MSG_P2A_SEND_EMAIL_CRONTAB_NOTIFY);
    $sendEmailNotifySocket->copy(array($email_info['email'], json_encode($payload)));
}
