<?php

require_once(dirname(__FILE__).'/check_expire_common_v4500.php');
require_once(dirname(__FILE__).'/check_third_party_lock_common.php');
date_default_timezone_set("PRC");

function RefreshYaleToken()
{
    $db = getDB();
    $cur_id = 0;
    $limit = 200;//每次获取多少条数据
    $sleep_offset = 15;//发送多少条请求后进行sleep
    $sleep_time = 3;//每次sleep多少秒
    while (1) {
        try {
            //提前15天刷新
            $sth = $db->prepare("select ID, RefreshToken, PersonalAccountUUID, UUID, RefreshOk from ThirdPartyLockAccount where ID > $cur_id and LockType = ".YALE_LOCK_TYPE." and (TO_DAYS(ExpireTime) < TO_DAYS(NOW()) + 15) order by ID limit $limit");
            $sth->execute();
            $token_list = $sth->fetchALL(PDO::FETCH_ASSOC);
            if (!$token_list) {
                return;
            }
            foreach ($token_list as $row => $token) {
                if ($token['RefreshOk'] == 0) {
                    $cur_id = $token['ID'];
                    continue;
                }
                if ($row % $sleep_offset == 0) {
                    sleep($sleep_time);
                }

                $id = $token['ID'];
                $cur_id = $token['ID'];
                $headers = [];
                $data = [];
                $data['grant_type'] = "refresh_token";
                //写死
                $data['client_id'] = YALE_CLIENT_ID;
                $data['client_secret'] = YALE_CLIENT_SECRET;
                $data['refresh_token'] = $token['RefreshToken'];

                $url = YALE_AUTH_URL."/access_token";
                $output = httpRequest('post', $url, $headers, $data);
                $outputArr = json_decode($output, true);
                if ($outputArr['access_token']) {
                    $refresh_token = $outputArr['refresh_token'];
                    if (!$refresh_token) {
                        $refresh_token = $token['RefreshToken'];
                    }
                    $access_token = $outputArr['access_token'];
                    $expire_in = $outputArr['expires_in'];
                    $expireTime = date('Y-m-d H:i:s', time()+$expire_in);
                    $sth = $db->prepare("update ThirdPartyLockAccount set RefreshToken = :refresh_token, Token = :access_token, ExpireTime = :expireTime where ID = :ID");
                    $sth->bindParam(':refresh_token', $refresh_token, PDO::PARAM_STR);
                    $sth->bindParam(':access_token', $access_token, PDO::PARAM_STR);
                    $sth->bindParam(':expireTime', $expireTime, PDO::PARAM_STR);
                    $sth->bindParam(':ID', $id, PDO::PARAM_STR);
                    $sth->execute();
                } else {
                    $account = GetPersonalAccountInfo($db, $token['PersonalAccountUUID']);
                    $dis = $account["dis"];
                    $ins = $account["ins"];
                    $community = $account["community"];
                    $name = $account["name"];
                    $uuid = $account["uuid"];
                    $tp_lock_account_uuid = $token['UUID'];
                    //告警 TODO: 没有把网络异常的情况考虑进去
                    $msg = "Refresh Yale Token Failed. dis=$dis,ins=$ins,name=$name,uuid=$uuid,ThirdPartyLockAccount UUID=$tp_lock_account_uuid. \nyale ret:$output";

                    //auth2.0 标准https://www.rfc-editor.org/rfc/rfc6749#section-5.2
                    if ($outputArr['error'] == "invalid_grant") {
                        $msg = $msg." invalid error need reband." ;
                        #刷新返回验证,这时候重新刷也没有用,直接置为失败,后续不再刷新
                        $sth = $db->prepare("update ThirdPartyLockAccount set RefreshOk = 0 where ID = :ID");
                        $sth->bindParam(':ID', $id, PDO::PARAM_STR);
                        $sth->execute();
                    }

                    AddAkcsAlarm(AKCS_MONITOR_ALARM_THIRDPARTY_DEVICE_OPERATION_ALARM, $msg);
                }
            }
        } catch (PDOException $e) {
            //告警
            AddAkcsAlarm(AKCS_MONITOR_ALARM_THIRDPARTY_DEVICE_OPERATION_ALARM, "Refresh Yale Token Failed, Error Msg: ".$e->getMessage());
            $db = null;
            echo $e->getMessage();
        }
    }

    $db = null;
}

RefreshYaleToken();
