<?php

// 定义OEM的名称： 0=akuvox,1=hager, 2=PalwintecS, PalwintecS也发邮件也用Akuvox的邮件模板
const OEM_TYPE_HAGER = 1;
const OEM_NAME_AKUVOX = "Akuvox";
const OEM_NAME_HAGER = "hager";
const DIS_OEM_NAME = ["Akuvox", "hager", "PalwintecS"];

/* php <-->csmain的消息枚举 */
const MSG_P2A = 0x00400000;
const MSG_P2A_APP_EXPIRE = MSG_P2A + 100;
const MSG_P2A_APP_WILLBE_EXPIRE = MSG_P2A + 101;
const MSG_P2A_NOTIFY_PM_ACCOUNT_WILL_EXPIRE = MSG_P2A + 104;
const MSG_P2A_PHONE_EXPIRE = MSG_P2A + 107;
const MSG_P2A_PHONE_WILL_EXPIRE = MSG_P2A + 108;
const MSG_P2A_NOTIFY_INSTALLER_APP_WILL_EXPIRE = MSG_P2A + 109;
const MSG_P2A_NOTIFY_INSTALLER_PHONE_WILL_EXPIRE = MSG_P2A + 110;
const MSG_P2A_NOTIFY_INSTALLER_APP_EXPIRE = MSG_P2A + 113;
const MSG_P2A_NOTIFY_INSTALLER_PHONE_EXPIRE = MSG_P2A + 114;
const MSG_P2A_NOTIFY_PM_FEATURE_WILL_EXPIRE = MSG_P2A + 1002;
const MSG_P2A_NOTIFY_INSTALLER_FEATURE_WILL_EXPIRE = MSG_P2A + 1003;
//新版本：个人终端管理员下更新配置
const MSG_P2A_NOTIFY_PERSONAL_MESSAGE = MSG_P2A + 1000;
//新版本更新社区下的用户或设备配置或社区信息
const MSG_P2A_NOTIFY_COMMUNITY_MESSAGE = MSG_P2A + 1001;
const MSG_P2A_NOTIFY_PM_APP_ACCOUNT_WILL_EXPIRE = MSG_P2A + 1006;
const MSG_P2A_NOTIFY_PM_APP_ACCOUNT_EXPIRE = MSG_P2A + 1007;
// 发送过期邮件
const MSG_P2A_SEND_EMAIL_CRONTAB_NOTIFY = MSG_P2A + 1020;
//发送过期Message
const MSG_P2A_SEND_MESSAGE_CRONTAB_NOTIFY = MSG_P2A + 1022;
//发送权限组更新消息
const MSG_P2A_ACCOUNT_ACCESS_UPDATE_NOTIFY = MSG_P2A + 1023;
