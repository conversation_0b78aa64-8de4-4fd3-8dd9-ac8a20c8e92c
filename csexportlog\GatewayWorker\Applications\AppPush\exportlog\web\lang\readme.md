## READ ME
### 使用说明
1. 运行目录: Git/appbackend-php-service/csexportlog/GatewayWorker/Applications/AppPush/exportlog/web/lang
2. 准备文件: 在web_back_end和web_front目录下分别存放web后端、web前端的语言文件（详见：获取语言文件）
3. 生成文件: 执行create_language_from_web.php生成需要文件
4. 查看文件: 生成文件存放在exportlog_back_end、exportlog_front目录

### 获取语言文件
1. 克隆仓库: http://gitlab.akcs.com/web_backend/tool.git
2. 进入运行目录: `tool/download-word`
3. 生成语言文件: 执行`npm i` 和 `npm run build`, 会在 Git\tool\download-word\dist 下生成语言文件
4. 拷贝语言文件: dist目录下的 webNew 目录下的文件拷贝到web_front, php目录下的文件拷贝web_back_end
