# 自动化测试配置

## 使用说明

1、配置文件：../config/env_[env].ini

2、脚本执行命令：python3 main.py [env]

其中，[env]表示测试环境标签

## 功能过期配置

1、创建名称为email-dis的dis，并且把邮箱改为email<EMAIL>，将密码修改成 Aa111111

2、创建subdis 邮箱email<EMAIL>，需要建立一个订单，把subdis的付费通知邮件改为email<EMAIL>

3、创建ins，名称: email-ins 邮箱：<EMAIL>，将密码修改成 Aa111111

4、创建一个社区

5、创建一个社区主账号 邮箱：<EMAIL>

6、创建一个pm, 邮箱：<EMAIL>

7、创建一个office

8、创建一个office 主账号 邮箱：<EMAIL>

9、创建一个单住户主账号 邮箱：<EMAIL> 并且开启高级功能

10、将上述的生成的项目UUID、用户UUID等填入ini配置文件的中

## 自动续费配置

1、创建一个社区主账号 邮箱：<EMAIL>

2、为email-comm1账号开启自动付费订单，并将OrderUUID添加到../config/env_**.ini文件的 COMM_USER_ORDER_UUID

3、创建一个pm, 邮箱：<EMAIL>

4、为email-pm1账号开启自动付费订单，并将OrderUUID添加到../config/env_**.ini文件的 COMM_PM_ORDER_UUID

5、创建一个office 主账号 邮箱：<EMAIL>

6、为email-office1账号开启自动付费订单，并将OrderUUID添加到../config/env_**.ini文件的 OFFICE_USER_ORDER_UUID（新办公暂不支持）

7、创建一个单住户主账号 邮箱：<EMAIL> 并且开启高级功能

8、为email-single1账号开启自动付费订单，并将OrderUUID添加到ini配置文件的 SINGLE_USER_ORDER_UUID

将上述的生成的项目UUID、用户UUID等填入../config/env_**.ini文件中

## 视频存储配置

1、启用社区的视频存储功能

2、启用单住户 email-single 的视频存储功能

## RentManager配置

1、登录supermanager账号

    a、在集成》RentManager下新增，选择 Ins: email-ins, PM: email-pm

    b、在客服》“来自 Rent Manager的邮件通知” 中，选择一个邮箱填写到 RentManager 的 RENT_ADMIN_EMAIL 配置项

2、登录email-ins账号

    a、在购买》集成中，选择刚新增的RentManager付费项目进行第一次付费激活

    b、在付费的账单信息中填写邮箱：<EMAIL>

## 设备离线通知配置

1、请确认：SystemSetting表的EnableDevOfflineNotify字段值为1（开启离线通知功能）

2、在社区项目的 email-comm.master 账号的房间下添加设备1（如果是模拟设备，请确保Devices表的DclientVer字段的值>4600）

3、将设备1的MAC配置在ini文件的COMM_DEV_OFFLINE_MAC项

4、登录PM账号，在社区项目的 设置》当设备未连接时发送邮件 置为开启

5、在办公项目的 email-office.master 添加设备2（如果是模拟设备，请确保Devices表的DclientVer字段的值>4600）

6、将设备2的MAC配置在ini文件的 OFFICE_DEV_OFFLINE_MAC 项

7、登录PM账号，在办公项目的 设置》当设备未连接时发送邮件 置为开启
