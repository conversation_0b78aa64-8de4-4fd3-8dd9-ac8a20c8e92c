<?php
require_once(dirname(__FILE__) . '/expireDataCheckUtil.php');
require_once(dirname(__FILE__) . '/../model/premiumPlanQuery.php');

class PremiumPlanControl extends ExpireDataCheckUtil
{
    public $commonQuery;
    private $premiumPlanQuery;

    public function __construct($db, $medooDb, $commonQuery)
    {
        parent::__construct($db, $medooDb);
        $this->commonQuery = $commonQuery;
        $this->premiumPlanQuery = new PremiumPlanQuery($db, $medooDb);
    }

    public function getExpireData($daysBefore)
    {
        // 获取过期数据
        $expireData = $this->premiumPlanQuery->getExpireData($daysBefore);

        // 过滤掉迁移dis的社区
        $expireData = $this->filterMigrateDistributor($expireData);

        return $expireData;
    }
}