<?php
require_once(dirname(__FILE__) . '/../../data_confusion.php');

class ThirdPartyLockQuery   
{
    private $db;
    private $medooDb;

    const DORMAKABA_LOCK = 3;
    const ITEC_LOCK = 6;
    const THIRD_PARTY_LOCK_PROJECT_PERSONAL = 1;

    public function __construct($db, $medooDb) {
        $this->db = $db;
        $this->medooDb = $medooDb;
    }

    private function getChargeableLockBrandList()
    {
        $lockBrands = [self::DORMAKABA_LOCK, self::ITEC_LOCK];
        return implode(',', $lockBrands);
    }

    public function getExpireData($daysBefore)
    {
        $brandList = $this->getChargeableLockBrandList();

        $sql = "select AA.UUID as DisUUID, AA.Account as DisAccount, A.UUID as InsUUID, A.Account as InsAccount,
                    <PERSON><PERSON><PERSON> AS LockBrand, T.LockUUID AS LockUUID, P.Name, U.Email, U.MobileNumber,
                    S.DistributorUUID as SubDistributorUUID, T.ProjectType, T.PersonalAccountUUID as PersonalAccountUUID, T.ExpireTime 
                from ThirdLockRelateInfo T 
                inner join Account A ON A.UUID = T.AccountUUID 
                left join Account AA ON A.ParentUUID = AA.UUID
                left join PersonalAccount P on P.UUID = T.PersonalAccountUUID
                left join PersonalAccountUserInfo U on U.UUID = P.UserInfoUUID
                left join SubDisMngList S ON S.InstallerUUID = A.UUID  
                where T.Active = 1 
                and T.ExpireTime > CURDATE() + INTERVAL :daysBefore DAY
                and T.ExpireTime <= CURDATE() + INTERVAL (:daysBefore + 1) DAY
                and T.ProjectType = :projectType
                and T.Brand in (:brandList)
                and T.LockUUID IS NOT NULL
                order by A.ParentUUID, A.ID, T.PersonalAccountUUID";

        $sth = $this->db->prepare($sql);
        $sth->bindValue(':brandList', $brandList, PDO::PARAM_STR);
        $sth->bindValue(':daysBefore', $daysBefore, PDO::PARAM_INT);
        $sth->bindValue(':projectType', self::THIRD_PARTY_LOCK_PROJECT_PERSONAL, PDO::PARAM_INT);
        $sth->execute();
        $expireList = $sth->fetchAll(PDO::FETCH_ASSOC);

        // 解密字段
        foreach ($expireList as $row => $item) {
            $expireList[$row]['Name'] = DataConfusion::getInstance()->decrypt($item['Name']);
            $expireList[$row]['Email'] = DataConfusion::getInstance()->decrypt($item['Email']);
            $expireList[$row]['MobileNumber'] = DataConfusion::getInstance()->decrypt($item['MobileNumber']);
        }
        return $expireList;
    }

    public function getDormakabaLockInfo($lockUUID)
    {
        $sth = $this->db->prepare("select Name as LockName, Grade from DormakabaLock where UUID = :uuid");
        $sth->bindParam(':uuid', $lockUUID, PDO::PARAM_STR);
        $sth->execute();
        return $sth->fetch(PDO::FETCH_ASSOC);
    }

    public function getItecLockInfo($lockUUID)
    {
        $sth = $this->db->prepare("select L.Name as LockName, G.Grade from ITecLock L inner join ITecGateway G on L.ITecGatewayUUID = G.UUID where L.UUID = :uuid;");
        $sth->bindParam(':uuid', $lockUUID, PDO::PARAM_STR);
        $sth->execute();
        return $sth->fetch(PDO::FETCH_ASSOC);
    }
}