<?php

date_default_timezone_set("PRC");
include_once __DIR__."/util/time.php";
require_once(__DIR__.'/../akcs_log.php');
require_once(__DIR__.'/../common/define.php');
require_once(__DIR__.'/../common/comm.php');
require_once(__DIR__.'/../common/log_db_comm.php');


function triggerExportlogChain($msg, $s3_cfg)
{
    $today = date("Ymd");
    $triggerFile = __DIR__."/../common/trigger.txt";
    
    $oss_url = upload_oss($s3_cfg, $triggerFile, "PmExportLog/$today/trigger/trigger.txt");

    $ret = array();
    $ret['urls'] = $oss_url;
    $ret['email'] = TRIGGER_EMAIL;
    $ret['msg'] = $msg;
    $ret['result'] = $oss_url == "" ? -1 : 0;
    $ret["export_type"] = EXPORTLOG_TYPE_TRIGGER;

    LOG_INFO("triggerExportlogChain info :" . json_encode($ret));
    return $ret;
}