<?php
/*
 * @Description: 时间计算
 * @version:
 * @Author: kxl
 * @Date: 2020-01-20 17:15:40
 * @LastEditors  : kxl
 */
namespace util\time;

const DEFAULTEXPIRETIME = "2299-12-31 23:59:59";
date_default_timezone_set("PRC");
include_once __DIR__."/../config/timeZone.php";
/**
 * @name: 夏令时计算
 */

function getNow()
{
    $now = date('Y-m-d H:i:s');
    return $now;
}

function summerTime($timeZone)
{
    if (!array_key_exists($timeZone, TIMEZONE)) {
        return $timeZone;
    }

    $data = TIMEZONE[$timeZone];
    $type = $data["Type"];
    $start = $data["Start"];
    $end = $data["End"];
    $offset = $data["Offset"];

    if ($type == 0) {
        $timeZone = computedSummerDate($timeZone, $start, $end, $offset);
    } else {
        $timeZone = computedSummerWeek($timeZone, $start, $end, $offset);
    }
    return $timeZone;
}

/**
 * @name: 按照日期计算夏令时
 */
function computedSummerDate($timeZone, $start, $end, $offset)
{
    // 计算用户时区下的当前时间
    $now = getNow();
    $now = setTimeZone($now, $timeZone, 3, "+", false);
    $nowTime = strtotime($now);
    list($sMonth, $sDate, $sHour) = explode("/", $start);
    list($eMonth, $eDate, $eHour) = explode("/", $end);
    $year = intval(date("Y", $nowTime));
    $start = "$year-$sMonth-$sDate $sHour:00:00";
    $end = "$year-$eMonth-$eDate $eHour:59:59";

    if ($nowTime >= strtotime($start) && $nowTime <= strtotime($end)
    ||
    ($sMonth > $eMonth && ($nowTime >= strtotime($start) || $nowTime <= strtotime($end)))) {
        list($time, $location) = explode(" ", $timeZone);
        list($hour, $min) = explode(":", $time);
        // 时区正负
        $type = substr($hour, 0, 1);
        if ($type == "+") {
            $timeZone = $hour.":".($min+$offset)." ".$location;
        } else {
            $timeZone = $hour.":".($min-$offset)." ".$location;
        }
    }

    return $timeZone;
}

/**
 * @name: 按照周计算夏令时
 */
function computedSummerWeek($timeZone, $start, $end, $offset)
{
    // 计算用户时区下的当前时间
    $now = date('Y-m-d H:i:s');
    $now = setTimeZone($now, $timeZone, 3, "+", false);
    $nowTime = strtotime($now);

    list($sMonth, $sWeekNum, $sWeek, $sHour) = explode("/", $start);
    list($eMonth, $eWeekNum, $eWeek, $eHour) = explode("/", $end);
    // 获取开始时间和结束时间
    $startDate = getWeekNumInMonth($nowTime, $sMonth, $sWeekNum, $sWeek);
    $endDate = getWeekNumInMonth($nowTime, $eMonth, $eWeekNum, $eWeek);
 
    $startTime = "$startDate $sHour:00:00";
    $endTime = "$endDate $eHour:59:59";
    if (strtotime($startTime) <= $nowTime && $nowTime <= strtotime($endTime)
    ||
    ($sMonth > $eMonth && ($nowTime >= strtotime($startTime) || $nowTime <= strtotime($endTime)))) {
        list($time, $location) = explode(" ", $timeZone);
        list($hour, $min) = explode(":", $time);
        // 时区正负
        $type = substr($hour, 0, 1);
        if ($type == "+") {
            $timeZone = $hour.":".($min+$offset)." ".$location;
        } else {
            $timeZone = $hour.":".($min-$offset)." ".$location;
        }
    }
    return $timeZone;
}

/**
 * @name: 获取第几个周几的日期
 */
function getWeekNumInMonth($nowTime, $month, $weekNum, $week)
{
    $year = date("Y", $nowTime);
    $startWeek = date("w", strtotime("$year-$month-01"));
    $startWeek = $startWeek == 0 ? 7 : $startWeek;
    if ($week >= $startWeek) {
        $startDate = $week-$startWeek+1;
    } else {
        $startDate = 8-($startWeek-$week);
    }

    $startDate = $startDate+($weekNum-1)*7;

    $totalDay = date("t", strtotime("$year-$month-01"));
    if ($startDate > $totalDay) {
        return getWeekNumInMonth($nowTime, $month, $weekNum-1, $week);
    }

    return "$year-$month-$startDate";
}

/**
 * @name: 时区时间计算
 */
function computedTime($time, $opera, $type, $unOpera, $compuSum=true)
{
    if ($compuSum) {
        $type = summerTime($type);
    }
    $type = explode(" ", $type)[0];
    $setTime = "";
    
    $symbol = substr($type, 0, 1);
    list($hour, $min) = explode(":", substr($type, 1));
    if ($symbol == "-") {
        $opera = $unOpera;
    }
    $setTime = " $opera$hour hours";
    if ($min != "00") {
        $setTime .= " $opera$min minute";
    }

    return $setTime;
}

function setCustomizeFormatType($type)
{
    //时间格式化
    $time1 = "";
    $time2 = "";
    switch ($type) {
        case '2':
            $time1 = 1;
            $time2 = 1;
            break;
        case '3':
            $time1 = 2;
            $time2 = 1;
            break;
        case '4':
            $time1 = 1;
            $time2 = 3;
            break;
        case '5':
            $time1 = 2;
            $time2 = 3;
            break;
        case '6':
            $time1 = 1;
            $time2 = 5;
            break;
        case '7':
            $time1 = 2;
            $time2 = 5;
            break;
        default:
            $time1 = 1;
            $time2 = 1;
            break;
    }
    return [$time1,$time2];
}

function setCustomizeFormat($time, $type)
{
    list($time1, $time2) = setCustomizeFormatType($type);
    if ($time1 == 1) {
        $time1 = date("h:i:s a", $time);
    } else {
        $time1 = date("H:i:s", $time);
    }
    
    if ($time2 == 1) {
        $time2 = date("Y-m-d", $time);
    } elseif ($time2 == 3) {
        $time2 = date("m-d-Y", $time);
    } else {
        $time2 = date("d-m-Y", $time);
    }
        
    $time = strtoupper($time2." ".$time1);
    
    return $time;
}

function setTime($time, $customizeForm)
{
    //4.0更改 时分秒 分离出日期计算
    $hms = explode(" ", $time)[1];
    //日期格式化

    list($time1, $time2) = setCustomizeFormatType($customizeForm);

    if ($time2 == 1) {
        $time = $time;
    } elseif ($time2 == 3) {
        $day = explode(" ", $time)[0];
        $day = explode("-", $day);
        $day = $day[2]."-".$day[0]."-".$day[1];
        $time = $day." ".$hms;
    } else {
        $day = explode(" ", $time)[0];
        $day = explode("-", $day);
        $day = $day[2]."-".$day[1]."-".$day[0];
        $time = $day." ".$hms;
    }
        

    $time = strtotime($time);
    @$now = time();
    //获取当前时间的年
    $year = date('Y', $now);
    $year = "$year-01-01";
    $year = strtotime($year);
    //与年首日比较
    if ($time<$year) {
        return [date('Y-m-d', $time),$hms];
    }
    //获取月
    $month = date('Y-m-d', $now);
    $month = strtotime($month);
    if ($time<$month) {
        return [date('m-d', $time),$hms];
    }
    return [$hms];
}

/**
 * @name: setTimeZone
 * @msg: 时区转换时间
 * @param $time：时间，$customizeForm：格式化参数，$timeType：时区参数，$opera:+ or -,时区计算方向
 * @return
 */
function setTimeZone($time, $timeType, $customizeForm, $opera = '+', $compuSum=true)
{
    //时区转换时间
    if (!$time) {
        return '';
    }
    if ($time === DEFAULTEXPIRETIME && $opera === "+") {
        return "--";
    }
    //2018-01-10 kxl 从8时区转换0时区
    $unOpera = $opera == '+'?'-':'+';
    // $time =  date("Y-m-d H:i:s",strtotime("$time $unOpera 8 hours"));
    $time = getUTCTime($time, $unOpera);
    
    $type = $timeType;
    
    $setTime = computedTime($time, $opera, $type, $unOpera, $compuSum);
    
    //4.0 新增，时间格式修改
    $timeResult =  setCustomizeFormat(strtotime("$time $setTime"), $opera === "+" ? $customizeForm : "3");
    
    return $timeResult;
}

/**
 * 获取标准服务器时间
 */
function getUTCTime($time, $opera)
{
    return date("Y-m-d H:i:s", strtotime("$time $opera 8 hours"));
}

function diffMaxTime($time)
{
    if ($time == DEFAULTEXPIRETIME) {
        return "--";
    } else {
        return $time;
    }
}

function setQueryTimeZone($data, $timeType, $customizeForm, $unChangeColumn=[])
{
    $changColumn = ["CreateTime","ExpireTime","StartTime","LastConnection","AlarmTime","DealTime",
    "CaptureTime","Time","ActiveTime","NextExpireTime","UpdateTime","PhoneExpireTime"];
    foreach ($data as &$val) {
        foreach ($val as $key=>&$val2) {
            if (in_array($key, $changColumn) && !in_array($key, $unChangeColumn)) {
                if (!$val2) {
                    continue;
                }
                if ($val2 === DEFAULTEXPIRETIME) {
                    $val2 = "--";
                    continue;
                }
                $val2 = setTimeZone($val2, $timeType, $customizeForm);
            }
        }
    }
    return $data;
}
