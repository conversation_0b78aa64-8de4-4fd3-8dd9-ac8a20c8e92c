<?php
/**
 * This file is part of workerman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link http://www.workerman.net/
 * @license http://www.opensource.org/licenses/mit-license.php MIT License
 */
use \Workerman\Worker;

$httpWorker = new Worker("http://0.0.0.0:9402");
$httpWorker->count = 1;

$httpWorker->onMessage = function($connection, $request) {
    $uri = $request->uri(); // 获取请求的 URI
    if ($uri === '/metrics') {
        $responseData = "version_metric{team=\"app_backend\"} ". BRANCH_OR_TAG;
    } else {
        // 其他 URI 返回 404
        $responseData = "404 Not Found";
    }
    // 设置 HTTP 响应头
    $connection->send($responseData);
};

// 如果不是在根目录启动，则运行runAll方法
if (!defined('GLOBAL_START')) {
    Worker::runAll();
}
