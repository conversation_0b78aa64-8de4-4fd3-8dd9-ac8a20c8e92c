<?php

require_once(__DIR__ . '/define.php');

require_once(__DIR__ . '/../s3_handle_redirect.php');
require_once(__DIR__ . '/../akcs_monitor.php');
require_once(__DIR__ . '/../../../../vendor/autoload.php'); // Include PHP Spreadsheet library

function getDB()
{
    $dbuser = "dbuser01";
    $dbpass = MYSQL_DB_PWD;
    $dbip = AKCS_MYSQL_DB_IP;
    $dbport = AKCS_MYSQL_DB_PORT;

    $mysql_conn_string = "mysql:host=$dbip;port=$dbport;dbname=AKCS";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function getLogDB($dbNode = 0)
{
    $dbuser = "dbuser01";
    $dbpass = MYSQL_DB_PWD;
    //LOG_DATABASEIP可能是多个地址集合 如logdb.akcs.inner;logdb1.akcs.inner
    $dbip = explode(";",LOG_MYSQL_DB_IP)[$dbNode];
    $dbport = LOG_MYSQL_DB_PORT;

    $mysql_conn_string = "mysql:host=$dbip;port=$dbport;dbname=LOG";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}


function LOG_TRACE($content)
{
    $tmpNow = time();
    $Now = date('Y-m-d H:i:s', $tmpNow);
    //当文件不存在时候，返回错误信息 会影响ios解析数据
    @file_put_contents(LOG_FILE, $Now . " " . $content, FILE_APPEND);
    @file_put_contents(LOG_FILE, "\n", FILE_APPEND);
}

function computedImageLink($path)
{
    $time = time();
    $link = $path;
    $path = substr($path, 8);
    return $link . "?token=" . md5($path . IMAGELINKKEY . $time) . "&ts=$time";
}


//递归创建路径
function mkDirs($dir)
{
    if (!is_dir($dir)) {
        if (!mkDirs(dirname($dir))) {
            return false;
        }
        if (!mkdir($dir, 0777)) {
            return false;
        }
    }
    return true;
}

function deldir($dir)
{
    //先删除目录下的文件：
    $dh = opendir($dir);
    while ($file = readdir($dh)) {
        if ($file != "." && $file != "..") {
            $fullpath = $dir . "/" . $file;
            if (!is_dir($fullpath)) {
                unlink($fullpath);
            } else {
                deldir($fullpath);
            }
        }
    }
    closedir($dh);
    rmdir($dir);
}
/*
//需要zip扩展
function addFileToZip($path,$zip){
  $handler=opendir($path); //打开当前文件夹由$path指定。
  while(($filename=readdir($handler))!==false){
    if($filename != "." && $filename != ".."){//文件夹文件名字为'.'和‘..'，不要对他们进行操作
      if(is_dir($path."/".$filename)){// 如果读取的某个对象是文件夹，则递归
        addFileToZip($path."/".$filename, $zip);
      }else{ //将文件加入zip对象
        $zip->addFile($path."/".$filename);
      }
    }
  }
  @closedir($path);
}

function zipDir ($zipname, $zipdir) {
    $zip=new ZipArchive();
    if($zip->open($zipname, ZipArchive::CREATE)=== TRUE){
      addFileToZip($zipdir, $zip); //调用方法，对要打包的根目录进行操作，并将ZipArchive的对象传递给方法
      $zip->close(); //关闭处理的zip文件
    }
}
*/
function zipDir($zipname, $rootdir, $zipdir)
{
    $zipname = addcslashes($zipname, '() ');
    $zipdir = addcslashes($zipdir, '() ');
    $rootdir = addcslashes($rootdir, '() ');
    $cmd = "cd $rootdir; zip -r $zipname $zipdir";
    shell_exec($cmd);
}

function downFile($url, $name, $savePath)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5); //只需要设置一个秒的数量就可以
    curl_setopt($ch, CURLOPT_HEADER, true);  //需要response header
    curl_setopt($ch, CURLOPT_NOBODY, false);  //需要response body
    $response = curl_exec($ch);

    //分离header与body
    $body = '';
    if (curl_getinfo($ch, CURLINFO_HTTP_CODE) == '200') {
        $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE); //头信息size
        $body = substr($response, $headerSize);
    }
    $response = null;
    curl_close($ch);

    //文件名
    $fullName = rtrim($savePath, '/') . '/' . $name;
    if (file_put_contents($fullName, $body)) {
        $body = null;
        return true;
    }

    $body = null;
    return false;
}

function logTableExist($logtable)
{
    $db = getDB();
    $sth = $db->prepare("show tables like \"$logtable\";");
    $ret = $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    if ($data) {
        return 1;
    }
    return 0;
}

function updateExcelUrl($trace_id, $url)
{
    $db = getDB();
    $sth = $db->prepare("update PmExportLog set DownloadUrl=:DownloadUrl where TraceID=:TraceID");
    $sth->bindParam(':DownloadUrl', $url, PDO::PARAM_STR);
    $sth->bindParam(':TraceID', $trace_id, PDO::PARAM_STR);

    $ret = $sth->execute();
}

function updateExportLogDownloadUrl($table_name, $record_uuid, $url)
{
    $db = getDB();
    $sth = $db->prepare("update $table_name set DownloadUrl=:DownloadUrl where UUID=:uuid");
    $sth->bindParam(':DownloadUrl', $url, PDO::PARAM_STR);
    $sth->bindParam(':uuid', $record_uuid, PDO::PARAM_STR);

    $ret = $sth->execute();
}

function getLogDir($trace_id, $nameflag, $index, $start, $end)
{
    $zipdir = "$nameflag-$start-$end-$index";
    $xlsx_name = "$nameflag-$start-$end-$index.xlsx";
    $zipname = "$nameflag-$start-$end-$index.zip";

    $dirinfo = [];
    $dirinfo[FILE_ZIP_DIR] = $zipdir;
    $dirinfo[FILE_ZIP_NAME] = $zipname;
    $dirinfo[FILE_CSV_NAME] = $xlsx_name;
    $dirinfo[FILE_SAVE_DIR] = EXPORTLOG_ROOT_DIR . "/" . $trace_id . "/" . $zipdir;
    $dirinfo[FILE_CSV_PATH] = $dirinfo[FILE_SAVE_DIR] . "/" . $xlsx_name;
    $dirinfo[FILE_ZIP_PATH] = EXPORTLOG_ROOT_DIR . "/" . $trace_id . "/" . $zipname;
    $dirinfo[FILE_ROOT] = EXPORTLOG_ROOT_DIR . "/" . $trace_id;
    return $dirinfo;
}

function upload_oss($s3_cfg, $localpath, $remotepath)
{
    $url = $s3_cfg->UploadFile($remotepath, $localpath);
    if ($url == "") {
        AddMonitorHandleUploadOSSErr("export log upload oss error. local path $localpath, remote path $remotepath");
    }
    LOG_INFO($url);
    return $url;
}

function reload_language($lang)
{
    $langfile_front = __DIR__ . "/../web/lang/exportlog_front/$lang.php";
    $langfile_back = __DIR__ . "/../web/lang/exportlog_back_end/$lang.php";
    if (file_exists($langfile_front)) {
        require($langfile_front);
    } else {
        require(__DIR__ . "/../web/lang/exportlog_front/en.php");
    }
    if (file_exists($langfile_back)) {
        require($langfile_back);
    } else {
        require(__DIR__ . "/../web/lang/exportlog_back_end/en.php");
    }
}


function DownloadPic($s3_cfg, $pic_url, $localfilename, $pic_dir, $time)
{
    if (strpos($pic_url, "/group") === 0) {
        $head = PIC_URL_HEAD;
        if (strstr($pic_url, 'group3')) {
            $head = PIC_URL_AWS_HEAD;
        }

        $url = computedImageLink($pic_url);
        $url = $head . $url;
        $ret = downFile($url, $localfilename, $pic_dir);
    } else {
        if (SERVER_LOCATION == 'au') {
            $timestamp1 = strtotime($time);
            $timestamp2 = strtotime(AU_QIANYI_TIME);
            if ($timestamp1 < $timestamp2) {
                S3HandleRedirect::getInstance()->DownloadFile($pic_url, $pic_dir . "/" . $localfilename);
                return;
            }
        }
        $s3_cfg->DownloadFile($pic_url, $pic_dir . "/" . $localfilename);
    }
}

/**
 * @brief                   按照指定的键，将 array2(关联数组) 合并到 array1(关联数组)
 * @param mixed $array1     数组1，array(关联数组的数组)
 * @param mixed $array2     数组2，array(关联数组)
 * @param mixed $key        指定键
 * @return                  array(关联数组)
 */
function MergeArraysByCustomKey($array1, $array2, $mergeKey)
{
    $indexedArray2 = [];
    foreach ($array2 as $item) {
        $indexedArray2[$item[$mergeKey]] = $item;
    }

    foreach ($array1 as $key => $subArray) {
        if (array_key_exists($mergeKey, $subArray) && array_key_exists($subArray[$mergeKey], $indexedArray2)) {
            $array1[$key] = array_merge($subArray, $indexedArray2[$subArray[$mergeKey]]);
        }
    }

    return $array1;
}


/**
 * @brief                   按照指定的键，将 array2(关联数组) 合并到 array1(关联数组)
 * @param mixed $data       数据数组，其中第一行表示行头
 * @param mixed $path       输出路径
 * @param mixed $filename   表格名称
 * @param mixed $key        0=成功，其他=失败
 * @return                  array(关联数组)
 */
function createExcelFormData($data, $path, $filename)
{
    if (!mkDirs($path)) {
        return -1;
    }

    // Convert a 1-D array to 2-D (for ease of looping)
    if (!is_array(end($data))) {
        $data = [$data];
    }

    try {
        // Create new Spreadsheet object
        [$startColumn, $startRow] = PhpOffice\PhpSpreadsheet\Cell\Coordinate::coordinateFromString('A1');
        $spreadsheet = new PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // write cell value
        foreach ($data as $rowData) {
            $currentColumn = $startColumn;
            foreach ($rowData as $cellValue) {
                if ($cellValue !== null) {
                    // Set cell value
                    $sheet->getCell($currentColumn . $startRow)->setValueExplicit($cellValue);
                }
                ++$currentColumn;
            }
            ++$startRow;
        }

        // Save Excel file
        $writer = new PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($path . "/" . $filename);

        // 清空和释放缓存
        PhpOffice\PhpSpreadsheet\Settings::getCache()->clear(); // 一定要释放cache，否则会导致内存不断增长!!!
        unset($spreadsheet);
        unset($writer);
    } catch (PhpOffice\PhpSpreadsheet\Exception $e) {
        LOG_ERROR("createExcelFormData catch PhpSpreadsheet Exception:" . $e->getMessage());
        LOG_ERROR("createExcelFormData getTraceAsString:\n" . $e->getTraceAsString());
    } catch (Exception $e) {
        LOG_ERROR("createExcelFormData catch Common Exception:" . $e->getMessage());
        LOG_ERROR("createExcelFormData getTraceAsString:\n" . $e->getTraceAsString());
    }
}

/**
 * @brief                   获取当前物理内存
 * @return                  占用的内存大小，单位kB
 */
function getCurrentMemoryUsage()
{
    // 获取当前进程的 PID
    $pid = getmypid();

    // 读取 proc 文件系统中的状态信息
    $proc_status = file_get_contents("/proc/$pid/status");

    // 从状态信息中匹配物理内存（获取rss的值）
    if (preg_match('/VmRSS:\s+(\d+) kB/', $proc_status, $matches)) {
        $memory_usage = $matches[1];
        return $memory_usage;
    } else {
        return -1;
    }
}
