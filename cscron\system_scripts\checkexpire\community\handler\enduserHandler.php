<?php

class EndUserHandler 
{
    private $daysBefore;
    private $expireData;
    private $communityExpireInfoList;
    private $communityChargeModeMap;
    public $expireDataControl; 

    public function __construct() {
    }
    
    public function setExpireParams($daysBefore, $expireData, $communityExpireInfoList, $communityChargeModeMap, $expireDataControl) {
        $this->daysBefore = $daysBefore;
        $this->expireData = $expireData;
        $this->communityExpireInfoList = $communityExpireInfoList;
        $this->communityChargeModeMap = $communityChargeModeMap;
        $this->expireDataControl = $expireDataControl;
    }

    public function filterData() {
        // 判断社区的付费模式 是否要发送给EndUser
        $filteredExpireData = $this->filterCommunityChargeMode();
        return $filteredExpireData;
    }

    // 判断社区的付费模式
    private function filterCommunityChargeMode() {
        // 判断社区的付费模式 是否要发送给EndUser
        $payTypeList = array(
            EmailNotifyRule::PAY_ITEM_USER_APP,
            EmailNotifyRule::PAY_ITEM_PM_APP,
            EmailNotifyRule::PAY_ITEM_VIDEO_RECORD,
            EmailNotifyRule::PAY_ITEM_FEATURE_PLAN,
            EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT,
        );

        // 获取每种付费类型发送给Installer的社区列表
        $payTypeCommunityUUIDMap = array(); 
        foreach ($payTypeList as $payType) {
            foreach ($this->communityExpireInfoList as $communityInfo) {
                $communityChargeMode = $this->communityChargeModeMap[$communityInfo['UUID']];
                if ($communityChargeMode && CommunityCheck::needNotifyEndUser($payType, $communityChargeMode, $this->daysBefore)) {
                    $payTypeCommunityUUIDMap[$payType][] = $communityInfo['UUID'];
                }
            }
        }

        // 获取每种付费类型发送给Enduser的社区列表
        $filteredExpireData = array();
        foreach ($this->expireData as $payType => $expireList) {
            foreach ($expireList as $expireInfo) {
                // 判断是否在payTypeCommunityUUIDMap中
                if (isset($payTypeCommunityUUIDMap[$payType]) && in_array($expireInfo['CommunityUUID'], $payTypeCommunityUUIDMap[$payType])) {
                    $filteredExpireData[$payType][] = $expireInfo;
                }
            }
        }
        return $filteredExpireData;
    }

    public function sendEmail($emailDataMap, $recevierInfoMap) {
        foreach ($emailDataMap as $endUserUUID => $emailData) {
            $endUserEmail = $recevierInfoMap[$endUserUUID]['email'];
            if (empty($endUserEmail)) {
                LOG_INFO("endUserEmail is empty, skip, endUserUUID: " . $endUserUUID);
                continue;
            }
            
            $emailInfo['email'] = $endUserEmail;
            $emailInfo['before'] = $this->daysBefore;
            $emailInfo['oem'] = $recevierInfoMap[$endUserUUID]['oem'];
            $emailInfo['user'] = $recevierInfoMap[$endUserUUID]['user'];
            $emailInfo['language'] = $recevierInfoMap[$endUserUUID]['language'];

            if ($this->daysBefore == -1) {
                $emailInfo['email_type'] = "community_has_expired_to_enduser";
            } else {
                $emailInfo['email_type'] = "community_will_expire_to_enduser";
            }

            $emailInfo['list'] = $emailData;
            sendEmailNotifyNew($emailInfo);
        }
    }
}