<?php
require_once(dirname(__FILE__) . '/expireDataCheckUtil.php');
require_once(dirname(__FILE__) . '/../model/videoStorageQuery.php');

class VideoStorageControl extends ExpireDataCheckUtil
{
    public $commonQuery;
    private $videoStorageQuery;

    public function __construct($db, $medooDb, $commonQuery)
    {
        parent::__construct($db, $medooDb);
        $this->commonQuery = $commonQuery;
        $this->videoStorageQuery = new VideoStorageQuery($db, $medooDb);
    }

    public function getExpireData($daysBefore)
    {
        // 获取过期数据
        $expireData = $this->videoStorageQuery->getExpireData($daysBefore);

        // 过滤掉迁移dis的社区
        $expireData = $this->filterMigrateDistributor($expireData);

        // 过滤掉自动扣费社区
        $expireData = $this->filterAutoPayCommunity($expireData);   

        return $expireData;
    }

    private function filterAutoPayCommunity($expireData)
    {
        $filteredData = [];
        foreach ($expireData as $data) {
            if (!$this->videoStorageQuery->IsCommunityEnableVideoStorageAutoPay($data['CommunityUUID'], AUTO_PAY_ORDER_TYPE_COMMUNITY_VIDEO_STORAGE)) {
                $filteredData[] = $data;
            }
        }
        return $filteredData;
    }
}