<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: AK.Crontab.proto

namespace AK\Crontab;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>AK.Crontab.CronUserAccessGroupNotifyMsg</code>
 */
class CronUserAccessGroupNotifyMsg extends \Google\Protobuf\Internal\Message
{
    /**
     * 定时发送用户权限组更新通知
     *
     * Generated from protobuf field <code>string account = 1;</code>
     */
    private $account = '';
    /**
     * 更新类型 0=新增 1=删除
     *
     * Generated from protobuf field <code>uint32 type = 2;</code>
     */
    private $type = 0;
    /**
     *社区id
     *
     * Generated from protobuf field <code>uint32 community_id = 3;</code>
     */
    private $community_id = 0;
    /**
     *权限组id
     *
     * Generated from protobuf field <code>uint32 ag_id = 4;</code>
     */
    private $ag_id = 0;

    public function __construct() {
        \GPBMetadata\AKCrontab::initOnce();
        parent::__construct();
    }

    /**
     * 定时发送用户权限组更新通知
     *
     * Generated from protobuf field <code>string account = 1;</code>
     * @return string
     */
    public function getAccount()
    {
        return $this->account;
    }

    /**
     * 定时发送用户权限组更新通知
     *
     * Generated from protobuf field <code>string account = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setAccount($var)
    {
        GPBUtil::checkString($var, True);
        $this->account = $var;

        return $this;
    }

    /**
     * 更新类型 0=新增 1=删除
     *
     * Generated from protobuf field <code>uint32 type = 2;</code>
     * @return int
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * 更新类型 0=新增 1=删除
     *
     * Generated from protobuf field <code>uint32 type = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkUint32($var);
        $this->type = $var;

        return $this;
    }

    /**
     *社区id
     *
     * Generated from protobuf field <code>uint32 community_id = 3;</code>
     * @return int
     */
    public function getCommunityId()
    {
        return $this->community_id;
    }

    /**
     *社区id
     *
     * Generated from protobuf field <code>uint32 community_id = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setCommunityId($var)
    {
        GPBUtil::checkUint32($var);
        $this->community_id = $var;

        return $this;
    }

    /**
     *权限组id
     *
     * Generated from protobuf field <code>uint32 ag_id = 4;</code>
     * @return int
     */
    public function getAgId()
    {
        return $this->ag_id;
    }

    /**
     *权限组id
     *
     * Generated from protobuf field <code>uint32 ag_id = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setAgId($var)
    {
        GPBUtil::checkUint32($var);
        $this->ag_id = $var;

        return $this;
    }

}

