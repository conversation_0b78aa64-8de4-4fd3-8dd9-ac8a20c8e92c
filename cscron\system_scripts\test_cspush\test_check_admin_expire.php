<?php
/* 新办公 Door/AdminApp/UserApp 过期通知邮件 */
require_once(dirname(__FILE__) . '/../checkexpire/time.php');
require_once(dirname(__FILE__) . '/../checkexpire/check_autopay_common.php');
require_once(dirname(__FILE__) . '/../checkexpire/check_expire_common_v4500.php');


class RowInfo
{
    public $project_name;
    public $company_name;
    public $service_type;       // 类型 door/user/admin/premium_attendance
    public $personnel_name;
    public $admin_name;
    public $device_location;
    public $device_name;
    public $door_name;
    public $expire_time;

    public function __construct(
        $_project_name,
        $_company_name,
        $_service_type,
        $_personnel_name,
        $_admin_name,
        $_device_location,
        $_device_name,
        $_door_name,
        $_expire_time
    ) {
        $this->project_name     = $_project_name;
        $this->company_name     = $_company_name;
        $this->service_type     = $_service_type;
        $this->personnel_name   = $_personnel_name;
        $this->admin_name       = $_admin_name;
        $this->device_location  = $_device_location;
        $this->device_name      = $_device_name;
        $this->door_name        = $_door_name;
        $this->expire_time      = $_expire_time;
    }
}

// 获取过期的门列表，返回DoorInfo数组
function getExpireDoorList($remainDays)
{
    $expireDoorList = array();

    // 使用示例
    $door1 = new RowInfo(
        "project_name001",
        "Office001",
        "door",
        "personnel_name",
        "admin_name",
        "device_location",
        "device_name",
        "door_name",
        "2025-12-31 10:11:11"
    );


    array_push($expireDoorList, $door1);
    array_push($expireDoorList, $door1);
    array_push($expireDoorList, $door1);
    array_push($expireDoorList, $door1);
    array_push($expireDoorList, $door1);
    return $expireDoorList;
}

// 获取过期的UserApp列表，返回AppInfo数组
function getExpireUserAppList($remainDays)
{
    $expireUserAppList = array();

    // 使用示例

    $app = new RowInfo(
        "project_name001",
        "Office001",
        "user",
        "personnel_name",
        "admin_name",
        "device_location",
        "device_name",
        "door_name",
        "2025-12-31 10:11:12"
    );

    array_push($expireUserAppList, $app);
    array_push($expireUserAppList, $app);
    array_push($expireUserAppList, $app);
    array_push($expireUserAppList, $app);
    array_push($expireUserAppList, $app);
    return $expireUserAppList;
}

// 获取过期的AdminApp列表，返回AppInfo数组
function getExpireAdminAppList($remainDays)
{
    $expireAdminAppList = array();

    // 使用示例
    $admin_app = new RowInfo(
        "project_name001",
        "Office001",
        "admin",
        "personnel_name",
        "admin_name",
        "device_location",
        "device_name",
        "door_name",
        "2025-12-31 10:11:13"
    );

    array_push($expireAdminAppList, $admin_app);
    array_push($expireAdminAppList, $admin_app);
    array_push($expireAdminAppList, $admin_app);
    array_push($expireAdminAppList, $admin_app);
    array_push($expireAdminAppList, $admin_app);
    array_push($expireAdminAppList, $admin_app);
    array_push($expireAdminAppList, $admin_app);
    array_push($expireAdminAppList, $admin_app);
    array_push($expireAdminAppList, $admin_app);
    array_push($expireAdminAppList, $admin_app);
    array_push($expireAdminAppList, $admin_app);
    array_push($expireAdminAppList, $admin_app);
    array_push($expireAdminAppList, $admin_app);
    return $expireAdminAppList;
}

// 构造邮件内容
function getExpireEmailContent($email_type, $project_uuid, $remainDays)
{
    // 构造邮件信息
    $email_info = array();
    $email_info["datas"] = array_merge(
        getExpireDoorList($remainDays),
        getExpireUserAppList($remainDays),
        getExpireAdminAppList($remainDays)
    );

    $email_info['web_ip'] = WEB_DOMAIN;
    $email_info["email_type"] = $email_type;
    $email_info["project_name"] = 'prjoect_name 1111';
    $email_info['remaining_days'] = strval($remainDays);
    $email_info["project_type"] = PROJECT_TYPE_NEW_OFFICE;

    // 获取收件人的信息
    $email_info["email"] = '<EMAIL>';
    $email_info['user'] = 'wjh_username';
    $email_info['language'] = 'en';

    // 判断是否需要显示付费链接
    $email_info["is_show_paylink"] = SHOW_PAYLINK_NONE;
    // $account_info = getAccountInfo($distributor_uuid);
    // if ($account_info && $account_info['Grade'] == ACCOUNT_GRADE_SUBDIS) {
    //     if ($account_info['ChargeMode'] != 1) {
    //         $email_info["is_show_paylink"] = SHOW_PAYLINK_NONE;
    //     }
    // }

    sendEmailNotify($email_info);
}

getExpireEmailContent("newoffice_multi_active", "fdfds", 1);
