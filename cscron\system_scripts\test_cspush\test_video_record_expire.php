<?php
/* 视频存储功能过期检测 */
require_once(dirname(__FILE__) . '/../checkexpire/check_expire_common_v4500.php');

const VIDEO_STORE_PROJECT_PERSONAL  = 1;
const VIDEO_STORE_PROJECT_RESIDENCE = 2;
const VIDEO_STORE_PROJECT_OFFICE    = 3;


// 社区账户，视频存储即将过期，发送给Dis/Ins/PM
const EMAIL_TYPE_COMMUNITY_VIDEO_RECORD_WILL_EXPIRE = "community_video_record_will_expire";
// 社区账户，视频存储已经过期，发送给Ins/PM
const EMAIL_TYPE_COMMUNITY_VIDEO_RECORD_HSA_EXPIRED = "community_video_record_has_expired";
// 个人账户，视频存储即将过期，发送给Dis/Ins
const EMAIL_TYPE_PERSONAL_VIDEO_RECORD_WILL_EXPIRE = "personal_video_record_will_expire";
// 个人账户，视频存储已经过期，发送给Ins
const EMAIL_TYPE_PERSONAL_VIDEO_RECORD_HAS_EXPIRED = "personal_video_record_has_expired";
// 个人账户，视频存储即将过期，发送给Enduser
const EMAIL_TYPE_PERSONAL_VIDEO_RECORD_WILL_EXPIRE_TO_ENDUSER = "personal_video_record_will_expire_to_enduser";
// 个人账户，视频存储已经过期，发送给Enduser
const EMAIL_TYPE_PERSONAL_VIDEO_RECORD_HAS_EXPIRED_TO_ENDUSER = "personal_video_record_has_expired_to_enduser";

/************************* 单住户发送通知邮件 ***************************/

// 单住户 视频存储即将过期发送邮件通知Dis
function PersonalWillExpireSendEmailToDis($email_type, $data, $before)
{
    $enduser_list = array();
    array_push($enduser_list, array('Name' => '名字', 'Email' => '邮箱@123.com', 'Phone' => '1*********'));
    array_push($enduser_list, array('Name' => '名字', 'Email' => '邮箱@123.com', 'Phone' => '1*********'));
    array_push($enduser_list, array('Name' => '名字', 'Email' => '邮箱@123.com', 'Phone' => '1*********'));
    array_push($enduser_list, array('Name' => '名字', 'Email' => '邮箱@123.com', 'Phone' => '1*********'));
    array_push($enduser_list, array('Name' => '名字', 'Email' => '邮箱@123.com', 'Phone' => '1*********'));

    // 发送邮件
    $email_info = array();
    $email_info['remaining_days'] = strval($before);
    $email_info["user"] = "Distributor001";
    $email_info["account"] = "Distributor001";
    $email_info["email_type"] = $email_type;
    $email_info['email'] = "<EMAIL>";
    $email_info['language'] = 'zh-cn';
    $email_info["is_show_paylink"] = SHOW_PAYLINK_DISTRIBUTOR;
    $email_info['enduser_list'] = $enduser_list;
    $email_info['web_ip'] = WEB_DOMAIN;
    $email_info['installer_uuid'] = "installer_uuid********111";
    sendEmailNotify($email_info);
}

// 单住户 视频存储即将过期发送邮件通知Ins
function PersonalWillExpireSendEmailToIns($email_type, $data, $before)
{
    $enduser_list = array();
    array_push($enduser_list, array('Name' => '名字', 'Email' => '邮箱@123.com', 'Phone' => '1*********'));
    array_push($enduser_list, array('Name' => '名字', 'Email' => '邮箱@123.com', 'Phone' => '1*********'));
    array_push($enduser_list, array('Name' => '名字', 'Email' => '邮箱@123.com', 'Phone' => '1*********'));
    array_push($enduser_list, array('Name' => '名字', 'Email' => '邮箱@123.com', 'Phone' => '1*********'));
    array_push($enduser_list, array('Name' => '名字', 'Email' => '邮箱@123.com', 'Phone' => '1*********'));


    $email_info = array();
    $email_info['remaining_days'] = strval($before);
    $email_info['email_type'] = $email_type;
    $email_info['email'] = "<EMAIL>";
    $email_info['enduser_list'] = $enduser_list;
    $email_info['user'] = 'Personal_Ins_001';
    $email_info['account'] = 'Personal_Ins_001';
    $email_info['web_ip'] = WEB_DOMAIN;
    $email_info['installer_id'] = ********;
    $email_info['language'] = 'zh-cn';
    $email_info["is_show_paylink"] = SHOW_PAYLINK_INSTALLER;
    sendEmailNotify($email_info);
}

// 单住户 视频存储即将过期发送邮件通知Enduser
function PersonalWillExpireSendEmailToEnduser($email_type, $data, $before)
{
    $email_info = array();
    $email_info['remaining_days'] = strval($before);
    $email_info['user'] = 'enduser';
    $email_info['email'] = "<EMAIL>";
    $email_info['email_type'] = $email_type;
    $email_info['language'] = 'zh-cn';
    sendEmailNotify($email_info);
}

// 单住户 视频存储已经过期发送邮件通知Ins
function PersonalExpiredSendEmailToIns($email_type, $data, $before)
{
    PersonalWillExpireSendEmailToIns($email_type, $data, $before);
}

// 单住户 视频存储已经过期发送邮件通知Enduser
function PersonalExpiredSendEmailToEnduser($email_type, $data, $before)
{
    PersonalWillExpireSendEmailToEnduser($email_type, $data, $before);
}

/************************ 社区项目发送通知邮件 **************************/

// 社区 视频存储即将过期发送邮件通知Dis
function CommunityWillExpireSendEmailToDis($email_type, $data, $before)
{
    $community_list = array();
    array_push($community_list, array('community_name' => '社区001'));

    $email_info = array();
    $email_info['remaining_days'] = strval($before);
    $email_info["user"] = 'com_to_dis';
    $email_info["account"] = 'com_to_dis';
    $email_info["email_type"] = $email_type;
    $email_info['email'] = "<EMAIL>";
    $email_info["community"] = "community001";
    $email_info["community_list"] = $community_list;
    $email_info['language'] = 'zh-cn';
    $email_info['web_ip'] = WEB_DOMAIN;
    $email_info['project_uuid'] = "project_uuid********111";
    $email_info['installer_uuid'] = "installer_uuid********111";
    $email_info["is_show_paylink"] = SHOW_PAYLINK_DISTRIBUTOR;
    sendEmailNotify($email_info);
}

// 社区 视频存储即将过期发送邮件通知Ins
function CommunityWillExpireSendEmailToIns($email_type, $data, $before)
{
    $community_list = array();
    array_push($community_list, array('community_name' => '社区001'));

    $email_info = array();
    $email_info['remaining_days'] = strval($before);
    $email_info['email_type'] = $email_type;
    $email_info['email'] = "<EMAIL>";
    $email_info['user'] = 'com_to_ins';
    $email_info['account'] = 'com_to_ins';
    $email_info['language'] = 'zh-cn';
    $email_info["community"] = "community001";
    $email_info["community_list"] = $community_list;
    $email_info["is_show_paylink"] = SHOW_PAYLINK_INSTALLER;
    
    $email_info['web_ip'] = WEB_DOMAIN;
    $email_info['project_id'] = 1111111;
    $email_info['project_uuid'] = "project_uuid********1";
    sendEmailNotify($email_info);
}

// 社区 视频存储即将过期发送邮件通知PM
function CommunityWillExpireSendEmailToPM($email_type, $data, $before)
{
    $community_list = array();
    array_push($community_list, array('community_name' => '社区001'));

    $email_info = array();
    $email_info['remaining_days'] = strval($before);
    $email_info['email'] = "<EMAIL>";
    $email_info['email_type'] = $email_type;
    $email_info['user'] = 'Property Manager';
    $email_info['account'] = '<EMAIL>';
    $email_info['language'] = 'zh-cn';
    
    $email_info['web_ip'] = WEB_DOMAIN;
    $email_info['project_id'] = 1111111;
    $email_info['project_uuid'] = "project_uuid********1";
    $email_info["community"] = "community001";
    $email_info["community_list"] = $community_list;
    $email_info["is_show_paylink"] = SHOW_PAYLINK_PM;
    sendEmailNotify($email_info);
}

// 社区 视频存已经储过期发送邮件通知Ins
function CommunityExpiredSendEmailToIns($email_type, $data, $before)
{
    CommunityWillExpireSendEmailToIns($email_type, $data, $before);
}

// 社区 视频存储已经过期发送邮件通知PM
function CommunityExpiredSendEmailToPM($email_type, $data, $before)
{
    CommunityWillExpireSendEmailToPM($email_type, $data, $before);
}


// 主函数
PersonalWillExpireSendEmailToDis(EMAIL_TYPE_PERSONAL_VIDEO_RECORD_WILL_EXPIRE, array(), 6);
PersonalWillExpireSendEmailToIns(EMAIL_TYPE_PERSONAL_VIDEO_RECORD_WILL_EXPIRE, array(), 6);
PersonalExpiredSendEmailToIns(EMAIL_TYPE_PERSONAL_VIDEO_RECORD_HAS_EXPIRED, array(), 6);
PersonalWillExpireSendEmailToEnduser(EMAIL_TYPE_PERSONAL_VIDEO_RECORD_WILL_EXPIRE_TO_ENDUSER, array(), 6);
PersonalExpiredSendEmailToEnduser(EMAIL_TYPE_PERSONAL_VIDEO_RECORD_HAS_EXPIRED_TO_ENDUSER, array(), 6);

CommunityWillExpireSendEmailToIns(EMAIL_TYPE_COMMUNITY_VIDEO_RECORD_WILL_EXPIRE, array(), 6);
CommunityExpiredSendEmailToIns(EMAIL_TYPE_COMMUNITY_VIDEO_RECORD_HSA_EXPIRED, array(), 6);
CommunityWillExpireSendEmailToPM(EMAIL_TYPE_COMMUNITY_VIDEO_RECORD_WILL_EXPIRE, array(), 6);
CommunityExpiredSendEmailToPM(EMAIL_TYPE_COMMUNITY_VIDEO_RECORD_HSA_EXPIRED, array(), 6);
