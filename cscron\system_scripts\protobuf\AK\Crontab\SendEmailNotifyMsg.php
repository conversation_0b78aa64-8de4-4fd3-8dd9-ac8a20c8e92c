<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: AK.Crontab.proto

namespace AK\Crontab;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>AK.Crontab.SendEmailNotifyMsg</code>
 */
class SendEmailNotifyMsg extends \Google\Protobuf\Internal\Message
{
    /**
     * 发送邮件统一模板
     *
     * Generated from protobuf field <code>string key = 1;</code>
     */
    private $key = '';
    /**
     * 统一json格式
     *
     * Generated from protobuf field <code>string payload = 2;</code>
     */
    private $payload = '';

    public function __construct() {
        \GPBMetadata\AKCrontab::initOnce();
        parent::__construct();
    }

    /**
     * 发送邮件统一模板
     *
     * Generated from protobuf field <code>string key = 1;</code>
     * @return string
     */
    public function getKey()
    {
        return $this->key;
    }

    /**
     * 发送邮件统一模板
     *
     * Generated from protobuf field <code>string key = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setKey($var)
    {
        GPBUtil::checkString($var, True);
        $this->key = $var;

        return $this;
    }

    /**
     * 统一json格式
     *
     * Generated from protobuf field <code>string payload = 2;</code>
     * @return string
     */
    public function getPayload()
    {
        return $this->payload;
    }

    /**
     * 统一json格式
     *
     * Generated from protobuf field <code>string payload = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setPayload($var)
    {
        GPBUtil::checkString($var, True);
        $this->payload = $var;

        return $this;
    }

}

