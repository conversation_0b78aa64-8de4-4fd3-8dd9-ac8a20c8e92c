<?php
require_once(dirname(__FILE__).'/../define.php');
const ACCOUNT_GRADE_DIS = 11;
const ACCOUNT_GRADE_PM  = 31;

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "dbuser01";
    $dbpass = DB_PASSWORD;
    $dbname = "AKCS";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function getAccountByUUID(&$db, $account_uuid)
{
    $sql = "SELECT ID,Account,UUID,Language,Location,TimeZone,ManageGroup,SendExpireEmailType,ChargeMode 
            from Account A 
            where UUID=:account_uuid";

    $sth = $db->prepare($sql);
    $sth->bindParam(':account_uuid', $account_uuid, PDO::PARAM_STR);
    $sth->execute();
    return $sth->fetch(PDO::FETCH_ASSOC);
}

// 根据account_uuid，获取姓名、邮箱
function getAccountInfoByAccountUUID(&$db, $account_uuid)
{
    $sql = "SELECT A.ID,A.UUID,A.Account,A.Language,A.TimeZone,A.ChargeMode,AU.Email,AU.Phone 
            FROM Account A  
            INNER JOIN AccountMap AP ON AP.AccountUUID=A.UUID 
            INNER JOIN AccountUserInfo AU ON AU.UUID=AP.UserInfoUUID 
            WHERE A.UUID=:account_uuid;";

    $sth = $db->prepare($sql);
    $sth->bindParam(':account_uuid', $account_uuid, PDO::PARAM_STR);
    $sth->execute();

    $account_info = $sth->fetch(PDO::FETCH_ASSOC);
    $account_info['Email'] = DataConfusion::getInstance()->decrypt($account_info['Email']);
    return $account_info;
}

// 获取DistributorInfo的信息
function getOemTypeByProjectUUID(&$db, $project_uuid)
{
    $sql = "SELECT disInfo.OemType from DistributorInfo disInfo 
            left join Account dis on dis.Account = disInfo.Account 
            left join Account comm on comm.ParentUUID = dis.UUID 
            where comm.UUID =:projectUUID";

    $sth = $db->prepare($sql);
    $sth->bindParam(':projectUUID', $project_uuid, PDO::PARAM_STR);
    $sth->execute();
    return $sth->fetch(PDO::FETCH_ASSOC);
}

// 根据project_id，获取Dis姓名、邮箱等
function getDistributorByProjectUUID(&$db, $project_uuid)
{
    $sql = "SELECT D.UUID,D.Account,D.Language,D.TimeZone,D.ChargeMode,AU.Email,AU.Phone 
            FROM Account A 
            INNER JOIN Account D ON D.ID = A.ParentID 
            INNER JOIN AccountMap AP ON AP.AccountUUID=D.UUID 
            INNER JOIN AccountUserInfo AU ON AU.UUID=AP.UserInfoUUID WHERE A.UUID=:project_uuid;";

    $sth = $db->prepare($sql);
    $sth->bindParam(':project_uuid', $project_uuid, PDO::PARAM_STR);
    $sth->execute();

    $dis_info = $sth->fetch(PDO::FETCH_ASSOC);
    $dis_info['Email'] = DataConfusion::getInstance()->decrypt($dis_info['Email']);
    return $dis_info;
}

// 获取SubDistributorInfo的信息
function getSubDistributorInfoByProjectUUID(&$db, $project_uuid)
{
    $sql = "SELECT SA.UUID,SA.Account,SA.Language,SA.TimeZone,SA.ChargeMode,AU.Email,AU.Phone 
    FROM Account A 
    INNER JOIN Account I ON I.ID = A.ManageGroup 
    INNER JOIN SubDisMngList S ON S.InstallerUUID  = I.UUID 
    INNER JOIN AccountMap AP ON AP.AccountUUID=S.DistributorUUID 
    INNER JOIN Account SA ON SA.UUID = S.DistributorUUID 
    INNER JOIN AccountUserInfo AU ON AU.UUID=AP.UserInfoUUID WHERE A.UUID=:account_uuid;";

    $sth = $db->prepare($sql);
    $sth->bindParam(':account_uuid', $project_uuid, PDO::PARAM_STR);
    $sth->execute();

    $dis_info = $sth->fetch(PDO::FETCH_ASSOC);
    $dis_info['Email'] = DataConfusion::getInstance()->decrypt($dis_info['Email']);
    return $dis_info;
}

// 根据project_id，获取所有Ins的姓名、邮箱
function getInstallerByProjectUUID(&$db, $project_uuid)
{
    $sql = "SELECT I.UUID,I.Account,I.Language,I.TimeZone,I.PayType,AU.Email,AU.Phone 
            FROM Account A 
            INNER JOIN Account I ON I.ID = A.ManageGroup 
            INNER JOIN AccountMap AP ON AP.AccountUUID=I.UUID 
            INNER JOIN AccountUserInfo AU ON AU.UUID=AP.UserInfoUUID WHERE A.UUID=:account_uuid;";

    $sth = $db->prepare($sql);
    $sth->bindParam(':account_uuid', $project_uuid, PDO::PARAM_STR);
    $sth->execute();

    $ins_info = $sth->fetch(PDO::FETCH_ASSOC);
    $ins_info['Email'] = DataConfusion::getInstance()->decrypt($ins_info['Email']);
    return $ins_info;
}

// 根据project_id，获取所有PM的姓名、邮箱
function getPropertyManagerByProjectID(&$db, $project_id)
{
    $sql = "SELECT A.Account,A.UUID,A.Language,P.FirstName,P.LastName,AU.Email,AU.Phone 
            FROM PropertyMngList L
            INNER JOIN PropertyInfo P ON P.AccountID = L.PropertyID 
            INNER JOIN Account A ON A.ID = L.PropertyID 
            INNER JOIN AccountMap AP ON AP.AccountUUID=A.UUID 
            INNER JOIN AccountUserInfo AU ON AU.UUID=AP.UserInfoUUID 
            WHERE L.CommunityID=:project_id";

    $sth = $db->prepare($sql);
    $sth->bindParam(':project_id', $project_id, PDO::PARAM_INT);
    $sth->execute();

    $pm_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($pm_list as $row => $item){
        $pm_list[$row]['Email'] = DataConfusion::getInstance()->decrypt($item['Email']);
    }

    return $pm_list;
}

// 根据pm_uuid，获取PM的姓名、邮箱
function getPropertyManagerByPmUUID(&$db, $pm_uuid)
{
    $sql = "SELECT A.UUID,A.Language,P.FirstName,P.LastName,AU.Email,AU.Phone 
            FROM Account A 
            INNER JOIN PropertyInfo P ON P.AccountID = A.ID 
            INNER JOIN AccountMap AP ON AP.AccountUUID=A.UUID 
            INNER JOIN AccountUserInfo AU ON AU.UUID=AP.UserInfoUUID 
            WHERE A.UUID=:pm_uuid;";

    $sth = $db->prepare($sql);
    $sth->bindParam(':pm_uuid', $pm_uuid, PDO::PARAM_STR);
    $sth->execute();

    $pm_info = $sth->fetch(PDO::FETCH_ASSOC);
    $pm_info['Email'] = DataConfusion::getInstance()->decrypt($pm_info['Email']);
    return $pm_info;
}

// 获取超管设置的运营人员
function getSuperAdministrator(&$db)
{
    $sql = "SELECT CS.EmailForRentManager,A.Language 
            FROM Account A 
            INNER JOIN CustomerService CS ON CS.MngAccount=A.Account 
            WHERE A.Grade=1;";

    $sth = $db->prepare($sql);
    $sth->execute();
    return $sth->fetch(PDO::FETCH_ASSOC);
}




