<?php
/*
 * @Description: 操作用户
 * @version: Re1.0
 * @Author: kxl
 * @Date: 2020-01-15 15:37:06
 * @LastEditors  : kxl
 */
date_default_timezone_set("PRC");
require_once(__DIR__ . '/define.php');
include_once __DIR__ . "/../util/time.php";
require_once(__DIR__ . '/../../common/comm.php');
require_once(__DIR__ . '/../../common/define.php');
require_once(__DIR__ . '/../../common/log_db_comm.php');
require_once(__DIR__ . '/office_admin/export_doorlog.php');
require_once(__DIR__ . '/property_manager/export_doorlog.php');

/**
 * @brief NewOffice导出日志
 */
function NewofficeExportDoorLog($datas, $s3_cfg)
{
    $operator_type = $datas["operator_type"];

    if ($operator_type == OPERATOR_TYPE_ADMIN) {
        return newoffice\office_admin\doorlog\NewofficeAdminExportDoorLog($datas, $s3_cfg);
    } else if ($operator_type == OPERATOR_TYPE_PM) {
        return newoffice\property_manager\doorlog\NewofficePmExportDoorLog($datas, $s3_cfg);
    } else {
        LOG_ERROR("Unsupported operator_type: $operator_type.");
        $retinfo["result"] = -1;
        $retinfo["urls"] = [];
        return $retinfo;
    }
}
