[{"TestName": "PMAppExpire", "PayMode": "DIS", "ExpireDays": "-1", "SendType": "SendToDisOrIns", "TestResult": [{"EmailType": "pm_app_account_expire", "Email": "$PM_EMAIL"}]}, {"TestName": "PMAppExpire", "PayMode": "INS", "ExpireDays": "-1", "SendType": "SendToDisOrIns", "TestResult": [{"EmailType": "pm_app_account_expire", "Email": "$INS_EMAIL"}]}, {"TestName": "PMAppExpire", "PayMode": "INS", "ExpireDays": "-1", "SendType": "SendToPMOrEndUser", "TestResult": [{"EmailType": "pm_app_account_expire", "Email": "$PM_EMAIL"}]}, {"TestName": "PMAppExpire", "PayMode": "PM", "ExpireDays": "-1", "SendType": "SendToPMOrEndUser", "TestResult": [{"EmailType": "pm_app_account_expire", "Email": "$PM_EMAIL"}]}]