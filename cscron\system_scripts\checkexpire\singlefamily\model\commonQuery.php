<?php

class CommonQuery 
{
    private $db;
    private $medooDb;

    public function __construct($db, $medooDb) {
        $this->db = $db;
        $this->medooDb = $medooDb;
    }

    public function getPersonalChargeModeByInsUUID($insUUID){
        $sth = $this->db->prepare("select I.ID as InsID, I.UUID as InsUUID, I.Account as InsAccount, I.PayType as InstallerMode, 
                            I.Location, I.SendExpireEmailType as SendExpireEmailType,
                            D.ID as DisID,D.UUID as DisUUID,D.Account as DisAccount,D.ChargeMode as DistributorMode,
                            0 as ProjectMode 
                            from Account I 
                            join Account D on I.ParentUUID = D.UUID 
                            where I.UUID=:insUUID"
                        );
        $sth->bindParam(':insUUID', $insUUID, PDO::PARAM_STR);
        $sth->execute();
        return $sth->fetch(PDO::FETCH_ASSOC);
    }

    public function IsUserEnableAutoPay($userUUID, $autoPayType)
    {
        //用户可能有历史订单，取用户最新的订单
        $sth = $this->db->prepare("select A.Status from SubscriptionList A left join SubscriptionEndUserList B on A.UUID = B.SubscriptionUUID
        left join PersonalAccount C on B.PersonalAccountUUID = C.UUID where C.UUID = :uuid and B.Type = :autoPayType order by A.ID desc limit 1");
        $sth->bindParam(':uuid', $userUUID, PDO::PARAM_STR);
        $sth->bindParam(':autoPayType', $autoPayType, PDO::PARAM_INT);
        $sth->execute();
        $result = $sth->fetch(PDO::FETCH_ASSOC);
        if ($result && $result['Status'] == 1)
        {
            LOG_INFO("UUID:$userUUID EnableAutoPay");
            return true;
        }
        return false;
    }

    // 判断用户是否开启三方锁自动扣费
    public function IsUserEnableThirdPartyLockAutoPay($lockUUID, $autoPayType)
    {
        //用户可能有历史订单，取用户最新的订单
        $sth = $this->db->prepare("select A.Status from SubscriptionList A left join SubscriptionEndUserList B on A.UUID = B.SubscriptionUUID
        where B.LockUUID = :lockUUID and B.Type = :autoPayType order by A.ID desc limit 1");
        $sth->bindParam(':lockUUID', $lockUUID, PDO::PARAM_STR);
        $sth->bindParam(':autoPayType', $autoPayType, PDO::PARAM_INT);
        $sth->execute();
        $result = $sth->fetch(PDO::FETCH_ASSOC);
        if ($result && $result['Status'] == 1)
        {
            return true;
        }
        return false;
    }

    // 获取BillingInfo的email
    public function getBillingInfoByAccountUUID($accountUUID)
    {
        return $this->medooDb->get("InstallerBillingInfo", [
            "[>]Account" => ["InstallerBillingInfo.Account" => "Account"]
        ], [
            "InstallerBillingInfo.Email",
            "InstallerBillingInfo.Account",
            "Account.Language"
        ], [
            "Account.UUID" => $accountUUID
        ]);
    }

    public function getBillingInfoByPersonalAccountUUID($personalAccountUUID)
    {
        $personalAccount = $this->medooDb->get("PersonalAccount", [
            "Language", "RoomNumber", "Name", "UserInfoUUID"
        ], [
            "UUID" => $personalAccountUUID
        ]);
        $personalAccount['Account'] = $personalAccount['Name'];

        $personalAccountUserInfo = $this->medooDb->get("PersonalAccountUserInfo", [
            "Email", "MobileNumber"
        ], [
            "UUID" => $personalAccount['UserInfoUUID']
        ]);
        return array_merge($personalAccountUserInfo, $personalAccount);
    }

    // 获取dis oem name
    public function getDisOemType($disAcountUUID)
    {
        return $this->medooDb->get("DistributorInfo", [
            "[>]Account" => ["DistributorInfo.Account" => "Account"]
        ], [
            "DistributorInfo.OemType"
        ], [
            "Account.UUID" => $disAcountUUID
        ])['OemType'];
    }

    // 获取dis下的subdis列表
    public function getSubDisListInfoByDisUUID($disUUID)
    {
        $sth = $this->db->prepare("select Account, UUID, Language, ChargeMode as SubDisMode from Account where ParentUUID = :disUUID and Grade = 12");
        $sth->bindParam(':disUUID', $disUUID, PDO::PARAM_STR);
        $sth->execute();
        return $sth->fetchALL(PDO::FETCH_ASSOC);
    }

    // 获取subdis的subDisMngList
    public function getSubDisMngInstallerUUIDList($subDisUUID)
    {
        $sth = $this->db->prepare("select InstallerUUID as InsUUID from SubDisMngList where DistributorUUID = :subDisUUID");
        $sth->bindParam(':subDisUUID', $subDisUUID, PDO::PARAM_STR);
        $sth->execute();
        return $sth->fetchALL(PDO::FETCH_ASSOC);
    }
}