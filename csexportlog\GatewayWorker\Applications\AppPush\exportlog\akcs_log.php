<?php
/**
* PHP log 类
*/
date_default_timezone_set('Asia/shanghai');
const LOG_TAG="ExportLog";



const LOG_FILE_PATH="/var/log/csexportlog/csexportlog.log";

//'pid:'. posix_getpid()  获取的时进程id   Thread::getCurrentThreadId()但是这个id和实际不一致
function LOG_INFO($msg)
{
    file_put_contents(LOG_FILE_PATH, date('Y-m-d H:i:s') ." pid:". posix_getpid() . ' INFO ' . $msg, FILE_APPEND | LOCK_EX);
    file_put_contents(LOG_FILE_PATH, "\n", FILE_APPEND | LOCK_EX);
}

function LOG_ERROR($msg)
{
    file_put_contents(LOG_FILE_PATH, date('Y-m-d H:i:s') ." pid:". posix_getpid() .  ' ERROR ' . $msg, FILE_APPEND | LOCK_EX);
    file_put_contents(LOG_FILE_PATH, "\n", FILE_APPEND | LOCK_EX);
}

function LOG_WARING($msg)
{
    file_put_contents(LOG_FILE_PATH, date('Y-m-d H:i:s') ." pid:". posix_getpid() .  ' WARING ' . $msg, FILE_APPEND | LOCK_EX);
    file_put_contents(LOG_FILE_PATH, "\n", FILE_APPEND | LOCK_EX);
}
