<?php

class PmAppQuery 
{
    private $db;
    private $medooDb;

    public function __construct($db, $medooDb) {
        $this->db = $db;
        $this->medooDb = $medooDb;
    }

    public function getExpireData($daysBefore)
    {
        // 使用Medoo的条件表达方式
        $pmAppExpireList = $this->medooDb->select("PersonalAccount", ["UUID", "Name", "ExpireTime"],
            Medoo::raw("where TO_DAYS(ExpireTime) = TO_DAYS(NOW()) +  :daysBefore and Active = 1 and Role = :role",
            [':daysBefore' => $daysBefore, ':role' => ACCOUNT_ROLE_COMMUNITY_PM])
        );
        
        $result = [];
        foreach ($pmAppExpireList as $row => $pmApp) {
            $sth = $this->db->prepare("select AAA.UUID as DisUUID, AAA.Account as DisAccount, AA.UUID as InsUUID, AA.Account as InsAccount, 
                        A.UUID as CommunityUUID, A.ID as CommunityID, A.Location as Community, A.TimeZone, P.UUID as PersonalAccountUUID
                        from PersonalAccount P 
                        join Account A on A.UUID = P.ParentUUID 
                        join Account AA on AA.ManageGroup = A.ManageGroup 
                        join Account AAA on AAA.UUID = AA.ParentUUID 
                        where P.UUID = :uuid");
            
            $sth->bindValue(':uuid', $pmApp['UUID'], PDO::PARAM_STR);
            $sth->execute();
            $pmAppExpireInfo = $sth->fetch(PDO::FETCH_ASSOC);
            $completeInfo = array_merge($pmAppExpireInfo, ["Name" => $pmApp['Name']]);
            $result[] = $completeInfo;
        }

        // 根据 CommunityUUID 排序
        usort($result, function($a, $b) {
            return strcmp($a['CommunityUUID'], $b['CommunityUUID']);
        });

        LOG_INFO("pmAppExpireList: " . json_encode($result));
        return $result;
    }
}