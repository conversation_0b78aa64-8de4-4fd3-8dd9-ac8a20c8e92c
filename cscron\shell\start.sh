#!/bin/bash
# ****************************************************************************
# Author        :   zhengrong.xu
# Last modified :   2023-11-07
# Filename      :   install.sh
# Version       :
# Description   :   cscron 的远程启动脚本
# Modifier      :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
IMAGE_ADDR=${12}                #容器地址

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }

bash $RSYNC_PATH/build/install.sh $RSYNC_PATH $PROJECT_RUN_PATH $IMAGE_ADDR