<?php
require_once(dirname(__FILE__) . '/../model/residentAppQuery.php');

abstract class ExpireDataCheckUtil
{
    protected $db;
    protected $medooDb;
    protected $commonQuery;

    public function __construct($db, $medooDb) {
        $this->db = $db;
        $this->medooDb = $medooDb;
        $this->commonQuery = new CommonQuery($db, $medooDb);
    }

    // 获取过期数据
    abstract public function getExpireData($daysBefore);

    // 过滤迁移dis的社区
    protected function filterMigrateDistributor($expireData)
    {
        return array_filter($expireData, function($data) {
            if (empty($data['DisAccount'])) {
                return false;
            }
            return !self::checkIsMigrateDis($data['DisAccount']);
        });
    }

    protected function checkIsMigrateDis($disAccount)
    {
        if ($disAccount == null) {
            return 0;
        }

        if (SERVER_LOCATION != "as" && SERVER_LOCATION != "eu" ) {
            return 0;
        }

        global $jp_dis_array;
        global $au_dis_array;
        global $az_dis_array;
        global $eu_dis_array;

        if (SERVER_LOCATION == "as" && in_array($disAccount, $au_dis_array)) {
            LOG_INFO("Is aucloud Dis, xinjiapo not need handle! disAccount=$disAccount\n");
            return 1;
        }

        if (SERVER_LOCATION == "as" && in_array($disAccount, $jp_dis_array)) {
            LOG_INFO("Is Jp Dis, xinjiapo not need handle! disAccount=$disAccount\n");
            return 1;
        }

        if (SERVER_LOCATION == "eu" && in_array($disAccount, $az_dis_array)) {
            LOG_INFO("Is Az Dis, ecloud not need handle! disAccount=$disAccount\n");
            return 1;
        }
        if (SERVER_LOCATION == "eu" && in_array($disAccount, $eu_dis_array)) {
            LOG_INFO("Is e2ucloud Dis, ecloud not need handle! disAccount=$disAccount\n");
            return 1;
        }
        return 0;
    }

    // 过滤掉自动扣费的用户
    protected function filterAutoPayUser($expireData)
    {
        // 获取过期用户中的自动扣费用户
        $filterAutoPayUserList = [];
        foreach ($expireData as $data) {
            // 如果用户没有开通自动扣费，则加入到发送过期邮件列表中
            if ($data && !$this->commonQuery->IsUserEnableAutoPay($data['PersonalAccountUUID'], AUTO_PAY_ORDER_TYPE_COMMUNITY)) {
                $filterAutoPayUserList[] = $data;
            }
        }
        return $filterAutoPayUserList;
    }
}   
