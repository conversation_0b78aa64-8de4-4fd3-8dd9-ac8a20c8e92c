set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
ENV=$4
HOST=$5
HOSTNAME=$9
IMAGE_ADDR=${12}

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }
[[ -z "$PROJECT_RUN_PATH" ]] && { echo "【PROJECT_RUN_PATH】变量值不能为空"; exit 1; }

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}



# ============================================================================
# ==== main
# ============================================================================
cd "$(dirname "$0")"
PKG_ROOT=$(cd .. && pwd)

INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
WEB_INSTALL_CONF=$RSYNC_PATH/web_backend_install.conf
APP_NAME=cslinker    # 安装包中 bin 目录下应用程序的文件名
APP_HOME=/usr/local/akcs/cslinker
LOG_PATH=/var/log/cslinkerlog
KDC_CONF=/etc/kdc.conf
SIGNAL=${SIGNAL:-TERM}
CONFWATCH_EXEC=/usr/local/akcs/csconfwatch_exec

# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------
echo "Begin to install $APP_NAME"

echo '读取配置'

if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi


# 检查 KDC_CONF 文件是否存在
if [ ! -f "$KDC_CONF" ]; then
    echo "文件不存在： $KDC_CONF 不存在."
    exit 1
fi

ENABLE_DBPROXY=$(grep_conf 'ENABLE_DBPROXY' $INSTALL_CONF)
DBPROXY_INNER_IP=$(grep_conf 'DBPROXY_INNER_IP' $INSTALL_CONF)
MYSQL_INNER_IP=$(grep_conf 'MYSQL_INNER_IP' $INSTALL_CONF)
NSQLOOKUPD_INNER_IP=$(grep_conf 'NSQLOOKUPD_INNER_IP' $INSTALL_CONF)
ETCD_INNER_IP=$(grep_conf 'ETCD_INNER_IP' $INSTALL_CONF)
REDIS_INNER_IP=$(grep_conf 'REDIS_INNER_IP' $INSTALL_CONF)
ENABLE_REDIS_SENTINEL=$(grep_conf 'ENABLE_REDIS_SENTINEL' $INSTALL_CONF)
SENTINEL_HOSTS=$(grep_conf 'SENTINEL_HOSTS' $INSTALL_CONF)
KAFKA_INNER_IP=$(grep_conf 'KAFKA_INNER_IP' $INSTALL_CONF)
GATEWAY_NUM=$(grep_conf 'GATEWAY_NUM' $INSTALL_CONF)
WEB_BACKEND_DOMAIN=$(grep_conf 'WEB_BACKEND_DOMAIN' $INSTALL_CONF)
SMARTHOME_TOKEN=$(grep_conf 'SMARTHOME_TOKEN' $WEB_INSTALL_CONF)
SMARTHOME_DOMAIN=$(grep_conf 'SMARTHOME_DOMAIN' $WEB_INSTALL_CONF)

if [ "$ENABLE_DBPROXY" -eq 1 ]; then
    # websocket还未开启主从, 直接连接到主库
    MYSQL_INNER_IP="$DBPROXY_INNER_IP";
    DATABASEPORT=3308;	#默认3308
else
    MYSQL_INNER_IP="$MYSQL_INNER_IP";
    DATABASEPORT=3306;
fi

#获取数据库密码
ENCRYPTED_DB_PASSWORD=$(grep '^AKCS_DBUSER01=' $KDC_CONF | awk '{print substr($0, index($0, "=") + 1)}')

# 检查 /bin/crypto 是否存在
if [ ! -x "/bin/crypto" ]; then
    echo "加密工具 /bin/crypto 不存在或不可执行."
    exit 1
fi
# 检查 ENCRYPTED_DB_PASSWORD 是否为空
if [ -z "$ENCRYPTED_DB_PASSWORD" ]; then
    echo "错误: 获取到的加密数据库密码为空."
    exit 1
fi

# 解密数据库密码
DECRYPTED_DB_PASSWORD=$(echo "$ENCRYPTED_DB_PASSWORD" | /bin/crypto -d 2>/dev/null)

# 检查解密是否成功
if [ -z "$DECRYPTED_DB_PASSWORD" ]; then
    echo "错误：无法解密数据库密码！"
    exit 1
fi

# 替换配置文件
echo '替换配置文件的配置'

export ETCDCTL_API=3
SMART_GATE=`/bin/etcdctl --endpoints=$ETCD_INNER_IP get /akconf/smg/inner_addr`
SMART_GATE=`echo $SMART_GATE | awk '{print $2}' | tr -d '\n'`
WEB_ADAPT_ENTRY=`/bin/etcdctl --endpoints=$ETCD_INNER_IP get akcs/web/adaptEntry/appBackend --prefix`
WEB_ADAPT_ENTRY=`echo $WEB_ADAPT_ENTRY | awk '{print $2}' | tr -d '\n'`

if [ -z $SMART_GATE ];then
   echo "etcd获取网关失败  /akconf/smg/inner_addr"
   exit 1
fi
echo "get SMART_GATE ip : $SMART_GATE"

sed -i "
    s/^.*const NSQ_LOOKUPD.*/const NSQ_LOOKUPD=\"${NSQLOOKUPD_INNER_IP}:8511\";/g
    s/^.*const MYSQL_DB_IP.*/const MYSQL_DB_IP=\"${MYSQL_INNER_IP}\";/g
    s/^.*const MYSQL_DB_PORT.*/const MYSQL_DB_PORT=\"${DATABASEPORT}\";/g
    s/^.*const SMARTHOME_HTTP_GATE.*/const SMARTHOME_HTTP_GATE=\"${SMART_GATE}\";/g
    s/^.*const WEB_ADAPT_ENTRY.*/const WEB_ADAPT_ENTRY=\"${WEB_ADAPT_ENTRY}\";/g
    s/^.*const REDISIP.*/const REDISIP=\"${REDIS_INNER_IP}\";/g
    s/^.*const KAFKA_INNER_IP.*/const KAFKA_INNER_IP=\"${KAFKA_INNER_IP}:8520\";/g
    s/^.*const ETCD_INNER_IP.*/const ETCD_INNER_IP=\"${ETCD_INNER_IP}\";/g
    s/^.*const WEB_BACKEND_DOMAIN.*/const WEB_BACKEND_DOMAIN=\"${WEB_BACKEND_DOMAIN}\";/g
    s/^.*const MYSQL_DB_PWD.*/const MYSQL_DB_PWD=\"${DECRYPTED_DB_PASSWORD}\";/g
    s/^.*const GATEWAY_NUM.*/const GATEWAY_NUM=\"${GATEWAY_NUM}\";/g
    s/^.*const SMARTHOME_TOKEN.*/const SMARTHOME_TOKEN=\"${SMARTHOME_TOKEN}\";/g
    s/^.*const SMARTHOME_DOMAIN.*/const SMARTHOME_DOMAIN=\"${SMARTHOME_DOMAIN}\";/g

    " "$PKG_ROOT"/linker/app/common/define.php
	
if [ "$ENABLE_REDIS_SENTINEL" = "1" ]; then
    sed -i "s/^.*const REDIS_SENTINEL_HOSTS.*/const REDIS_SENTINEL_HOSTS=\"${SENTINEL_HOSTS}\";/g
	s/^.*const ENABLE_REDIS_SENTINEL.*/const ENABLE_REDIS_SENTINEL=\"${ENABLE_REDIS_SENTINEL}\";/g
	" "$PKG_ROOT"/linker/app/common/define.php
else
    sed -i "s/^.*const ENABLE_REDIS_SENTINEL.*/const ENABLE_REDIS_SENTINEL=\"${ENABLE_REDIS_SENTINEL}\";/g
	" "$PKG_ROOT"/linker/app/common/define.php
fi

# 日本云pacport生产环境配置
if [ "$GATEWAY_NUM" -eq 5 ]; then
    sed -i "
    s#^.*const PACPORT_PROD_ENV_SWITCH.*#const PACPORT_PROD_ENV_SWITCH=1;#g
    " "$PKG_ROOT"/linker/app/common/define.php
fi

echo "创建存放日志的文件夹 $LOG_PATH"
if [ ! -d $LOG_PATH ]; then
    mkdir -p $LOG_PATH
fi

echo "删除旧的安装文件 $APP_HOME"
if [ -d $APP_HOME ]; then
    rm -rf $APP_HOME
fi

echo '复制安装包的文件'
if [ ! -d $APP_HOME ]; then
    mkdir -p $APP_HOME
fi

cp -rf "$PKG_ROOT"/linker $APP_HOME
cp /etc/ip /usr/local/akcs/cslinker/linker/app
cp "$PKG_ROOT"/README.md /usr/local/akcs/cslinker/

mkdir -p $CONFWATCH_EXEC
cp -f "$PKG_ROOT"/shell/change_linker_conf_by_etcd.php "$CONFWATCH_EXEC"/
#复制Dockerfile，docker-compose并启动服务
# cp -rf "$PKG_ROOT"/Dockerfile $HTML_ROOT/../
# cp -rf "$PKG_ROOT"/docker-compose.yml $HTML_ROOT/../
# cp -rf "$PKG_ROOT"/docker-compose-prod.yml $HTML_ROOT/../


#docker变量
http_server_port=8799
log_path="-v /var/log/cslinkerlog:/var/log/cslinkerlog"
app_path="-v /usr/local/akcs/cslinker:/usr/local/akcs/cslinker"
etcd_path="-v /bin/etcdctl:/bin/etcdctl"
start_cmd="export ETCDCTL_API=3 && php /usr/local/akcs/cslinker/linker/start.php start"

#docker仓库拉取镜像
IMAGE_NAME=${IMAGE_ADDR}/ak_system/akcs-php8.0:1.0
docker pull $IMAGE_NAME

if [ `docker ps -a | grep $APP_NAME | wc -l` -gt 0 ];then docker stop $APP_NAME;docker rm $APP_NAME;fi

echo "docker run -d -e TZ=Asia/Shanghai --restart=always $log_path $app_path $etcd_path  -p $http_server_port:$http_server_port -p 9403:9403  --name $APP_NAME $IMAGE_NAME $start_cmd"
docker run -d -e TZ=Asia/Shanghai --restart=always $log_path $app_path $etcd_path -p $http_server_port:$http_server_port -p 9403:9403 --name $APP_NAME $IMAGE_NAME sh -c "$start_cmd"

#监控插件安装
echo "===执行监控安装==="
bash -x $RSYNC_PATH/shell/monitor.sh $RSYNC_PATH $PROJECT_RUN_PATH $ENV $HOST $HOSTNAME

echo "$APP_NAME install complete."
