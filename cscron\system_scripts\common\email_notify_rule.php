
<?php
require_once(dirname(__FILE__) . '/define.php');
require_once(dirname(__FILE__) . '/../define.php');

// 邮件发送规则类
class EmailNotifyRule
{
    // 支付模式
    const PAY_MODE_NORMAL                       = 0;    // 普通支付
    const PAY_MODE_NO_PERMISSION                = 1;    // 没有权限
    const PAY_MODE_CREDIT                       = 2;    // 信用卡支付

    // Account 表的 SendExpireEmailType 字段
    const SEND_TO_MYSELF                        = 1;    // 发送给自己
    const SEND_TO_PM_OR_ENDUSER                 = 2;    // 发送给PM或者EndUser
    const SEND_TO_PM                            = 3;    // 发送给PM

    // 付费项目类型
    const PAY_ITEM_PM_APP                       = 1;    // PM APP
    const PAY_ITEM_USER_APP                     = 2;    // 住户 APP
    const PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT     = 3;    // 三方锁（公共锁+APT锁）
    const PAY_ITEM_VIDEO_RECORD                 = 4;    // 视频存储
    const PAY_ITEM_FEATURE_PLAN                 = 5;    // 高级功能

    // 初始化通知对象
    private const INIT_NOTIFY_RULE = array(
        "dis" => 0,
        "ins" => 0,
        "pm" => 0,
        "enduser" => 0,
    );

    // 获取社区项目的通知对象
    static private function
    GetResidenceNotifyTarget($subdis_pay_mode, $ins_pay_mode, $pm_pay_mode, $send_expiration_type, $pay_item, $remain_days)
    {
        $result = EmailNotifyRule::INIT_NOTIFY_RULE;

        // Ins没有付费权限 或 Ins有信用卡付费权限
        if ($ins_pay_mode == EmailNotifyRule::PAY_MODE_NO_PERMISSION || $ins_pay_mode == EmailNotifyRule::PAY_MODE_CREDIT) {
            // 住户APP 
            if ($pay_item == EmailNotifyRule::PAY_ITEM_USER_APP) {
                if ($remain_days == 15) {
                    $result["dis"] = 1;
                } else if ($remain_days == 5) {
                    $result["dis"] = 1;
                    $result["pm"] = 1;
                } else if ($remain_days == 3 || $remain_days == -1) {
                    $result["enduser"] = 1;
                }
            }
            // PM APP + 视频存储 + 社区高级功能
            else if ($pay_item == EmailNotifyRule::PAY_ITEM_PM_APP 
                || $pay_item == EmailNotifyRule::PAY_ITEM_VIDEO_RECORD
                || $pay_item == EmailNotifyRule::PAY_ITEM_FEATURE_PLAN) {
                if ($remain_days == 15) {
                    $result["dis"] = 1;
                } else if ($remain_days == 5) {
                    $result["dis"] = 1;
                    $result["pm"] = 1;
                } else if ($remain_days == 3 || $remain_days == -1) {
                    $result["pm"] = 1;
                }
            }
            //三方锁（公共锁+APT锁）
            else if ($pay_item == EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT) {
                if ($remain_days == 15) {
                    $result["dis"] = 1;
                } else if ($remain_days == 5) {
                    $result["dis"] = 1;
                    $result["pm"] = 1;
                } else if ($remain_days == 3 || $remain_days == -1) {
                    $result["pm"] = 1;
                    $result["enduser"] = 1;
                }
            }
        }
        // Ins有普通线上付费权限
        else if ($ins_pay_mode == EmailNotifyRule::PAY_MODE_NORMAL) {
            // 住户APP
            if ($pay_item == EmailNotifyRule::PAY_ITEM_USER_APP) {
                if ($pm_pay_mode == EmailNotifyRule::PAY_MODE_NO_PERMISSION) {
                    if ($send_expiration_type == SEND_TO_MYSELF) {
                        $result["ins"] = 1;
                    } else if ($send_expiration_type == SEND_TO_PM_OR_ENDUSER) {
                        if ($remain_days == 15) {
                            $result["ins"] = 1;
                        } else if ($remain_days == 5) {
                            $result["ins"] = 1;
                            $result["pm"] = 1;
                        } else if ($remain_days == 3 || $remain_days == -1) {
                            $result["enduser"] = 1;
                        }
                    } else if ($send_expiration_type == SEND_TO_PM) {
                        if ($remain_days == 15) {
                            $result["ins"] = 1;
                        } else if ($remain_days == 5) {
                            $result["ins"] = 1;
                            $result["pm"] = 1;
                        } else if ($remain_days == 3 || $remain_days == -1) {
                            $result["pm"] = 1;
                        }
                    }
                } else if ($pm_pay_mode == EmailNotifyRule::PAY_MODE_NORMAL) {
                    if ($remain_days == 15 || $remain_days == 5) {
                        $result["pm"] = 1;
                    } else if ($remain_days == 3 || $remain_days == -1) {
                        $result["enduser"] = 1;
                    }
                }
            }
            // PM APP + 视频存储 + 高级功能
            else if (
                $pay_item == EmailNotifyRule::PAY_ITEM_PM_APP ||
                $pay_item == EmailNotifyRule::PAY_ITEM_VIDEO_RECORD ||
                $pay_item == EmailNotifyRule::PAY_ITEM_FEATURE_PLAN
            ) {
                if ($pm_pay_mode == EmailNotifyRule::PAY_MODE_NO_PERMISSION) {
                    if ($send_expiration_type == SEND_TO_MYSELF) {
                        $result["ins"] = 1;
                    } else if ($send_expiration_type == SEND_TO_PM_OR_ENDUSER || $send_expiration_type == SEND_TO_PM) {
                        if ($remain_days == 15) {
                            $result["ins"] = 1;
                        } else if ($remain_days == 5) {
                            $result["ins"] = 1;
                            $result["pm"] = 1;
                        } else if ($remain_days == 3 || $remain_days == -1) {
                            $result["pm"] = 1;
                        }
                    }
                } else if ($pm_pay_mode == EmailNotifyRule::PAY_MODE_NORMAL) {
                    $result["pm"] = 1;
                }
            }
            //三方锁（公共锁+APT锁）
            else if ($pay_item == EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT) {
                if ($pm_pay_mode == EmailNotifyRule::PAY_MODE_NO_PERMISSION) {
                    if ($send_expiration_type == SEND_TO_MYSELF) {
                        $result["ins"] = 1;
                    } else if ($send_expiration_type == SEND_TO_PM_OR_ENDUSER) {
                        if ($remain_days == 15) {
                            $result["ins"] = 1;
                        } else if ($remain_days == 5) {
                            $result["ins"] = 1;
                            $result["pm"] = 1;
                        } else if ($remain_days == 3 || $remain_days == -1) {
                            $result["pm"] = 1;
                            $result["enduser"] = 1;
                        }
                    } else if ($send_expiration_type == SEND_TO_PM) {
                        if ($remain_days == 15) {
                            $result["ins"] = 1;
                        } else if ($remain_days == 5) {
                            $result["ins"] = 1;
                            $result["pm"] = 1;
                        } else if ($remain_days == 3 || $remain_days == -1) {
                            $result["pm"] = 1;
                        }
                    }
                } else if ($pm_pay_mode == EmailNotifyRule::PAY_MODE_NORMAL) {
                    if ($remain_days == 15 || $remain_days == 5) {
                        $result["pm"] = 1;
                    } else if ($remain_days == 3 || $remain_days == -1) {
                        $result["pm"] = 1;
                        $result["enduser"] = 1;
                    }
                }
            }
        }

        return $result;
    }

    // 获取单住户项目的通知对象
    static private function
    GetPersonalNotifyTarget($subdis_pay_mode, $ins_pay_mode, $pm_pay_mode, $send_expiration_type, $pay_item, $remain_days)
    {
        $result = EmailNotifyRule::INIT_NOTIFY_RULE;

        // Ins没有付费权限 || Ins有信用卡付费权限
        if ($ins_pay_mode == EmailNotifyRule::PAY_MODE_NO_PERMISSION || $ins_pay_mode == EmailNotifyRule::PAY_MODE_CREDIT) {
            if ($remain_days == 15 || $remain_days == 5) {
                $result["dis"] = 1;
            } else if ($remain_days == 3 || $remain_days == -1) {
                $result["enduser"] = 1;
            }
        }

        // Ins有普通线上付费权限
        else if ($ins_pay_mode == EmailNotifyRule::PAY_MODE_NORMAL) {
            // 不发送给EndUser
            if ($send_expiration_type == SEND_TO_MYSELF) {
                $result["ins"] = 1;
            }
            // 发送给EndUser
            else {
                if ($remain_days == 15 || $remain_days == 5) {
                    $result["ins"] = 1;
                } else if ($remain_days == 3 || $remain_days == -1) {
                    $result["enduser"] = 1;
                }
            }
        }

        return $result;
    }


    // 获取旧办公项目的通知对象
    static private function
    GetOldOfficeNotifyTarget($subdis_pay_mode, $ins_pay_mode, $pm_pay_mode, $send_expiration_type, $pay_item, $remain_days)
    {
        $result = EmailNotifyRule::INIT_NOTIFY_RULE;

        // Ins没有付费权限 || Ins有信用卡付费权限
        if ($ins_pay_mode == EmailNotifyRule::PAY_MODE_NO_PERMISSION || $ins_pay_mode == EmailNotifyRule::PAY_MODE_CREDIT) {
            if ($remain_days == 15) {
                $result["dis"] = 1;
            } else if ($remain_days == 5) {
                $result["dis"] = 1;
                $result["pm"] = 1;
            } else if ($remain_days == 3 || $remain_days == -1) {
                $result["pm"] = 1;
            }
        }

        // Ins有线上付费权限
        else {
            // PM没有付费权限
            if($pm_pay_mode == EmailNotifyRule::PAY_MODE_NO_PERMISSION){
                // 不发送给PM
                if ($send_expiration_type == SEND_TO_MYSELF) {
                    $result["ins"] = 1;
                }
                // 发送给PM
                else {
                    if ($remain_days == 15) {
                        $result["ins"] = 1;
                    } else if ($remain_days == 5) {
                        $result["ins"] = 1;
                        $result["pm"] = 1;
                    } else if ($remain_days == 3 || $remain_days == -1) {
                        $result["pm"] = 1;
                    }
                }
            }
            // PM有线上付费权限
            else{
                $result["pm"] = 1;
            }
        }

        return $result;
    }

    // 获取新办公项目的通知对象
    static private function
    GetNewOfficeNotifyTarget($subdis_pay_mode, $ins_pay_mode, $pm_pay_mode, $send_expiration_type, $pay_item, $remain_days)
    {
        $result = EmailNotifyRule::INIT_NOTIFY_RULE;

        // Ins没有付费权限
        if ($ins_pay_mode == EmailNotifyRule::PAY_MODE_NO_PERMISSION) {
            if ($remain_days == 15) {
                $result["dis"] = 1;
            } else if ($remain_days == 5) {
                $result["dis"] = 1;
                $result["pm"] = 1;
            } else if ($remain_days == 3 || $remain_days == -1) {
                $result["pm"] = 1;
            }
        }

        // Ins有付费权限
        else {
            // 不发送给PM
            if ($send_expiration_type == SEND_TO_MYSELF) {
                $result["ins"] = 1;
            }
            // 发送给PM
            else {
                if ($remain_days == 15) {
                    $result["ins"] = 1;
                } else if ($remain_days == 5) {
                    $result["ins"] = 1;
                    $result["pm"] = 1;
                } else if ($remain_days == 3 || $remain_days == -1) {
                    $result["pm"] = 1;
                }
            }
        }

        return $result;
    }

    /** 
     * 获取邮件通知的对象
     * @param $project_type         项目类型
     * @param $subdis_pay_mode      子分销商支付模式
     * @param $ins_pay_mode         Ins支付模式
     * @param $pm_pay_mode          PM支付模式
     * @param $send_expiration_type 发送到期通知的方式
     * @param $pay_item             支付项
     * @param $remain_days          剩余天数
     * 
     * @return array 通知对象
     */
    static private function
    GetEmailNotifyTarget($project_type, $subdis_pay_mode, $ins_pay_mode, $pm_pay_mode, $send_expiration_type, $pay_item, $remain_days)
    {
        if ($project_type == PROJECT_TYPE_RESIDENCE) {
            // 注意：社区，send_expiration_type应该取项目在Account表的send_expiration_type字段值
            return EmailNotifyRule::GetResidenceNotifyTarget($subdis_pay_mode, $ins_pay_mode, $pm_pay_mode, $send_expiration_type, $pay_item, $remain_days);
        } else if ($project_type == PROJECT_TYPE_PERSONAL) {
            // 注意：单住户，send_expiration_type应该取Ins在Account表的send_expiration_type字段值
            return EmailNotifyRule::GetPersonalNotifyTarget($subdis_pay_mode, $ins_pay_mode, $pm_pay_mode, $send_expiration_type, $pay_item, $remain_days);
        } else if ($project_type == PROJECT_TYPE_OFFICE) {
            // 注意：旧办公，send_expiration_type应该取项目在Account表的send_expiration_type字段值
            return EmailNotifyRule::GetOldOfficeNotifyTarget($subdis_pay_mode, $ins_pay_mode, $pm_pay_mode, $send_expiration_type, $pay_item, $remain_days);
        } 
        else if ($project_type == PROJECT_TYPE_NEW_OFFICE) {
            // 注意：新办公，send_expiration_type应该取项目在Account表的send_expiration_type字段值
            return EmailNotifyRule::GetNewOfficeNotifyTarget($subdis_pay_mode, $ins_pay_mode, $pm_pay_mode, $send_expiration_type, $pay_item, $remain_days);
        } else {
            return EmailNotifyRule::INIT_NOTIFY_RULE;
        }
    }

    static public function
    IfNeedNotifyDis($project_type, $subdis_pay_mode, $ins_pay_mode, $pm_pay_mode, $send_expiration_type, $pay_item, $remain_days)
    {
        $target = EmailNotifyRule::GetEmailNotifyTarget($project_type, $subdis_pay_mode, $ins_pay_mode, $pm_pay_mode, $send_expiration_type, $pay_item, $remain_days);
        return $target["dis"];
    }

    static public function
    IfNeedNotifyIns($project_type, $subdis_pay_mode, $ins_pay_mode, $pm_pay_mode, $send_expiration_type, $pay_item, $remain_days)
    {
        $target = EmailNotifyRule::GetEmailNotifyTarget($project_type, $subdis_pay_mode, $ins_pay_mode, $pm_pay_mode, $send_expiration_type, $pay_item, $remain_days);

        return $target["ins"];
    }

    static public function
    IfNeedNotifyPm($project_type, $subdis_pay_mode, $ins_pay_mode, $pm_pay_mode, $send_expiration_type, $pay_item, $remain_days)
    {
        $target = EmailNotifyRule::GetEmailNotifyTarget($project_type, $subdis_pay_mode, $ins_pay_mode, $pm_pay_mode, $send_expiration_type, $pay_item, $remain_days);

        return $target["pm"];
    }

    static public function
    IfNeedNotifyEnduser($project_type, $subdis_pay_mode, $ins_pay_mode, $pm_pay_mode, $send_expiration_type, $pay_item, $remain_days)
    {
        $target = EmailNotifyRule::GetEmailNotifyTarget($project_type, $subdis_pay_mode, $ins_pay_mode, $pm_pay_mode, $send_expiration_type, $pay_item, $remain_days);

        return $target["enduser"];
    }

    static public function
    IfHasPayPermission($charge_mode)
    {
        if ($charge_mode == EmailNotifyRule::PAY_MODE_NORMAL || $charge_mode == EmailNotifyRule::PAY_MODE_CREDIT) {
            return true;
        }

        return false;
    }
}
