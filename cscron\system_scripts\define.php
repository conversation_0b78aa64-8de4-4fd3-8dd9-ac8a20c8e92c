<?php
// 项目类型
const PROJECT_TYPE_RESIDENCE = 0;
const PROJECT_TYPE_OFFICE = 1;
const PROJECT_TYPE_PERSONAL = 2;
const PROJECT_TYPE_NEW_OFFICE = 3;

//kafka相关定义
const KAFKA_NOTIFY_APP_BACKEND_TOPIC = "notify_app_backend";
const KAFKA_INNER_IP = "*************";

// 日志文件
const LOG_FILE_PATH="/var/log/cscronlog/cscronlog.log";
// 
const UNIX_DOMAIN = "/var/adapt_sock/adapt.sock";

// 邮箱加密的秘钥、填充串
const EMAIL_AES_ENCRYPT_IV = "0123456789000000";
const EMAIL_AES_ENCRYPT_KEY = "Akuvox1956131*69czeahaaew216023*";

//数据库密码
const DB_PASSWORD = "";