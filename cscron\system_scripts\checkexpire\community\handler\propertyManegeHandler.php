<?php

class PropertyManegeHandler 
{
    private $daysBefore;
    private $expireData;
    private $communityExpireInfoList;
    private $communityChargeModeMap;
    public $expireDataControl;

    public function __construct() {
    }
    
    public function setExpireParams($daysBefore, $expireData, $communityExpireInfoList, $communityChargeModeMap, $expireDataControl) {
        $this->daysBefore = $daysBefore;
        $this->expireData = $expireData;
        $this->communityExpireInfoList = $communityExpireInfoList;
        $this->communityChargeModeMap = $communityChargeModeMap;
        $this->expireDataControl = $expireDataControl;
    }

    public function filterData() {
        // 判断社区的付费模式 是否要发送给pm
        $filteredExpireData = $this->filterCommunityChargeMode();
        return $filteredExpireData;
    }

    // 判断社区的付费模式
    private function filterCommunityChargeMode() {
        // 判断社区的付费模式 是否要发送给Pm
        $payTypeList = array(
            EmailNotifyRule::PAY_ITEM_USER_APP,
            EmailNotifyRule::PAY_ITEM_PM_APP,
            EmailNotifyRule::PAY_ITEM_VIDEO_RECORD,
            EmailNotifyRule::PAY_ITEM_FEATURE_PLAN,
            EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT,
        );

        // 获取每种付费类型发送给pm的社区列表
        $payTypeCommunityUUIDMap = array(); 
        foreach ($payTypeList as $payType) {
            foreach ($this->communityExpireInfoList as $communityInfo) {
                $communityChargeMode = $this->communityChargeModeMap[$communityInfo['UUID']];
                if ($communityChargeMode && CommunityCheck::needNotifyPm($payType, $communityChargeMode, $this->daysBefore)) {
                    $payTypeCommunityUUIDMap[$payType][] = $communityInfo['UUID'];
                }
            }
        }

        // 获取每种付费类型发送给pm的社区列表
        $filteredExpireData = array();
        foreach ($this->expireData as $payType => $expireList) {
            foreach ($expireList as $expireInfo) {
                if (isset($payTypeCommunityUUIDMap[$payType]) && in_array($expireInfo['CommunityUUID'], $payTypeCommunityUUIDMap[$payType])) {
                    $filteredExpireData[$payType][] = $expireInfo;
                }
            }
        }
        return $filteredExpireData;
    }   

    public function sendEmail($emailDataMap, $recevierInfoMap) {
        foreach ($emailDataMap as $pmUUID => $pmEmailData) {
            $pmEmail = $recevierInfoMap[$pmUUID]['email'];
            $loginAccount = $recevierInfoMap[$pmUUID]['loginAccount'];
            if (empty($pmEmail)) {
                LOG_INFO("pmEmail is empty, skip, pmUUID: " . $pmUUID);
                continue;
            }

            // ins下同一个时区的社区一起发送
            $timezoneEmailDataMap = $this->getTimeZoneEmailDataMap($pmEmailData);
            foreach ($timezoneEmailDataMap as $timezone => $emailData) {
                $emailInfo['email'] = $pmEmail;
                $emailInfo["web_domain"] = WEB_DOMAIN;
                $emailInfo['before'] = $this->daysBefore;
                $emailInfo['login_account'] = $loginAccount;
                $emailInfo['oem'] = $recevierInfoMap[$pmUUID]['oem'];
                $emailInfo['user'] = $recevierInfoMap[$pmUUID]['user'];
                $emailInfo['language'] = $recevierInfoMap[$pmUUID]['language'];
                $emailInfo['is_show_paylink'] = SHOW_PAYLINK_PM;
                
                if ($this->daysBefore == -1) {
                    $emailInfo['email_type'] = "community_has_expired";
                } else {
                    $emailInfo['email_type'] = "community_will_expire";
                }
    
                $this->recordRenewPaymentList($emailData);
    
                $emailInfo['list'] = $emailData;
                sendEmailNotifyNew($emailInfo);
            }
        }
    }
    
    // pm下同一个时区的一起发送
    private function getTimeZoneEmailDataMap($insEmailData) {
        $timeZoneEmailDataMap = array();
        foreach ($insEmailData as $payType => $dataList) {
            foreach ($dataList as $data) {
                $timeZoneEmailDataMap[$data['TimeZone']][$payType][] = $data;
            }
        }
        return $timeZoneEmailDataMap;
    }

    // 存在有支付权限的社区才展示, 否则不展示
    private function recordRenewPaymentList($emailData) {
    
        $communityUUIDList = array();
        foreach ($emailData as $payItem => $dataList) {
            foreach ($dataList as $data) {
                $communityUUID = $data['CommunityUUID'];
                $chargeMode = $this->communityChargeModeMap[$communityUUID];

                $pmMode = $chargeMode['ProjectMode'];
                $insMode = $chargeMode['InstallerMode'];

                // Ins和PM不一样的时候，视为没有权限（ins修改了支付方式 项目要保留旧的值，代表没有支付权限）
                if($insMode != $pmMode) {
                    continue;
                }
        
                if (EmailNotifyRule::IfHasPayPermission($pmMode)) {
                    $communityUUIDList[] = $communityUUID;
                }
            }
        }
        
        $renewUUID = $this->expireDataControl->commonQuery->GenerateUUID();
        $communityUUIDList = array_unique($communityUUIDList);
        foreach ($communityUUIDList as $communityUUID) {
            $this->expireDataControl->commonQuery->recordEmailRenewPayment($renewUUID, $communityUUID);
        }
        return $renewUUID;
    }
}