<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: AK.CrontabOffice.proto

namespace AK\CrontabOffice;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>AK.CrontabOffice.PMAppExpire</code>
 */
class PMAppExpire extends \Google\Protobuf\Internal\Message
{
    /**
     *cmd id:   MSG_P2A_NOTIFY_OFFICE_PM_ACCOUNT_EXPIRE
     *
     * Generated from protobuf field <code>string community = 1;</code>
     */
    private $community = '';
    /**
     * Generated from protobuf field <code>string email = 2;</code>
     */
    private $email = '';
    /**
     * Generated from protobuf field <code>string name = 3;</code>
     */
    private $name = '';
    /**
     * Generated from protobuf field <code>int32 account_num = 4;</code>
     */
    private $account_num = 0;
    /**
     * Generated from protobuf field <code>string list = 5;</code>
     */
    private $list = '';

    public function __construct() {
        \GPBMetadata\AKCrontabOffice::initOnce();
        parent::__construct();
    }

    /**
     *cmd id:   MSG_P2A_NOTIFY_OFFICE_PM_ACCOUNT_EXPIRE
     *
     * Generated from protobuf field <code>string community = 1;</code>
     * @return string
     */
    public function getCommunity()
    {
        return $this->community;
    }

    /**
     *cmd id:   MSG_P2A_NOTIFY_OFFICE_PM_ACCOUNT_EXPIRE
     *
     * Generated from protobuf field <code>string community = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setCommunity($var)
    {
        GPBUtil::checkString($var, True);
        $this->community = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string email = 2;</code>
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * Generated from protobuf field <code>string email = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setEmail($var)
    {
        GPBUtil::checkString($var, True);
        $this->email = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string name = 3;</code>
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * Generated from protobuf field <code>string name = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 account_num = 4;</code>
     * @return int
     */
    public function getAccountNum()
    {
        return $this->account_num;
    }

    /**
     * Generated from protobuf field <code>int32 account_num = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setAccountNum($var)
    {
        GPBUtil::checkInt32($var);
        $this->account_num = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string list = 5;</code>
     * @return string
     */
    public function getList()
    {
        return $this->list;
    }

    /**
     * Generated from protobuf field <code>string list = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setList($var)
    {
        GPBUtil::checkString($var, True);
        $this->list = $var;

        return $this;
    }

}

