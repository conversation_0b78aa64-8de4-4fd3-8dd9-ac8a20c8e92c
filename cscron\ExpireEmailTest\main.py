# coding=utf-8

import os
import sys
import time
import argparse
import common.globals
from common.db import GetDbConn
from common.handle_ini import *
from test_detect.detect import *

def read_ini_config(filepath):
    '''
        读取ini配置文件
    '''
    config = parse_ini_file(filepath)
    return config

def parse_argument():
    """
        解析输入参数, 选择测试的环境. 当前支持的环境: sz/sh
    """
    parser = argparse.ArgumentParser(description="Automated testing cscron script")
    parser.add_argument('env', help='Select the test environment, supported: sz/sh')

    args = parser.parse_args()
    if not args.env:
        parser.error("Error: Please provide a value for the argument: env")
        sys.exit(1)

    return args

def automated_testing(env_ini):
    '''
        部署到开发主机dev3(部署cscron)进行测试，目前要求csliner和cscron要部署到同一台
    '''
    testSingle(env_ini)
    testCommUser(env_ini)
    testOfficeUser(env_ini)
    testOfficeFeature(env_ini)
    testPMApp(env_ini)
    testAutoPay(env_ini)
    testVideoRecord(env_ini)
    testRentManager(env_ini)
    testDeviceOffline(env_ini)

if __name__ == "__main__":
    args = parse_argument()
    print("Input env argument  :", args.env)

    # 读 配置文件
    env_int_file = "./config/env_{}.ini".format(args.env)
    print("Read env config from:", env_int_file)

    if(os.path.exists(env_int_file) == False):
        print("No such config file :", env_int_file)
        sys.exit(1)

    env_config = read_ini_config(env_int_file)
    common.globals.g_server_env = env_config['server']
    env_ini = env_config['env']

    # 开始测试
    automated_testing(env_ini)
