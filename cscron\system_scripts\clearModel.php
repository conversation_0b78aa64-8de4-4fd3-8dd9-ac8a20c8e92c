<?php

require_once(dirname(__FILE__).'/define.php');


date_default_timezone_set("PRC");

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "dbuser01";
    $dbpass = DB_PASSWORD;
    $dbname = "AKCS";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}


function clearModel($table)
{
    $db = getDB();
    //不做表是否存在的处理，因为如果不存在也就直接报错退出了
    $sth = $db->prepare("select ID,ModelUrl from $table where TO_DAYS(NOW()) - TO_DAYS(UploadTime) > 0 limit 0,1000;");
    $sth->execute();
    $model_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    if (empty($model_list))
    {
        return 0;
    }

    foreach ($model_list as $row => $model)
    {
        $sth2 = $db->prepare("delete from $table where id=:ID");
        $sth2->bindParam(':ID', $model['ID'], PDO::PARAM_INT);
        $sth2->execute();
        
        $url = $model["ModelUrl"];
        if (strlen($url) > 0)
        {
            $cmd="curl -s -d \"$url\" \"http://lookupdaddr:8513/pub?topic=delpic\" >/dev/null";
        }       
        shell_exec($cmd);
    }
    return 1;

}


while (clearModel("FaceModel"))
{
      
}

?>
