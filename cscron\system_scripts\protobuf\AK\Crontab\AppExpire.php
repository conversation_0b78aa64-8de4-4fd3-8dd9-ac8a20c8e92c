<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: AK.Crontab.proto

namespace AK\Crontab;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>AK.Crontab.AppExpire</code>
 */
class AppExpire extends \Google\Protobuf\Internal\Message
{
    /**
     *cmd id:   MSG_P2A_APP_EXPIRE
     *
     * Generated from protobuf field <code>string uid = 1;</code>
     */
    private $uid = '';
    /**
     * Generated from protobuf field <code>string user_name = 2;</code>
     */
    private $user_name = '';
    /**
     * Generated from protobuf field <code>string email = 3;</code>
     */
    private $email = '';
    /**
     * Generated from protobuf field <code>string community = 4;</code>
     */
    private $community = '';

    public function __construct() {
        \GPBMetadata\AKCrontab::initOnce();
        parent::__construct();
    }

    /**
     *cmd id:   MSG_P2A_APP_EXPIRE
     *
     * Generated from protobuf field <code>string uid = 1;</code>
     * @return string
     */
    public function getUid()
    {
        return $this->uid;
    }

    /**
     *cmd id:   MSG_P2A_APP_EXPIRE
     *
     * Generated from protobuf field <code>string uid = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setUid($var)
    {
        GPBUtil::checkString($var, True);
        $this->uid = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string user_name = 2;</code>
     * @return string
     */
    public function getUserName()
    {
        return $this->user_name;
    }

    /**
     * Generated from protobuf field <code>string user_name = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setUserName($var)
    {
        GPBUtil::checkString($var, True);
        $this->user_name = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string email = 3;</code>
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * Generated from protobuf field <code>string email = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setEmail($var)
    {
        GPBUtil::checkString($var, True);
        $this->email = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string community = 4;</code>
     * @return string
     */
    public function getCommunity()
    {
        return $this->community;
    }

    /**
     * Generated from protobuf field <code>string community = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setCommunity($var)
    {
        GPBUtil::checkString($var, True);
        $this->community = $var;

        return $this;
    }

}

