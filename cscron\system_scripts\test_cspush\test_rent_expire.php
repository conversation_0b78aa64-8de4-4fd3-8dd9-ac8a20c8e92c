<?php
/* 过期邮件测试 - 构造邮件数据并发到kafka，cspush根据配置的内容发送邮件 */
/* RentManager功能过期检测逻辑（对于的业务逻辑文件: check_rent_expire.php） */
require_once(dirname(__FILE__) . '/../checkexpire/rent_manager/rent_common.php');
require_once(dirname(__FILE__) . '/../checkexpire/check_expire_common_v4500.php');

// 发送 "即将过期的PM账号列表" 给 ins用户
function rent_customer_will_expire_to_ins()
{
    $emailInfo = [
        "user" => "ins wjh",
        "remaining_days" => "12",
        "email" => "<EMAIL>",
        "email_type" => "rent_customer_will_expire_to_ins",
        "pay_url" => "http:\/\/www.akuvox.com\/",
        "language" => "zh-cn",
        "pm_list" => [
            [
                "company_name" => "company01",
                "company_code" => "123456",
                "pm_name" => "property manager1",
                "pm_email" => "<EMAIL>"
            ],
            [
                "company_name" => "company01",
                "company_code" => "123456",
                "pm_name" => "property manager2",
                "pm_email" => "<EMAIL>"
            ],
            [
                "company_name" => "company02",
                "company_code" => "123456",
                "pm_name" => "property manager3",
                "pm_email" => "<EMAIL>"
            ]
        ]
    ];
    SendRentEmailNotify($emailInfo);
}

// 发送 "已经过期的PM账号列表" 给 ins用户
function rent_customer_has_expired_to_ins()
{
    $emailInfo = [
        "user" => "ins wjh",
        "remaining_days" => "12",
        "email" => "<EMAIL>",
        "email_type" => "rent_customer_has_expired_to_ins",
        "pay_url" => "http:\/\/www.akuvox.com\/",
        "language" => "zh-cn",
        "pm_list" => [
            [
                "company_name" => "company01",
                "company_code" => "123456",
                "pm_name" => "property manager1",
                "pm_email" => "<EMAIL>"
            ],
            [
                "company_name" => "company01",
                "company_code" => "123456",
                "pm_name" => "property manager2",
                "pm_email" => "<EMAIL>"
            ],
            [
                "company_name" => "company02",
                "company_code" => "123456",
                "pm_name" => "property manager3",
                "pm_email" => "<EMAIL>"
            ]
        ]
    ];
    SendRentEmailNotify($emailInfo);
}

// 发送 "即将过期的PM账号列表" 给 PM用户
function rent_customer_will_expire_to_pm()
{
    $emailInfo = [
        "remaining_days" => "12",
        "email_type" => "rent_customer_will_expire_to_pm",
        "email" => "<EMAIL>",
        "user" => "propetry manager",
        "language" => "zh-cn",
    ];

    SendRentEmailNotify($emailInfo);
}

// 发送 "已经过期的PM账号列表" 给 PM用户
function rent_customer_has_expired_to_pm()
{
    $emailInfo = [
        "remaining_days" => "12",
        "email_type" => "rent_customer_has_expired_to_pm",
        "email" => "<EMAIL>",
        "user" => "propetry manager",
        "language" => "zh-cn",
    ];

    SendRentEmailNotify($emailInfo);
}

// 发送 "已经过期的PM账号列表" 给 admin
function rent_customer_has_expired_to_admin()
{
    $emailInfo = [
        "remaining_days" => "12",
        "user" => "admin002 wjh",
        "email" => "<EMAIL>",
        "email_type" => "rent_customer_has_expired_to_admin",
        "language" => "zh-cn",
        "pm_list" => [
            [
                "company_name" => "company01",
                "company_code" => "123456",
                "pm_name" => "property manager1",
                "pm_email" => "<EMAIL>"
            ],
            [
                "company_name" => "company01",
                "company_code" => "123456",
                "pm_name" => "property manager2",
                "pm_email" => "<EMAIL>"
            ],
            [
                "company_name" => "company02",
                "company_code" => "123456",
                "pm_name" => "property manager3",
                "pm_email" => "<EMAIL>"
            ]
        ]
    ];
    SendRentEmailNotify($emailInfo);
}

// 发送 "已经付费的PM账号列表" 给 admin
function rent_customer_has_paid_to_admin()
{
    $emailInfo = [
        "remaining_days" => "12",
        "user" => "admin002 wjh",
        "email" => "<EMAIL>",
        "email_type" => "rent_customer_has_paid_to_admin",
        "language" => "zh-cn",
        "pm_list" => [
            [
                "company_name" => "company01",
                "company_code" => "123456",
                "pm_name" => "property manager1",
                "pm_email" => "<EMAIL>"
            ],
            [
                "company_name" => "company01",
                "company_code" => "123456",
                "pm_name" => "property manager2",
                "pm_email" => "<EMAIL>"
            ],
            [
                "company_name" => "company02",
                "company_code" => "123456",
                "pm_name" => "property manager3",
                "pm_email" => "<EMAIL>"
            ]
        ]
    ];
    SendRentEmailNotify($emailInfo);
}

rent_customer_will_expire_to_ins();
rent_customer_has_expired_to_ins();
rent_customer_will_expire_to_pm();
rent_customer_has_expired_to_pm();
rent_customer_has_expired_to_admin();
rent_customer_has_paid_to_admin();
