<?php
ini_set('date.timezone', 'Asia/Shanghai');
const KAFKA_IP = "*************:8520";
const KAFKA_EXPORTLOG_TOPIC = "exportlog";
const KAFKA_EXPORTLOG_CONSUMER = "exportlog_group";

const KAFKA_EXPORTLOG_EXCEL_TOPIC = "exportlog_excel";
const KAFKA_EXPORTLOG_EXCEL_CONSUMER = "exportlog_excel_group";

const KAFKA_PUSH_EMAIL_TOPIC="push_email";

const AKCS_MYSQL_DB_IP = "*******";
const AKCS_MYSQL_DB_PORT = "3308";
const LOG_MYSQL_DB_IP = "*******";
const LOG_MYSQL_DB_PORT = "3308";
const MYSQL_DB_PWD = "";

const SERVER_LOCATION = "na";
const JP_QIANYI_TIME = "2022-07-13 23:15:00";
const AU_QIANYI_TIME = "2023-12-14 23:15:00";
const IMAGELINKKEY = "ak_fdfs";
const FDFS_IP = "**********";
const FDFS_AWS_IP = "**********";
const PIC_URL_HEAD = "http://".FDFS_IP.":8090";
const PIC_URL_AWS_HEAD = "http://".FDFS_AWS_IP.":8090";
const LOG_FILE = "/var/log/php/restful.log";

const EXPORTLOG_ROOT_DIR = "/exportlog";
const OSS_FILE_EXPIRE_TIME = 604800; //7天。亚马逊oss最长存储7天
const DOWNLOAD_CSV_LIMIT = 20000; //导出excel最多2万条
const DOWNLOAD_PIC_LIMIT = 20000;
const PER_DIR_PIC_LIMIT = 5000;
const PER_DOWNLOAD_NUMBER_TO_SLEEP = 50;    //每下载50张需要等待1秒
const PER_DOWNLOAD_NUMBER_SLEEP_SEC = 1;    //每下载50张需要等待1秒
const EXPORTLOG_WAITING_TIME_ALARM = 1800;  //从网页点击到开始处理的超时告警
const EXPORTLOG_HANDLE_TIME_ALARM = 1800;   //处理超时
const EXPORTLOG_TYPE_ALL = 0;
const EXPORTLOG_TYPE_EXCEL_ONLY = 1;
const EXPORTLOG_TYPE_TRIGGER = 2;

//目录结构，数组的键值
const FILE_SAVE_DIR = "dir/trace_id/trace_id_index";
const FILE_CSV_PATH = "dir/trace_id/trace_id_index/csvname.csv";
const FILE_ZIP_PATH = "dir/trace_id/zipname.zip";
const FILE_ROOT = "dir/trace_id";
const FILE_ZIP_DIR = "trace_id_index";
const FILE_ZIP_NAME = "zipname";
const FILE_CSV_NAME = "csvname";

const TRIGGER_EMAIL = "<EMAIL>";

//对象存储类型
const STORAGE_TYPE_OSS = 0; //对象存储类型：阿里云
const STORAGE_TYPE_AWS = 1; //对象存储类型：亚马逊云
const STORAGE_TYPE_UKD = 2; //对象存储类型：优刻得

// 项目类型
const PROJECT_TYPE_RESIDENCE = 0;
const PROJECT_TYPE_OFFICE = 1;
const PROJECT_TYPE_PERSONAL = 2;
const PROJECT_TYPE_NEWOFFICE = 3;
