<?php
ini_set('date.timezone','Asia/Shanghai');
const GLOBAL_MACPOOL_IP = "*************:7443";
const GLOBAL_WEB_URL = "https://".GLOBAL_MACPOOL_IP;
require_once(dirname(__FILE__).'/define.php');


function getMACDB()
{
    $dbuser = "dbuser01";
    $dbpass = DB_PASSWORD;
    $dbhost="127.0.0.1";

    $mysql_conn_string = "mysql:host=$dbhost;port=3306;dbname=AKCS";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

const LOG_FILE = "/var/log/php/get_global_mac_pool.log";
function LOG_TRACE($content)
{
    $tmpNow = time();
    $Now = date('Y-m-d H:i:s', $tmpNow);
    //当文件不存在时候，返回错误信息 会影响ios解析数据
	@file_put_contents(LOG_FILE, $Now." ".$content, FILE_APPEND);
	@file_put_contents(LOG_FILE, "\n", FILE_APPEND);
}

function httpRequest($method, $url, $headers, $data = '', $is_json = 0)
{
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($curl, CURLOPT_HEADER, false);
    curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 30);
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
	curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
    if($method == 'post')
    {
        curl_setopt($curl, CURLOPT_POST, true);
        if($is_json)
        {
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }
        else
        {
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
        }
    }

	LOG_TRACE("Get:".$url);
    $output = curl_exec($curl);
	LOG_TRACE("Return:".$output);
    if($output == false)
    {
        printf("Error: ".curl_error($curl));
    }
    curl_close($curl);
    return $output;
    
}

function insertAKCSMACs($db, $mac, $authcode)
{
    try {	
		$sth = $db->prepare("insert into MacPool (Mac, Authcode) values(:mac, :authcode)");
		$sth->bindParam(':mac', $mac, PDO::PARAM_STR);
		$sth->bindParam(':authcode', $authcode, PDO::PARAM_STR);
		$sth->execute();
	}
	catch(PDOException $e)
	{	
		if (strstr($e->getMessage(), "Duplicate entry"))
		{
			//发送告警
			LOG_TRACE("insert mac $mac error:".$e->getMessage());
		}
		else
		{
			LOG_TRACE("insert mac $mac unknown error:".$e->getMessage());
			throw new Exception($e->getMessage());
			
		}
	}
}

function Getmacs()
{
    $db = getMACDB();
    try
    {
        $sth_id = $db->prepare("SELECT MacPoolLastID FROM SystemSetting LIMIT 1");
        $sth_id->execute();
        $result_last_id = $sth_id->fetch(PDO::FETCH_ASSOC);
        $last_id = $result_last_id['MacPoolLastID'];
        $headers = array();
        do
        {
            $output = httpRequest('get', GLOBAL_WEB_URL.'/akcs_get_macs?last_id='.$last_id, $headers, 0);
            $outputArr = json_decode($output, true);
            
			if ($outputArr && $outputArr['result'] == 0)
			{
				$db->beginTransaction();
                $datas = $outputArr['datas'];
                $gmax_id = intval($datas['max_id']);
                $gcur_id = intval($datas['cur_id']);
                $gmacs = $datas['macs'];
                //插入macs
                foreach ($gmacs as $macs)
                {
                    if (null == $macs['Mac'] || null == $macs['Authcode']) 
                    {
						//告警
                        LOG_TRACE("Invalid param");
                    }
					else
					{
						insertAKCSMACs($db, $macs['Mac'], $macs['Authcode']);
					}
                }
    
                //更新last_id
                $last_id = $gcur_id;
                $sth = $db->prepare("update SystemSetting set MacPoolLastID = :last_id where 1=1");
                $sth->bindParam(':last_id', $last_id, PDO::PARAM_INT);
                $sth->execute();
				$db->commit();
            }
            else if ($outputArr['result'] == -1)
            {
                echo "not in whiteips\n";
                $content = file_get_contents('/etc/ip');
                $cmd = "echo \"mac pool 更新not in whiteips. cscron迁移  $content\" | mutt -s \"mac pool 更新not in whiteips. cscron迁移\"  -b <EMAIL> -c <EMAIL>";
                shell_exec($cmd);
                return 0;
            }
            else if ($outputArr['result'] == 1)
            {
                $gmax_id = $last_id;
                echo "last_id is max_id\n";
            }
			
        }
        while ($last_id < $gmax_id);
    }
    catch(PDOException $e)
    {
        LOG_TRACE("GETMAC db error:" . $e->getMessage());    
        return 0;
    }
    return 1;
    
}

Getmacs();


?>
