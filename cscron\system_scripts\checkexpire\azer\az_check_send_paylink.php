<?php

require_once(dirname(__FILE__).'/az_check_send_paylink_common.php');

$medooDb = getMedooDb();
$db = $medooDb->pdo;

$server_tag = GetServerTag();

//社区公维金过期检查
function CheckCommunalFeeExpire($before)
{
    $room_list = GetAutoSendRoomList($before);
    foreach($room_list as $room_info)
    {
        //跟随社区公维金设置
        if($room_info['IsDefaultCommunalFee'] == 1)
        {
            $communal_fee = $room_info['CommFee'];
        }
        //跟随房间公维金设置
        else
        {
            $communal_fee = $room_info['CommunalFee'];
        }

        $master_account = GetCommMasterAccountByUUID($room_info['PersonalAccountUUID']);
        $expire_time = $room_info['ExpireTime'];
        $effect_year = GetYear($expire_time);
        $effect_month = GetMonth($expire_time);
        $effect_day = GetDay($expire_time);
        $user_language = $master_account['Language'];

        $order_uuid = GetAutoSendOrderUUID($room_info, $master_account, $communal_fee);
        $pay_link = GetPayLink($order_uuid);

        //发送Message
        $message_info = array();
        $message_info['effect_year'] = $effect_year;
        $message_info['effect_month'] = $effect_month;
        $message_info['effect_day'] = $effect_day;
        $message_info['pay_link'] = $pay_link;
        $message_info['account'] = $master_account['Account'];
        $message_info['language'] = $user_language;
        SendAzMessageNotify($message_info);

        $email = GetEmailByUUID($master_account['UserInfoUUID']);
        //主账号有填邮箱才发送
        if(!$email)
        {
            continue;
        }
        //邮件相关信息
        $email_info = array();
        //订单相关信息
        $email_info['pay_link'] = $pay_link;
        $email_info['effect_year'] = $effect_year;
        $email_info['effect_month'] = $effect_month;
        $email_info['effect_day'] = $effect_day;
        //amount需要保留两位小数
        $email_info['amount'] = strval(round($communal_fee * $room_info['Area'] / 100) / 100); //amount = fee * 100 * area * 100 / 10000 
        //邮件相关信息
        $email_info['email'] = $email;
        $email_info['language'] = $user_language;
        $email_info['project_type'] = PROJCET_RESIDENCE;
        $email_info['email_type'] = "auto_send_bill";
        SendAzEmailNotify($email_info);
    }
}
