<?php
require_once __DIR__ . '/common/aliyun.phar';
require_once __DIR__ . '/common/aws.phar';
require_once(dirname(__FILE__).'/akcs_log.php');


use Aws\S3\S3Client;
use Aws\Exception\AwsException;
use OSS\OssClient;
use OSS\Core\OssException;

class S3HandleRedirect
{
    private $cfg;
    private static $instance;
    public function __construct()
    {
        $this->cfg = $this->InitS3Cfg();
    }
    private function __clone()
    {
    }
    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    private function InitS3Cfg()
    {
        $cfg = parse_ini_file("/usr/local/akcs/csexportlog/GatewayWorker/oss_scloud.conf");
        if ($cfg['IS_AWS']) {
            try {
                $credentials = new Aws\Credentials\Credentials($cfg['User'], $cfg['Password']);
                $s3 = new Aws\S3\S3Client([
                    'version'     => 'latest',
                    'region'      => $cfg['RegionID'],
                    'credentials' => $credentials
                ]);
                $cfg["s3"] = $s3;
            } catch (Exception $exception) {
                LOG_ERROR("Init S3 Client Error:" . $exception->getMessage());
            }
        } else {
            try {
                $oss = new OssClient($cfg['User'], $cfg['Password'], $cfg['Endpoint']);
                $cfg["oss"] = $oss;
            } catch (OssException $exception) {
                LOG_ERROR("Init OSS Client Error:" . $exception->getMessage());
            }
        }
        return $cfg;
    }
    public function DownloadFile($filepath, $localfile)
    {
        if (strlen($filepath) == 0) {
            return;
        }
        $cfg = $this->cfg;
        if ($cfg['IS_AWS']) {
            try {
                $file = $cfg['s3']->getObject([
                    'Bucket' => $cfg['BucketForPic'],
                    'Key' => $filepath
                ]);
                $body = $file->get('Body');
                file_put_contents($localfile, $body->getContents());
            } catch (Exception $exception) {
                LOG_ERROR("Failed to download $filepath  with error: " . $exception->getMessage());
            }
        } else {
            $options = array(
                OssClient::OSS_FILE_DOWNLOAD => $localfile
            );
            try {
                $cfg['oss']->getObject($cfg['BucketForPic'], $filepath, $options);
            } catch (OssException $exception) {
                LOG_ERROR("Failed to download $filepath  with error: " . $exception->getMessage());
            }
        }
    }
    public function UploadFile($remote_path, $localfile)
    {
        $cfg = $this->cfg;
        if ($cfg['IS_AWS']) {
            try {
                $cfg['s3']->putObject([
                    'Bucket' => $cfg['BucketForLog'],
                    'Key' => $remote_path,
                    'SourceFile' => $localfile,
                ]);
                $cmd = $cfg['s3']->getCommand('GetObject', [
                    'Bucket' => $cfg['BucketForLog'],
                    'Key' => $remote_path
                ]);
                $request = $cfg['s3']->createPresignedRequest($cmd, '+604800 second');
                $presignedUrl = (string)$request->getUri();
                return $presignedUrl;
            } catch (Exception $exception) {
                LOG_ERROR("Failed to Upload S3 File $localfile  with error: " . $exception->getMessage());
                return "";
            }
        } else {
            try {
                $cfg['oss']->uploadFile($cfg['BucketForLog'], $remote_path, $localfile);
                $signedUrl = $cfg['oss']->signUrl($cfg['BucketForLog'], $remote_path, 604800, "GET", "");
                return $signedUrl;
            } catch (OssException $exception) {
                LOG_ERROR("Failed to Upload OSS File $localfile  with error: " . $exception->getMessage());
                return "";
            }
        }
    }    
}