<?php

require_once(__DIR__ . '/akcs_log.php');
require_once(__DIR__ . '/akcs_monitor.php');
require_once(__DIR__ . '/common/define.php');
require_once(__DIR__ . '/web/call.php');
require_once(__DIR__ . '/web/doorlog.php');
require_once(__DIR__ . '/web/capture.php');
require_once(__DIR__ . '/web/trigger.php');
require_once(__DIR__ . '/web/temperature.php');
require_once(__DIR__ . '/web/newoffice/export_call_log.php');
require_once(__DIR__ . '/web/newoffice/export_doorlog.php');

const EXPORT_LOG_TRIGGER = '0';
const EXPORT_LOG_DOORLOG = '1';
const EXPORT_LOG_CAPTURE = '2';
const EXPORT_LOG_TEMPERATURE = '3';
const EXPORT_LOG_CALLHISTORY = '4';

function ExportLog($data, $s3_cfg)
{
    // 新办公使用统一的协议, key是data, 值是array()
    if (is_array($data) && isset($data["data"]) && is_array($data["data"])) {
        $data += $data["data"];
    }

    // 旧办公 key是datas, 值是 json字符串
    if (is_array($data) && isset($data["datas"]) && is_string($data["datas"])) {
        $data += json_decode($data["datas"], true);
    }

    $result = array();;
    if (array_key_exists("project_type", $data) && PROJECT_TYPE_NEWOFFICE == $data["project_type"]) {
        $result = NewOfficeExportLog($data, $s3_cfg);
    } else {
        $result = ExportLogV68($data, $s3_cfg);
    }

    LOG_INFO("ExportLog end, result=" . json_encode($result));
    return $result;
}

function ExportLogV68($datas, $s3_cfg)
{
    // 链路监控
    if (isset($datas['log_type']) && $datas['log_type'] == EXPORT_LOG_TRIGGER) {
        return triggerExportlogChain($datas['msg'], $s3_cfg);
    }
    
    $retinfo =array();
    $trace_id = $datas["trace_id"];
    $log_type = $datas["log_type"];
    $language = $datas["language"];
    $web_sendtime = $datas["timestamp"];
    $communit_id = $datas["communit_id"];
    $export_type = $datas["export_type"];
    $project_type = $datas["project_type"];
    $export_endtime = $datas["export_endtime"];
    $export_startime = $datas["export_startime"];
    LOG_INFO("ExportLogV68 start, web request time=$web_sendtime, trace_id=$trace_id");

    $handle_starttime = time();
    if ($handle_starttime - $web_sendtime > EXPORTLOG_WAITING_TIME_ALARM) {
        $wait_time = $handle_starttime - $web_sendtime;
        AddMonitorHandleWaitingTooLong("ExportLogV68 waiting too long, configure timeout is " . EXPORTLOG_WAITING_TIME_ALARM . " waittime=$wait_time");
    }
    $partition = $datas['kafka_partition'];
    $offset = $datas['kafka_offset'];

    ob_start();
    var_dump($datas);
    $result = ob_get_clean();
    LOG_INFO($result);
    LOG_INFO("ExportLogV68: partition=$partition, offset=$offset, trace_id=$trace_id, communit_id=$communit_id, export_type=$export_type, export_startime=$export_startime, export_endtime=$export_endtime, log_type=$log_type");

    //1=DoorLog 2=Capture 3=TemperatureCapture 4=callhistory
    switch ($log_type) {
        case EXPORT_LOG_DOORLOG:
            $retinfo = ExportLogDoorLog($datas, $s3_cfg);
            break;
        case EXPORT_LOG_CAPTURE:
            $retinfo = ExportLogCapture($datas, $s3_cfg);
            break;
        case EXPORT_LOG_TEMPERATURE:
            $retinfo = ExportLogTemperature($datas, $s3_cfg);
            break;
        case EXPORT_LOG_CALLHISTORY:
            $retinfo = ExportLogCallHistory($datas, $s3_cfg);
            break;
        default:
            LOG_ERROR("ExportLogV68: trace_id=$trace_id, communit_id=$communit_id,log_type=$log_type error log type!");
            $retinfo["result"] = -1;
            break;
    }

    $handle_endtime = time();
    $use_time = $handle_endtime - $handle_starttime;
    if ($use_time > EXPORTLOG_HANDLE_TIME_ALARM) {
        AddMonitorHandleTooLong("ExportLogV68 handle too long, configure timeout is " . EXPORTLOG_HANDLE_TIME_ALARM . " handle time $use_time");
    }
    
    LOG_INFO("ExportLogV68 end, use time:$use_time, trace_id=$trace_id");
    $ret = array_merge($retinfo, $datas);
    return $ret;
}

function NewOfficeExportLog($datas, $s3_cfg)
{
    // 链路监控
    if (isset($datas['log_type']) && $datas['log_type'] == EXPORT_LOG_TRIGGER) {
        return triggerExportlogChain($datas['msg'], $s3_cfg);
    }

    $retinfo = array();
    $record_uuid = $datas["record_uuid"];
    $operator_type = $datas["operator_type"];
    if ($operator_type == OPERATOR_TYPE_ADMIN) {
        $record = getAdminExportLogRecordByUUID($record_uuid);
        $datas["company_uuid"] = $record["OfficeCompanyUUID"];
    } else if ($operator_type == OPERATOR_TYPE_PM) {
        $record = getPmExportLogRecordByUUID($record_uuid);
        $datas["account_uuid"] = $record["AccountUUID"];
    } else {
        LOG_ERROR("Unsupported operator_type: $operator_type.");
        $retinfo = array("result" => -1);
        return $retinfo;
    }

    $trace_id = $datas["trace_id"];
    $log_type = $datas["log_type"];
    $language = $datas["language"];
    $web_sendtime = $datas["timestamp"] / 1000000;   // 新办公中用微秒，转为秒
    $export_type = $datas["export_type"];
    $project_type = $datas["project_type"];
    $export_endtime = $datas["export_endtime"];
    $export_startime = $datas["export_startime"];
    LOG_INFO("NewOfficeExportLog start, web request time=$web_sendtime, trace_id=$trace_id");

    $handle_starttime = time();
    if ($handle_starttime - $web_sendtime > EXPORTLOG_WAITING_TIME_ALARM) {
        $wait_time = $handle_starttime - $web_sendtime;
        AddMonitorHandleWaitingTooLong(
            "NewOfficeExportLog waiting too long, configure timeout is " .
            EXPORTLOG_WAITING_TIME_ALARM . ", waittime=$wait_time, trace_id=$trace_id"
        );
    }
    $partition = $datas['kafka_partition'];
    $offset = $datas['kafka_offset'];

    ob_start();
    var_dump($datas);
    $result = ob_get_clean();
    LOG_INFO($result);
    LOG_INFO("NewOfficeExportLog: partition=$partition offset=$offset trace_id=$trace_id, export_type=$export_type, record_uuid=$record_uuid, export_startime=$export_startime, export_endtime=$export_endtime, log_type=$log_type, operator_type=$operator_type");

    switch ($log_type) {
        case EXPORT_LOG_DOORLOG:
            $retinfo = NewofficeExportDoorLog($datas, $s3_cfg);
            break;
        case EXPORT_LOG_CALLHISTORY:
            $datas["communit_id"] = $datas["account_uuid"];
            $retinfo = NewofficeExportCallLog($datas, $s3_cfg);
            break;
        default:
            LOG_ERROR("NewOfficeExportLog: trace_id=$trace_id, log_type=$log_type  error log type!");
            $retinfo = array("result" => -1);
            break;
    }

    $handle_endtime = time();
    $use_time = $handle_endtime - $handle_starttime;
    if ($use_time > EXPORTLOG_HANDLE_TIME_ALARM) {
        AddMonitorHandleTooLong("NewOfficeExportLog handle too long, configure timeout is " .
            EXPORTLOG_HANDLE_TIME_ALARM . " handle time $use_time, trace_id=$trace_id"
        );
    }

    LOG_INFO("NewOfficeExportLog end, use time:$use_time, trace_id=$trace_id");
    $ret = array_merge($retinfo, $datas);
    return $ret;
}
