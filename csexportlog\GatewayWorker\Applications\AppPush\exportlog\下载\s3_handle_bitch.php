<?php

require_once __DIR__ . '/aliyun.phar';
require_once __DIR__ . '/aws.phar';

use Aws\S3\S3Client;
use Aws\Exception\AwsException;
use OSS\OssClient;
use OSS\Core\OssException;


const HandBucket='BucketForLog';

class S3Handle
{
    private $cfg;
    public function __construct()
    {
        $this->cfg = $this->InitS3Cfg();
    }
    private function InitS3Cfg()
    {
        $cfg = parse_ini_file("/etc/oss_install.conf");
        if ($cfg['IS_AWS']) {
            try {
                $credentials = new Aws\Credentials\Credentials($cfg['User'], $cfg['Password']);
                $s3 = new Aws\S3\S3Client([
                    'version'     => 'latest',
                    'region'      => $cfg['RegionID'],
                    'credentials' => $credentials
                ]);
                $cfg["s3"] = $s3;
            } catch (Exception $exception) {
                LOG_ERROR("Init S3 Client Error:" . $exception->getMessage());
            }
        } else {
            try {
                $oss = new OssClient($cfg['User'], $cfg['Password'], $cfg['Endpoint']);
                $cfg["oss"] = $oss;
            } catch (OssException $exception) {
                LOG_ERROR("Init OSS Client Error:" . $exception->getMessage());
            }
        }
        return $cfg;
    }
    public function DownloadFile($filepath, $localfile)
    {
        if (strlen($filepath) == 0) {
            return;
        }
        $cfg = $this->cfg;
        if ($cfg['IS_AWS']) {
            try {
                $file = $cfg['s3']->getObject([
                    'Bucket' => $cfg[HandBucket],
                    'Key' => $filepath
                ]);
                $body = $file->get('Body');
                file_put_contents($localfile, $body->getContents());
            } catch (Exception $exception) {
                LOG_ERROR("Failed to download $filepath  with error: " . $exception->getMessage());
            }
        } else {
            $options = array(
                OssClient::OSS_FILE_DOWNLOAD => $localfile
            );
            try {
                $cfg['oss']->getObject($cfg[HandBucket], $filepath, $options);
            } catch (OssException $exception) {
                LOG_ERROR("Failed to download $filepath  with error: " . $exception->getMessage());
            }
        }
    }
    
    public function GetDirFileList($directory)
    {
        if (strlen($directory) == 0) {
            return;
        }
        $cfg = $this->cfg;
        if ($cfg['IS_AWS']) {
            try {
                // 使用 listObjectsV2 方法列出指定存储桶和目录中的文件
                $objects = $cfg['s3']->listObjects([
                    'Bucket' => $cfg[HandBucket],
                    'Prefix' => $directory
                ]);

                // 循环遍历对象并输出文件名
                $fils = array();
                foreach ($objects['Contents'] as $object) {
                    array_push($fils, $object['Key']);
                }
                return $fils;
            } catch (AwsException $e) {
                echo $e->getMessage();
            }
            
        } else {
            $options = array(
                OssClient::OSS_FILE_DOWNLOAD => $localfile
            );
            try {
                $cfg['oss']->getObject($cfg[HandBucket], $filepath, $options);
            } catch (OssException $exception) {
                LOG_ERROR("Failed to download $filepath  with error: " . $exception->getMessage());
            }
        }
    }    
    

    
    public function UploadFile($remote_path, $localfile)
    {
        $cfg = $this->cfg;
        if ($cfg['IS_AWS']) {
            try {
                $cfg['s3']->putObject([
                    'Bucket' => $cfg['BucketForLog'],
                    'Key' => $remote_path,
                    'SourceFile' => $localfile,
                ]);
                $cmd = $cfg['s3']->getCommand('GetObject', [
                    'Bucket' => $cfg['BucketForLog'],
                    'Key' => $remote_path
                ]);
                $request = $cfg['s3']->createPresignedRequest($cmd, '+604800 second');
                $presignedUrl = (string)$request->getUri();
                return $presignedUrl;
            } catch (Exception $exception) {
                LOG_ERROR("Failed to Upload S3 File $localfile  with error: " . $exception->getMessage());
                return "";
            }
        } else {
            try {
                $cfg['oss']->uploadFile($cfg['BucketForLog'], $remote_path, $localfile);
                $signedUrl = $cfg['oss']->signUrl($cfg['BucketForLog'], $remote_path, 604800, "GET", "");
                return $signedUrl;
            } catch (OssException $exception) {
                LOG_ERROR("Failed to Upload OSS File $localfile  with error: " . $exception->getMessage());
                return "";
            }
        }
    }
}


$servers = ["ucloud_app1-52.52.9.50","ucloud_app2-184.169.232.190","ucloud_app3-54.153.114.69","ucloud_app4-54.241.249.251","ucloud_app8-54.176.68.190"];

$s3 = new S3Handle();

foreach($servers as $ser) 
{
    $time = "2023-11-01";
    $savedir = "$time/$ser"; #下载到本地目录
    
    $dir = "csmain/$ser/2023-11/$time/";
    $files = $s3->GetDirFileList($dir);

    shell_exec("mkdir -p $savedir");#创建本地目录
    foreach($files as $file)
    {
        $fileName = basename($file);    
        $s3->DownloadFile($file, $savedir."/".$fileName);
    }    
}










