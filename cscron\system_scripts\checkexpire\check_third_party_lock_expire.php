<?php
/* 三方锁功能过期检测 */
require_once(dirname(__FILE__) . '/email_type.php');
require_once(dirname(__FILE__) . '/check_expire_common_v4500.php');
require_once(dirname(__FILE__) . '/../common/email_notify_rule.php');
require_once(dirname(__FILE__) . '/check_third_party_lock_common.php');
LOG_INFO("Check Third Party Lock Expire begin");

const SUBSCRIPTIONENDUSERLIST_SINGLE_THIRD_PARTY_LOCK_TYPE = 7;
const SUBSCRIPTIONENDUSERLIST_COMMUNITY_THIRD_PARTY_LOCK_TYPE = 8;

const ACCOUNT_GRADE_SUBDIS = 12;
const DORMAKABA_LOCK = 3;
const ITEC_LOCK = 6;

//2025.1.17 当前锁版本收费仅考虑3=Dormakaba,6=Itec，TTlock不收费
class LockBrands
{
    public static function getChargeableLockBrands()
    {
        return [
            DORMAKABA_LOCK,
            ITEC_LOCK,
        ];
    }
}

// 社区项目，查找 $remainDays 天后要过期的三方锁数据，remainDays<0 表示过期 remainDays 天
function getCommunityExpireList($remainDays)
{
    global $db;

    // 获取收费的锁品牌数组
    $lockBrands = LockBrands::getChargeableLockBrands();

    // 将锁品牌数组转换为字符串形式（例如：'3,6'）
    $brandList = implode(',', $lockBrands);

    $sql = "SELECT A.ID AS ProjectID, 
                   A.UUID AS ProjectUUID, 
                   A.ParentUUID AS DistributorUUID, 
                   A.Location AS CommunityName, 
                   A.Language, 
                   T.InstallerUUID  AS InstallerUUID,
                   T.PersonalAccountUUID  AS PersonalAccountUUID,
                   T.CommunityUnitUUID  AS CommunityUnitUUID,
                   T.Brand AS LockBrand,
                   T.LockUUID AS LockUUID,
                   S.DistributorUUID AS SubDistributorUUID,
                   T.ProjectType
            FROM ThirdLockRelateInfo T 
            INNER JOIN Account A ON A.UUID = T.AccountUUID 
            LEFT JOIN SubDisMngList S ON S.InstallerUUID = T.InstallerUUID  
            WHERE T.Active = 1 
            AND T.ExpireTime > CURDATE() + INTERVAL :remainDays DAY
            AND T.ExpireTime <= CURDATE() + INTERVAL (:remainDays + 1) DAY
            AND T.ProjectType = :projectType 
            AND T.Brand IN ($brandList)
            AND T.LockUUID IS NOT NULL
            ORDER BY A.ParentUUID, T.InstallerUUID, A.ID, T.PersonalAccountUUID;";

    $sth = $db->prepare($sql);

    $sth->bindValue(':remainDays', $remainDays, PDO::PARAM_INT);
    $sth->bindValue(':projectType', THIRD_PARTY_LOCK_PROJECT_RESIDENCE, PDO::PARAM_INT);

    $sth->execute();
    $expireList = $sth->fetchAll(PDO::FETCH_ASSOC);

    // 剔除开启自动扣费的用户
    $expireList = getThirdPartyExpireLockListWithoutAutoPay($expireList, SUBSCRIPTIONENDUSERLIST_COMMUNITY_THIRD_PARTY_LOCK_TYPE);
    LOG_INFO("get Community third party lock ExpireList ,ExpireList num:" . count($expireList) . ", remainDays:$remainDays");

    return $expireList;
}

// 单住户项目，查找$remainDays天后要过期的数据，remainDays<0表示过期remainDays天
//2025.1.17 当前锁版本收费仅考虑3=Dormakaba,6=Itec，TTlock不收费，下面对锁的品牌进行过滤
function getPersonalExpireList($remainDays)
{
    global $db;
    // 获取锁品牌数组
    $lockBrands = LockBrands::getChargeableLockBrands();

    // 将锁品牌数组转换为字符串形式（例如：'3,6'）
    $brandList = implode(',', $lockBrands);

    $sql = "SELECT A.ID AS InstallerID, 
                   A.UUID AS InstallerUUID, 
                   A.ParentUUID AS DistributorUUID, 
                   A.Language, 
                   T.Brand AS LockBrand,
                   T.LockUUID AS LockUUID,
                   S.DistributorUUID AS SubDistributorUUID,
                   T.ProjectType,
                   T.PersonalAccountUUID  AS PersonalAccountUUID
            FROM ThirdLockRelateInfo T 
            INNER JOIN Account A ON A.UUID = T.AccountUUID 
            LEFT JOIN SubDisMngList S ON S.InstallerUUID = A.UUID  
            WHERE T.Active = 1 
            AND T.ExpireTime > CURDATE() + INTERVAL :remainDays DAY
            AND T.ExpireTime <= CURDATE() + INTERVAL (:remainDays + 1) DAY
            AND T.ProjectType = :projectType 
            AND T.Brand IN ($brandList)
            AND T.LockUUID IS NOT NULL
            ORDER BY A.ParentUUID, A.ID, T.PersonalAccountUUID;";

    $sth = $db->prepare($sql);
    $sth->bindValue(':remainDays', $remainDays, PDO::PARAM_INT);
    $sth->bindValue(':projectType', THIRD_PARTY_LOCK_PROJECT_PERSONAL, PDO::PARAM_INT);

    $sth->execute();

    $expireList = $sth->fetchAll(PDO::FETCH_ASSOC);

    // 剔除开启自动扣费的用户
    $expireList = getThirdPartyExpireLockListWithoutAutoPay($expireList, SUBSCRIPTIONENDUSERLIST_SINGLE_THIRD_PARTY_LOCK_TYPE);

    LOG_INFO("get Personal third party lock ExpireList ,ExpireList num:" . count($expireList) . ", remainDays:$remainDays");

    return $expireList;
}

// 剔除开启自动扣费的用户
function getThirdPartyExpireLockListWithoutAutoPay($expireList, $autopayType)
{
    $expireEndUserList = array();
    foreach ($expireList as $lockInfo) {
        array_push($expireEndUserList, $lockInfo);
        if (!isUserEnableThirdPartyLockAutoPay($lockInfo['LockUUID'], $autopayType)) {
        }
    }
    
    return $expireEndUserList;
}

// 三方锁功能在remainDays天后过期
function checkThirdPartyLockExpire($remainDays)
{
    // 处理社区锁
    $communityExpireList = getCommunityExpireList($remainDays);
    $communityFilteredList = array();

    foreach ($communityExpireList as &$item) {
        if (!checkAppIsMigrateDisByUUID($item['InstallerUUID'])) {
            buildLockList($item);
            array_push($communityFilteredList, $item);
        }
    }
    sendEmail(THIRD_PARTY_LOCK_PROJECT_RESIDENCE, $communityFilteredList, $remainDays);

    // 处理单住户锁
    $personalExpireList = getPersonalExpireList($remainDays);
    $personalFilteredList = array();

    foreach ($personalExpireList as &$item) {
        if (!checkAppIsMigrateDisByUUID($item['InstallerUUID'])) {
            buildLockList($item);
            array_push($personalFilteredList, $item);
        }
    }
    sendEmail(THIRD_PARTY_LOCK_PROJECT_PERSONAL, $personalFilteredList, $remainDays);
    return;
}

// 实现邮件发送功能
function sendEmail($projectType, $expireList, $remainDays)
{
    if (empty($expireList)) {
        return;
    }
    if ($remainDays < 0) {
        if ($projectType == THIRD_PARTY_LOCK_PROJECT_PERSONAL) {
            //personalExpiredSendEmailToIns(EMAIL_TYPE_PERSONAL_THIRD_PARTY_LOCK_HAS_EXPIRED, $expireList, $remainDays);
            personalExpiredSendEmailToEnduser(EMAIL_TYPE_PERSONAL_THIRD_PARTY_LOCK_HAS_EXPIRED_TO_ENDUSER, $expireList, $remainDays);
        } elseif ($projectType == THIRD_PARTY_LOCK_PROJECT_RESIDENCE) {
            //communityExpiredSendEmailToIns(EMAIL_TYPE_COMMUNITY_THIRD_PARTY_LOCK_HSA_EXPIRED, $expireList, $remainDays);
            //communityExpiredSendEmailToPM(EMAIL_TYPE_COMMUNITY_THIRD_PARTY_LOCK_HSA_EXPIRED, $expireList, $remainDays);
            communityExpireSendEmailToEndUser(EMAIL_TYPE_COMMUNITY_THIRD_PARTY_LOCK_HSA_EXPIRED_TO_ENDUSER, $expireList, $remainDays);
        }
    } else {
        if ($projectType == THIRD_PARTY_LOCK_PROJECT_PERSONAL) {
            //personalWillExpireSendEmailToDis(EMAIL_TYPE_PERSONAL_THIRD_PARTY_LOCK_WILL_EXPIRE, $expireList, $remainDays);
            //personalWillExpireSendEmailToSubDis(EMAIL_TYPE_PERSONAL_THIRD_PARTY_LOCK_WILL_EXPIRE, $expireList, $remainDays);
            //personalWillExpireSendEmailToIns(EMAIL_TYPE_PERSONAL_THIRD_PARTY_LOCK_WILL_EXPIRE, $expireList, $remainDays);
            personalWillExpireSendEmailToEnduser(EMAIL_TYPE_PERSONAL_THIRD_PARTY_LOCK_WILL_EXPIRE_TO_ENDUSER, $expireList, $remainDays);
        } elseif ($projectType == THIRD_PARTY_LOCK_PROJECT_RESIDENCE) {
            //communityWillExpireSendEmailToDis(EMAIL_TYPE_COMMUNITY_THIRD_PARTY_LOCK_WILL_EXPIRE, $expireList, $remainDays);
            //communityWillExpireSendEmailToSubDis(EMAIL_TYPE_COMMUNITY_THIRD_PARTY_LOCK_WILL_EXPIRE, $expireList, $remainDays);
            //communityWillExpireSendEmailToIns(EMAIL_TYPE_COMMUNITY_THIRD_PARTY_LOCK_WILL_EXPIRE, $expireList, $remainDays);
            //communityWillExpireSendEmailToPM(EMAIL_TYPE_COMMUNITY_THIRD_PARTY_LOCK_WILL_EXPIRE, $expireList, $remainDays);
            communityWillExpireSendEmailToEndUser(EMAIL_TYPE_COMMUNITY_THIRD_PARTY_LOCK_WILL_EXPIRE_TO_ENDUSER, $expireList, $remainDays);
        }
    }
    return;
}

/************************* 单住户发送通知邮件 ***************************/

// 单住户 三方锁过期发送邮件通知Ins
function personalExpiredSendEmailToIns($emailType, $expireList, $remainDays)
{
    personalWillExpireSendEmailToIns($emailType, $expireList, $remainDays);
}

// 单住户 三方锁过期发送邮件通知Enduser
function personalExpiredSendEmailToEnduser($emailType, $expireList, $remainDays)
{
    personalWillExpireSendEmailToEnduser($emailType, $expireList, $remainDays);
}

// 单住户 三方锁即将过期发送邮件通知Dis
function personalWillExpireSendEmailToDis($emailType, $expireList, $remainDays)
{
    global $db;
    $lockList = array();
    $preInstallerUUID = "";
    $preDistributorUUID = "";
    array_push($expireList, array('DistributorUUID' => "null"));  // 末尾插入空行，便于处理最后一条数据
    foreach ($expireList as $item) {
        if (empty($item)) {
            continue;
        }
         // 当前记录Dis和上一条记录Dis不一样，说明是不同Dis。发送上一个Dis的邮件
        $installerUUID = empty($item['InstallerUUID']) ? "" : $item['InstallerUUID'];
        $distributorUUID = empty($item['DistributorUUID']) ? "" : $item['DistributorUUID'];
        if ($preDistributorUUID != $distributorUUID && count($lockList) > 0) {
            // 获取经销商账户信息
            $disAccountInfo = getAccountInfoByAccountUUID($db, $preDistributorUUID); 
            $disBillingInfo = getInstallerEmailInfoByAccountUUID($preDistributorUUID);
            if (!empty($disBillingInfo['Email'])) {
                $chargeMode = getPersonalChargeModeByInsUUID($preInstallerUUID);
                if ($chargeMode && PersonalCheck::needNotifyDis(EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT, $chargeMode, $remainDays)) {
                    $emailInfo = array();
                    $emailInfo['web_ip'] = WEB_DOMAIN;
                    $emailInfo["email_type"] = $emailType;
                    $emailInfo['remaining_days'] = strval($remainDays);
                    $emailInfo["email"] = $disBillingInfo['Email'];
                    $emailInfo['user'] = $disAccountInfo['Account'];     // 邮件的显示名称
                    $emailInfo["account"] = $disAccountInfo['Account'];  // 登录的账号名称（获取付费链接时使用）
                    $emailInfo["installer_uuid"] = $preInstallerUUID;    //获取付费链接时使用
                    $emailInfo['language'] = $disAccountInfo["Language"];
                    $emailInfo['table_list'] = $lockList;
                    $emailInfo["expire_lock_num"] = count($lockList);
                    $emailInfo["is_show_paylink"] = SHOW_PAYLINK_DISTRIBUTOR;
                    //判断是否需要显示付费链接
                    $account_info = getAccountInfo($preDistributorUUID);
                    if($account_info && $account_info['Grade'] == ACCOUNT_GRADE_SUBDIS){
                        if(!PersonalCheck::ifSubDisHasPayPermission($account_info)){
                            $email_info['is_show_paylink'] = SHOW_PAYLINK_NONE;
                        }
                    }
                    sendEmailNotify($emailInfo);
                }
            }
            $lockList = array();
        }
        // 插入当前用户数据
        $preInstallerUUID = $installerUUID;
        $preDistributorUUID = $distributorUUID;
        $lockData = buildPerLockData($item);
        array_push($lockList, $lockData);
    }
}

function PersonalWillExpireSendEmailToSubDis($emailType, $expireList, $remainDays)
{
    foreach ($expireList as $row => $item) {
        $expireList[$row]['DistributorUUID'] = $item['SubDistributorUUID'];
    }
    personalWillExpireSendEmailToDis($emailType, $expireList, $remainDays);
   
}

function personalWillExpireSendEmailToIns($emailType, $expireList, $remainDays)
{
    global $db;
    $lockList = array();
    $preInstallerUUID = "";
    $preInstallerID = "";
    array_push($expireList, array('InstallerUUID' => "null"));  // 末尾插入空行，便于处理最后一条数据
    foreach ($expireList as $item) {
        if (empty($item)) {
            continue;
        }
        // 当前记录Ins和上一条记录Ins不一样，说明是不同Ins。发送上一个Ins的邮件
        $installerUUID = empty($item['InstallerUUID']) ? "" : $item['InstallerUUID'];
        $installerID = empty($item['InstallerID']) ? "" : $item['InstallerID'];
        if ($preInstallerUUID != $installerUUID && count($lockList) > 0) {
            $insAccountInfo = getAccountInfoByAccountUUID($db, $preInstallerUUID);
            $insBillingInfo = getInstallerEmailInfoByAccountUUID($preInstallerUUID);
            if (!empty($insBillingInfo['Email'])) {
                $chargeMode = getPersonalChargeModeByInsUUID($preInstallerUUID);
                if ($chargeMode && PersonalCheck::needNotifyIns(EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT, $chargeMode, $remainDays)) {
                    $emailInfo = array();
                    $emailInfo['web_ip'] = WEB_DOMAIN;
                    $emailInfo['email_type'] = $emailType;
                    $emailInfo['table_list'] = $lockList;
                    $emailInfo['email'] = $insBillingInfo['Email'];
                    $emailInfo['user'] = $insAccountInfo['Account'];     // 邮件的显示名称
                    $emailInfo['account'] = $insAccountInfo['Account'];  // 登录的账号名称（获取付费链接时使用）
                    $emailInfo['installer_id'] = $preInstallerID;  // 获取付费链接时使用）
                    $emailInfo['language'] = $insAccountInfo["Language"];
                    $emailInfo['remaining_days'] = strval($remainDays);
                    $emailInfo["expire_lock_num"] = count($lockList);   
                    $emailInfo["is_show_paylink"] = SHOW_PAYLINK_NONE;
                    // 检查付费模式(dis开启下级付费, ins就有权限)
                    if (PersonalCheck::ifInsHasPayPermission($preInstallerUUID)) {
                        $emailInfo["is_show_paylink"] = SHOW_PAYLINK_INSTALLER;
                    }
                    sendEmailNotify($emailInfo);
                }
            }
            $lockList = array();
        }
        $preInstallerUUID = $installerUUID;
        $preInstallerID = $installerID;
        $lockData = buildPerLockData($item);
        array_push($lockList, $lockData);
    }
}

//单住户底下多把锁同时过期时，只发送一封邮件
function personalWillExpireSendEmailToEnduser($emailType, $expireList, $remainDays)
{
    $prePersonalAccountUUID = "";
    $preitem = [];
    $lockList = array();
    array_push($expireList, array('PersonalAccountUUID' => "null"));  // 末尾插入空行，便于处理最后一条数据
    foreach ($expireList as $item) {
        if(empty($item)){
            continue;
        }
        // 当前记录personal和上一条记录personal不一样，说明是不同personal。发送上一个personal的邮件
        $personalaccountUUID = empty($item['PersonalAccountUUID']) ? "" : $item['PersonalAccountUUID'];
        // 获取个人账户信息
        $personalAccountInfo = getPersonalAccountInfobyPersonalAccountUUID($prePersonalAccountUUID);
        if ($prePersonalAccountUUID != $personalaccountUUID ) {
            if (!empty($personalAccountInfo['Email'])) {
                $chargeMode = getPersonalChargeModeByInsUUID($preitem['InstallerUUID']);
                if ($chargeMode && PersonalCheck::needNotifyEnduser(EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT, $chargeMode, $remainDays)) {
                    $emailInfo = array();
                    $emailInfo['user'] = $personalAccountInfo['Name'];
                    $emailInfo['email'] = $personalAccountInfo['Email'];
                    $emailInfo['email_type'] = $emailType;
                    $emailInfo['language'] = $preitem['LockInfo']['Language'];
                    $emailInfo['remaining_days'] = strval($remainDays);
                    $emailInfo['table_list'] = $lockList;
                    sendEmailNotify($emailInfo);
                }
            }
            $lockList = array();
        }
        $prePersonalAccountUUID = $personalaccountUUID;
        $lockData = buildPerLockData($item);
        array_push($lockList, $lockData);
        $preitem = $item;
    }
}

// 构建单住户锁邮件内容信息
function buildPerLockData($item) {
    return [
        'lock_name' => $item['LockInfo']['LockName'],
        'room_name' => $item['LockInfo']['RoomName'],
        'user_email' => $item['LockInfo']['UserEmail'],
        'user_mobile_number' => $item['LockInfo']['UserMobile'],
    ];
}

function communityExpiredSendEmailToIns($emailType, $expireList, $remainDays)
{
    communityWillExpireSendEmailToIns($emailType, $expireList, $remainDays);
}

function communityExpiredSendEmailToPM($emailType, $expireList, $remainDays)
{
    communityWillExpireSendEmailToPM($emailType, $expireList, $remainDays);
}

function communityExpireSendEmailToEndUser($emailType, $expireList, $remainDays)
{
    communityWillExpireSendEmailToEndUser($emailType, $expireList, $remainDays);
}

function communityWillExpireSendEmailToDis($emailType, $expireList, $remainDays)
{
    global $db;
    $lockList = array();
    $preProjectUUID = "";
    $preProjectID = "";
    $preInstallerUUID = "";
    $preDistributorUUID = "";
    array_push($expireList, array('ProjectUUID' => "null"));  // 末尾插入空行，便于处理最后一条数据
    foreach ($expireList as $item) {
        if (empty($item)) {
            continue;
        }
        // 当前记录社区uuid和上一条记录社区uuid不一样，说明是不同社区uuid。发送上一个社区的邮件
        $projectUUID = empty($item['ProjectUUID']) ? "" : $item['ProjectUUID'];
        $projectID = empty($item['ProjectID']) ? "" : $item['ProjectID'];
        $installerUUID = empty($item['InstallerUUID']) ? "" : $item['InstallerUUID'];
        $distributorUUID = empty($item['DistributorUUID']) ? "" : $item['DistributorUUID'];
        if ($preProjectUUID != $projectUUID && count($lockList) > 0) {
            // 查询 pre_distributor 的账号信息
            $disAccountInfo = getAccountInfoByAccountUUID($db, $preDistributorUUID);
            $disBillingInfo = getInstallerEmailInfoByAccountUUID($preDistributorUUID);
            if (!empty($disBillingInfo['Email'])) {
                $chargeMode = getCommunityChargeMode($preProjectID);
                if ($chargeMode && CommunityCheck::needNotifyDis(EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT, $chargeMode, $remainDays)) {
                    // 构造邮件信息和发送邮件
                    $emailInfo = array();             
                    $emailInfo['web_ip'] = WEB_DOMAIN;
                    $emailInfo['email_type'] = $emailType;
                    $emailInfo['remaining_days'] = strval($remainDays);
                    $emailInfo['community_name'] = $lockList[0]['community_name'];
                    $emailInfo['email'] = $disBillingInfo['Email'];
                    $emailInfo['user'] = $disAccountInfo['Account'];
                    $emailInfo['language'] = $disAccountInfo['Language'];
                    $emailInfo['table_list'] = $lockList;  
                    $emailInfo["expire_lock_num"] = count($lockList);
                    $emailInfo["account"] = $disAccountInfo['Account'];   //account,project_uuid,installer_uuid获取付费链接时使用 
                    $emailInfo["project_uuid"] = $preProjectUUID;
                    $emailInfo["installer_uuid"] = $preInstallerUUID;
                    $emailInfo['is_show_paylink'] = SHOW_PAYLINK_DISTRIBUTOR;   // dis一定有付费权限
                    
                    // 判断是否为SUBDIS，如果是判断是否需要显示付费链接
                    $account_info = getAccountInfo($preDistributorUUID);
                    if($account_info && $account_info['Grade'] == ACCOUNT_GRADE_SUBDIS){
                        if(!CommunityCheck::ifSubDisHasPayPermission($account_info)){
                            $email_info['is_show_paylink'] = SHOW_PAYLINK_NONE;
                        }
                    }
                    sendEmailNotify($emailInfo);
                }
            }
            $lockList = array();
        }
        // 插入当前用户数据
        $preDistributorUUID = $distributorUUID;
        $preInstallerUUID = $installerUUID;
        $preProjectUUID = $projectUUID;
        $preProjectID = $projectID;
        $lockData = buildCommLockData($item);
        array_push($lockList, $lockData);
    }
}

function communityWillExpireSendEmailToSubDis($emailType, $expireList, $remainDays)
{
    foreach ($expireList as $row => $item) {
        $expireList[$row]['DistributorUUID'] = $item['SubDistributorUUID'];
    }
    communityWillExpireSendEmailToDis($emailType, $expireList, $remainDays);
}

function communityWillExpireSendEmailToIns($emailType, $expireList, $remainDays)
{
    global $db;
    $lockList = array();
    $preProjectUUID = "";
    $preProjectID = "";
    $preInstallerUUID = "";
    array_push($expireList, array('ProjectUUID' => "null"));  // 末尾插入空行，便于处理最后一条数据
    foreach ($expireList as $item) {
        if (empty($item)) {
            continue;
        }
        $projectUUID = empty($item['ProjectUUID']) ? "" : $item['ProjectUUID'];
        $projectID = empty($item['ProjectID']) ? "" : $item['ProjectID'];
        $installerUUID = empty($item['InstallerUUID']) ? "" : $item['InstallerUUID'];
        if ($preProjectUUID != $projectUUID && count($lockList) > 0) {
            // 获取安装人员账户信息
            $insAccountInfo = getAccountInfoByAccountUUID($db, $preInstallerUUID);
            $insBillingInfo = getInstallerEmailInfoByAccountUUID($preInstallerUUID);

            if (!empty($insBillingInfo['Email'])) {
                $chargeMode = getCommunityChargeMode($preProjectID);
                if ($chargeMode && CommunityCheck::needNotifyIns(EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT, $chargeMode, $remainDays)) {
                    // 构建邮件信息
                    $emailInfo = array();             
                    $emailInfo['web_ip'] = WEB_DOMAIN;
                    $emailInfo['email_type'] = $emailType;
                    $emailInfo['remaining_days'] = strval($remainDays);
                    $emailInfo['community_name'] = $lockList[0]['community_name'];
                    $emailInfo['email'] = $insBillingInfo['Email'];
                    $emailInfo['user'] = $insAccountInfo['Account'];
                    $emailInfo['language'] = $insAccountInfo['Language'];
                    $emailInfo['is_show_paylink'] = SHOW_PAYLINK_NONE;
                    $emailInfo['table_list'] = $lockList;      
                    $emailInfo['account'] = $insAccountInfo['Account'];  // 登录的账号名称（获取付费链接时使用）
                    $emailInfo["project_uuid"] = $preProjectUUID;
                    $emailInfo["project_id"] = $preProjectID;
                    $emailInfo["expire_lock_num"] = count($lockList);          
                    if (CommunityCheck::ifInsHasPayPermission($preProjectUUID)) {
                        $emailInfo["is_show_paylink"] = SHOW_PAYLINK_INSTALLER;
                    }
                    sendEmailNotify($emailInfo);
                }
            }
            $lockList = array();
        }
        $preInstallerUUID = $installerUUID;
        $preProjectUUID = $projectUUID;
        $preProjectID = $projectID;
        $lockData = buildCommLockData($item);
        array_push($lockList, $lockData);
    }
}

function removeThirdPartyLocks(&$lockList)
{
    foreach ($lockList as $key => $item) {
        if (isset($item['lock_grade']) && $item['lock_grade'] == THIRD_PARTY_LOCK_IN_RESIDENCE_APT) {
            // 删除该项
            unset($lockList[$key]);
        }
    }
    // 重新索引数组
    $lockList = array_values($lockList);
}
//根据$chargeMode和 $remainDays过滤掉不需要发送给pm 的锁
function filterThirdPartLockByPm($chargeMode, $remainDays, &$lockList)
{
    $insMode = $chargeMode["InstallerMode"];
    $expireEmailType = $chargeMode["SendExpireEmailType"];
    $pmMode = $chargeMode["ProjectMode"];
    // Ins没有付费权限 或 Ins有信用卡付费权限
    if ($insMode == EmailNotifyRule::PAY_MODE_NO_PERMISSION || $insMode == EmailNotifyRule::PAY_MODE_CREDIT) {
        // 过期时间为 3和-1时，只有社区公共区域的三方锁过期需要通知pm
        if ($remainDays == 3 || $remainDays == -1) {
            removeThirdPartyLocks($lockList);
        }
    }
    // Ins有普通线上付费权限
    else if ($insMode == EmailNotifyRule::PAY_MODE_NORMAL) {
        if ($pmMode == EmailNotifyRule::PAY_MODE_NO_PERMISSION) {
            //通知类型为SEND_TO_PM_OR_ENDUSER 且过期时间为 3和-1时，只有社区公共区域的三方锁过期需要通知pm
            if ($expireEmailType == SEND_TO_PM_OR_ENDUSER) {
                if ($remainDays == 3 || $remainDays == -1) {
                    removeThirdPartyLocks($lockList);
                }
            }
        }else if ($pmMode == EmailNotifyRule::PAY_MODE_NORMAL) {
            // 过期时间为 3和-1时，payByPm 只有社区公共区域的三方锁过期需要通知pm
            if ($remainDays == 3 || $remainDays == -1) {
                removeThirdPartyLocks($lockList);
            }
        }
    }
}

function communityWillExpireSendEmailToPM($emailType, $expireList, $remainDays)
{
    global $db;
    $lockList = array();
    $preProjectUUID = "";
    $preProjectID = "";
    array_push($expireList, array('ProjectUUID' => "null"));  // 末尾插入空行，便于处理最后一条数据
    foreach ($expireList as $item) {
        if (empty($item)) {
            continue; 
        }
        $projectUUID = empty($item['ProjectUUID']) ? "" : $item['ProjectUUID'];
        $projectID = empty($item['ProjectID']) ? "" : $item['ProjectID'];
        if ($preProjectUUID != $projectUUID && count($lockList) > 0) {
            // 获取PM列表
            $pmInfoList = getCommAndPMInfoByMngID($preProjectUUID);
            foreach ($pmInfoList as $pmInfo) {
                $prePmAccountInfo  = getAccountInfoByAccountUUID($db, $pmInfo['UUID']);
                if (!empty($prePmAccountInfo['Email'])) {
                    $chargeMode = getCommunityChargeMode($preProjectID);
                    if ($chargeMode && CommunityCheck::needNotifyPm(EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT, $chargeMode, $remainDays)) {
                         //过滤掉不需要发送给pm的三封锁
                        filterThirdPartLockByPm($chargeMode, $remainDays, $lockList);
                        if(count($lockList) > 0)
                        {
                            // 构建邮件信息
                            $emailInfo = array();        
                            $emailInfo['web_ip'] = WEB_DOMAIN;
                            $emailInfo["email_type"] = $emailType;
                            $emailInfo['remaining_days'] = strval($remainDays);
                            $emailInfo['community_name'] = $lockList[0]['community_name'];
                            $emailInfo["email"] = $prePmAccountInfo['Email'];
                            $emailInfo['user'] = $pmInfo['FirstName'] . ' ' . $pmInfo['LastName'];
                            $emailInfo['language'] = $prePmAccountInfo['Language'];
                            $emailInfo["is_show_paylink"] = SHOW_PAYLINK_NONE;   
                            $emailInfo["table_list"] = $lockList;
                            $emailInfo["expire_lock_num"] = count($lockList); 
                            $emailInfo['account'] = $prePmAccountInfo['Email'];  // 获取付费链接时使用
                            $emailInfo["project_uuid"] = $preProjectUUID;
                            $emailInfo["project_id"] = $preProjectID;
                            if (CommunityCheck::ifPMHasPayPermission($preProjectUUID)) {
                                $emailInfo["is_show_paylink"] = SHOW_PAYLINK_PM;
                            }
                            sendEmailNotify($emailInfo);
                        }
                    }
                }
            }
            $lockList = array();
        }
        $preProjectUUID = $projectUUID;
        $preProjectID = $projectID;
        $lockData = buildCommLockData($item);
        array_push($lockList, $lockData);
        
    }
}

function communityWillExpireSendEmailToEndUser($emailType, $expireList, $remainDays)
{
    $prePersonalAccountUUID = "";
    $preitem = [];
    $lockList = array();
    array_push($expireList, array('PersonalAccountUUID' => "null"));  // 末尾插入空行，便于处理最后一条数据
    foreach ($expireList as $item) {
        if(empty($item)){
            continue;
        }
        //只有房间下的锁过期需要通知enduser,过滤掉不是房间下的锁
        if (isset($item['LockInfo']['Grade']) && $item['LockInfo']['Grade'] != THIRD_PARTY_LOCK_IN_RESIDENCE_APT) {
            continue;
        }
        // 当前记录personal和上一条记录personal不一样，说明是不同personal。发送上一个personal的邮件
        $personalaccountUUID = empty($item['PersonalAccountUUID']) ? "" : $item['PersonalAccountUUID'];
        if ($prePersonalAccountUUID != $personalaccountUUID && count($lockList)) {
            $projectUUID = empty($preitem['ProjectUUID']) ? "" : $preitem['ProjectUUID'];
            $projectID = empty($preitem['ProjectID']) ? "" : $preitem['ProjectID'];
            // 获取个人账户信息
            $personalAccountInfo = getPersonalAccountInfobyPersonalAccountUUID($prePersonalAccountUUID);
            if (!empty($personalAccountInfo['Email'])) {
                $chargeMode = getCommunityChargeMode($projectID);
                if ($chargeMode && CommunityCheck::needNotifyEndUser(EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT, $chargeMode, $remainDays)) {
                    // 构建邮件信息
                    $emailInfo = array();
                    $emailInfo['user'] = $personalAccountInfo['Name'];
                    $emailInfo['email'] = $personalAccountInfo['Email'];
                    $emailInfo['email_type'] = $emailType;
                    $emailInfo['language'] = $personalAccountInfo["Language"];
                    $emailInfo['remaining_days'] = strval($remainDays);
                    $emailInfo["project_uuid"] = $projectUUID;
                    $emailInfo["table_list"] = $lockList;
                    sendEmailNotify($emailInfo);
                }
            }
            $lockList = array();
        }
        $prePersonalAccountUUID = $personalaccountUUID;
        $lockData = buildCommLockData($item);
        array_push($lockList, $lockData);
        $preitem = $item;
    }
}

// 构建社区锁邮件内容信息
function buildCommLockData($item) {
    return [
        'lock_name' => $item['LockInfo']['LockName'],
        'community_name' => $item['LockInfo']['CommunityName'],
        'room_name' => $item['LockInfo']['RoomName'],
        'unit_name' => $item['LockInfo']['UnitName'],
        'lock_grade' =>$item['LockInfo']['Grade']
    ];
}

$medooDb = getMedooDb();
$db = $medooDb->pdo;

// 检查不同天数的过期情况
checkThirdPartyLockExpire(15);
checkThirdPartyLockExpire(5);
checkThirdPartyLockExpire(3);
checkThirdPartyLockExpire(-1);
LOG_INFO("Check Third Party Lock Expire end");
