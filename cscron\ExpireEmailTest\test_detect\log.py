# coding=utf-8

def clean_local_logs(log_file_path):
    # 清除日志文件
    with open(log_file_path, "w"):
        pass

def check_log(log_file_path, env):
    # 读取并保存日志文件内容
    logs = []
    with open(log_file_path, "r") as file:
        for line in file:
            logs.append(line.strip())

    while True:
        if len(logs) > 0:
            item = logs.pop(0)
            if (check_log_line(item, env)):
                return True
        else:
            break
        
    return False

def check_log_line(item, env):
    all_checks_passed = True
    try:
        for key, value in env.items():
            if value not in item:
                all_checks_passed = False

        if all_checks_passed:
            return True
    except Exception as e:
        print(item)
    return False
