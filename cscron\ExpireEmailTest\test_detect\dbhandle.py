# coding=utf-8
from common.define import *
from common.db import GetDbConn

# 支付模式
PAY_MODE_NORMAL         = 0;    # 普通支付
PAY_MODE_NO_PERMISSION  = 1;    # 没有权限
PAY_MODE_CREDIT         = 2;    # 信用卡支付

def setChargeMode(env_ini, one_test):
    pay_mode = one_test["PayMode"]
    send_type = one_test["SendType"]
    dis_uuid = env_ini["DIS_UUID"]
    ins_uuid = env_ini["INS_UUID"]
    project_uuid = env_ini["PROJECT_UUID"]
    
    conn = GetDbConn()
    cursor = conn.cursor()
    if pay_mode == "DIS":
        # Dis总是有权限，Ins没有权限
        cursor.execute("update Account set PayType =  %s where UUID = '%s'" % (PAY_MODE_NO_PERMISSION, ins_uuid))
        conn.commit()
    elif pay_mode == "INS":
        # Ins有权限，PM没有权限
        cursor.execute("update Account set PayType =  %s where UUID = '%s'" % (PAY_MODE_NORMAL, ins_uuid))
        conn.commit()
        cursor.execute("update Account set ChargeMode =  %s where UUID = '%s'" % (PAY_MODE_NO_PERMISSION, project_uuid))
        conn.commit()
    elif pay_mode == "PM":
        # Ins有权限，PM有权限
        cursor.execute("update Account set PayType =  %s where UUID = '%s'" % (PAY_MODE_NORMAL, ins_uuid))
        conn.commit()
        cursor.execute("update Account set ChargeMode =  %s where UUID = '%s'" % (PAY_MODE_NORMAL, project_uuid))
        conn.commit()
        
    if send_type == "SendToPMOrEndUser":
        cursor.execute("update Account set SendExpireEmailType = 2 where UUID = '%s'" % project_uuid)
        conn.commit()
    elif send_type == "SendToPM":
        cursor.execute("update Account set SendExpireEmailType = 3 where UUID = '%s'" % project_uuid)
        conn.commit()
    else :
        cursor.execute("update Account set SendExpireEmailType = 1 where UUID = '%s'" % project_uuid)
        conn.commit()
    
    cursor.close()
    conn.close()
    
def setUserLandlineExpireDays(single_env, expire_days):
    node_uuid = single_env['NODE_UUID']
    conn = GetDbConn()
    cursor = conn.cursor()
    cursor.execute("UPDATE PersonalAccount SET PhoneExpireTime = DATE_ADD(NOW(), INTERVAL %s DAY) WHERE UUID = '%s'" % (expire_days, node_uuid))
    conn.commit()
    cursor.close()
    conn.close()
    
def setUserAppExpireDays(env_ini, expire_days):
    node_uuid = env_ini['NODE_UUID']
    conn = GetDbConn()
    cursor = conn.cursor()
    cursor.execute("UPDATE PersonalAccount SET ExpireTime = DATE_ADD(NOW(), INTERVAL %s DAY) WHERE UUID = '%s'" % (expire_days, node_uuid))
    conn.commit()
    cursor.close()
    conn.close()
    
def setOfficeFeatureExpireDays(env_ini, expire_days):
    project_uuid = env_ini['PROJECT_UUID']
    conn = GetDbConn()
    cursor = conn.cursor()
    cursor.execute("UPDATE OfficeInfo SET FeatureExpireTime = DATE_ADD(NOW(), INTERVAL %s DAY) WHERE AccountUUID = '%s'" % (expire_days, project_uuid))
    conn.commit()
    cursor.close()
    conn.close()
    
def setUserAutoPayOrderActive(env_ini):
    order_uuid = env_ini['ORDER_UUID']
    conn = GetDbConn()
    cursor = conn.cursor()
    cursor.execute("UPDATE SubscriptionList SET Status = 1 WHERE UUID = '%s'" % order_uuid)
    conn.commit()
    cursor.close()
    conn.close() 
    
def setUserAutoPayOrderCancel(env_ini):
    order_uuid = env_ini['ORDER_UUID']
    conn = GetDbConn()
    cursor = conn.cursor()
    cursor.execute("UPDATE SubscriptionList SET Status = 5 WHERE UUID = '%s'" % order_uuid)
    conn.commit()
    cursor.close()
    conn.close() 

def setUserAutoPayChargeDays(env_ini, charge_days):
    order_uuid = env_ini['ORDER_UUID']
    conn = GetDbConn()
    cursor = conn.cursor()
    cursor.execute("UPDATE SubscriptionList SET NextPayTime = DATE_ADD(NOW(), INTERVAL %s DAY) WHERE UUID = '%s'" % (charge_days, order_uuid))
    conn.commit()
    cursor.close()
    conn.close()
    
def setUserAutoPayPayerInfo(env_ini):
    order_uuid = env_ini['ORDER_UUID']
    conn = GetDbConn()
    cursor = conn.cursor()
    cursor.execute("UPDATE SubscriptionList SET PayerUUID = '%s', PayerType = %d WHERE UUID = '%s'" % (env_ini['PayerUUID'], int(env_ini['PayerType']), order_uuid))
    conn.commit()
    cursor.close()
    conn.close() 
    
def setVideoRecordExpireDaysByProjectUUID(env_ini, expire_days):
    project_uuid = env_ini['PROJECT_UUID']
    conn = GetDbConn()
    cursor = conn.cursor()
    cursor.execute("UPDATE VideoStorage SET ExpireTime = DATE_ADD(NOW(), INTERVAL %s DAY) WHERE AccountUUID = '%s'" % (expire_days, project_uuid))
    conn.commit()
    cursor.close()
    conn.close()
    
def setVideoRecordExpireDaysByPersonalUUID(env_ini, expire_days):
    personal_uuid = env_ini['SINGLE_NODE_UUID']
    conn = GetDbConn()
    cursor = conn.cursor()
    cursor.execute("UPDATE VideoStorage SET ExpireTime = DATE_ADD(NOW(), INTERVAL %s DAY) WHERE PersonalAccountUUID = '%s'" % (expire_days, personal_uuid))
    conn.commit()
    cursor.close()
    conn.close()

def setRentMangerExpireDaysByPMUUID(env_ini, expire_days):
    pm_uuid = env_ini['PM_UUID']
    conn = GetDbConn()
    cursor = conn.cursor()
    cursor.execute("UPDATE RentManagerCustomer SET ExpiredTime = DATE_ADD(NOW(), INTERVAL %s DAY) WHERE PmUUID = '%s'" % (expire_days, pm_uuid))
    conn.commit()
    cursor.close()
    conn.close()

def updateCommunityInfoSetLastDevOfflineNotifyTime(account_uuid, time_str):
    conn = GetDbConn()
    cursor = conn.cursor()
    cursor.execute("UPDATE CommunityInfo SET LastDevOfflineNotifyTime='%s' WHERE AccountUUID='%s'" % (time_str, account_uuid))
    conn.commit()
    cursor.close()
    conn.close()

def updateOfficeInfoSetLastDevOfflineNotifyTime(account_uuid, time_str):
    conn = GetDbConn()
    cursor = conn.cursor()
    cursor.execute("UPDATE OfficeInfo SET LastDevOfflineNotifyTime='%s' WHERE AccountUUID='%s'" % (time_str, account_uuid))
    conn.commit()
    cursor.close()
    conn.close()

def setDeviceOfflineTime(account_uuid, mac, time_str):
    conn = GetDbConn()
    cursor = conn.cursor()
    cursor.execute("UPDATE Devices SET Status=0,LastDisConn='%s' WHERE AccountUUID='%s' AND MAC='%s'" % (time_str, account_uuid, mac))
    conn.commit()
    cursor.close()
    conn.close()
