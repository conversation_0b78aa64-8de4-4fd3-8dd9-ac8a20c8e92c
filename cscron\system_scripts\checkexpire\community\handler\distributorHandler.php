<?php
require_once __DIR__ . '/../../../common/email_common.php';

class DistributorHandler 
{
    private $daysBefore;
    private $expireDataMap;
    private $communityExpireInfoList;
    private $communityChargeModeMap;
    public $expireDataControl;

    public function __construct() {
    }
    
    public function setExpireParams($daysBefore, $expireDataMap, $communityExpireInfoList, $communityChargeModeMap, $expireDataControl) {
        $this->daysBefore = $daysBefore;
        $this->expireDataMap = $expireDataMap;
        $this->communityExpireInfoList = $communityExpireInfoList;
        $this->communityChargeModeMap = $communityChargeModeMap;
        $this->expireDataControl = $expireDataControl;
    }

    public function filterData() {
        // 判断社区的付费模式 是否要发送给Distributor
        $filteredExpireData = $this->filterCommunityChargeMode();
        return $filteredExpireData;
    }

    // 判断社区的付费模式
    private function filterCommunityChargeMode() {
        // 判断社区的付费模式 是否要发送给Distributor
        $payTypeList = array(
            EmailNotifyRule::PAY_ITEM_USER_APP,
            EmailNotifyRule::PAY_ITEM_PM_APP,
            EmailNotifyRule::PAY_ITEM_VIDEO_RECORD,
            EmailNotifyRule::PAY_ITEM_FEATURE_PLAN,
            EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT,
        );

        // 获取每种付费类型发送给Dis的社区列表
        $payTypeCommunityUUIDMap = array();
        foreach ($payTypeList as $payType) {
            foreach ($this->communityExpireInfoList as $communityInfo) {
                $communityChargeMode = $this->communityChargeModeMap[$communityInfo['UUID']];
                if ($communityChargeMode && CommunityCheck::needNotifyDis($payType, $communityChargeMode, $this->daysBefore)) {
                    $payTypeCommunityUUIDMap[$payType][] = $communityInfo['UUID'];
                }
            }
        }

        // 获取每种付费类型发送给Dis的社区列表
        $filteredExpireData = array();
        foreach ($this->expireDataMap as $payType => $expireData) {
            foreach ($expireData as $expireInfo) {
                if (isset($payTypeCommunityUUIDMap[$payType]) && in_array($expireInfo['CommunityUUID'], $payTypeCommunityUUIDMap[$payType])) {
                    $filteredExpireData[$payType][] = $expireInfo;
                }
            }
        }
        return $filteredExpireData;
    }   

    // 发送邮件
    public function sendEmail($emailDataMap, $recevierInfoMap) {
        // 发给每个DIS
        foreach ($emailDataMap as $disUUID => $disEmailData) {
            $disEmail = $recevierInfoMap[$disUUID]['email'];
            $loginAccount = $recevierInfoMap[$disUUID]['loginAccount'];
            if (empty($disEmail)) {
                LOG_INFO("disEmail is empty, skip, disUUID: " . $disUUID);
                continue;
            }

            // dis下的每个ins都要发一封
            $insEmailDataMap = $this->getInsEmailDataMap($disEmailData);
            foreach ($insEmailDataMap as $insUUID => $insEmailData) {

                // ins下社区时区一致的一起发送
                $timezonEmailDataMap = $this->getTimeZoneEmailDataMap($insEmailData);
                LOG_INFO("dis timezonEmailDataMap: " . json_encode($timezonEmailDataMap));
                foreach ($timezonEmailDataMap as $timezone => $emailData) {

                    $emailInfo['email'] = $disEmail;
                    $emailInfo["web_domain"] = WEB_DOMAIN;
                    $emailInfo['installer_uuid'] = $insUUID;
                    $emailInfo['before'] = $this->daysBefore;
                    $emailInfo['login_account'] = $loginAccount;
                    $emailInfo['oem'] = $recevierInfoMap[$disUUID]['oem'];
                    $emailInfo['user'] = $recevierInfoMap[$disUUID]['user'];
                    $emailInfo['language'] = $recevierInfoMap[$disUUID]['language'];
                    $emailInfo['is_show_paylink'] = SHOW_PAYLINK_DISTRIBUTOR;

                    if ($this->daysBefore == -1) {
                        $emailInfo['email_type'] = "community_has_expired";
                    } else {
                        $emailInfo['email_type'] = "community_will_expire";
                    }
                    $emailInfo['renew_uuid'] = $this->recordRenewPaymentList($emailData);
        
                    $emailInfo['list'] = $emailData;
                    sendEmailNotifyNew($emailInfo);
        
                    // 发送邮件给subdis
                    $this->sendSubDisEmail($disUUID,  $recevierInfoMap[$disUUID]['oem'], $emailData);
                }
            }
        }
    }

    // 每个ins单独发送
    private function getInsEmailDataMap($disEmailData) {
        $insEmailDataMap = array();
        foreach ($disEmailData as $payType => $dataList) {
            foreach ($dataList as $data) {
                $insEmailDataMap[$data['InsUUID']][$payType][] = $data;
            }
        }
        return $insEmailDataMap;
    }

    // ins下同一个时区的一起发送
    private function getTimeZoneEmailDataMap($insEmailData) {
        $timeZoneEmailDataMap = array();
        foreach ($insEmailData as $payType => $dataList) {
            foreach ($dataList as $data) {
                $timeZonePrefix = explode(' ', $data['TimeZone'])[0];
                $timeZoneEmailDataMap[$timeZonePrefix][$payType][] = $data;
                LOG_INFO("dis getTimeZoneEmailDataMap timeZonePrefix: " . $timeZonePrefix);
                LOG_INFO("dis getTimeZoneEmailDataMap data: " . json_encode($data));
            }
        }
        return $timeZoneEmailDataMap;
    }

    private function recordRenewPaymentList($emailData) {
        $communityUUIDList = array();
        foreach ($emailData as $payItem => $dataList) {
            foreach ($dataList as $data) {
                $communityUUIDList[] = $data['CommunityUUID'];
            }
        }

        $renewUUID = $this->expireDataControl->commonQuery->GenerateUUID();
        $communityUUIDList = array_unique($communityUUIDList);
        foreach ($communityUUIDList as $communityUUID) {
            $this->expireDataControl->commonQuery->recordEmailRenewPayment($renewUUID, $communityUUID);
        }

        return $renewUUID;
    }

    // 发送邮件给subdis
    private function sendSubDisEmail($disUUID, $oem, $emailData)
    {
        // 获取dis下的所有subDis
        $subDisInfoList = $this->expireDataControl->commonQuery->getSubDisListInfoByDisUUID($disUUID);

        // 遍历subDis发送邮件
        foreach ($subDisInfoList as $row => $subDisInfo) {
            // 获取subDis的billingInfo
            $subDisBillingInfo = $this->expireDataControl->commonQuery->getBillingInfoByAccountUUID($subDisInfo['UUID']);
            if (!$subDisBillingInfo || empty($subDisBillingInfo['Email'])) {
                continue;
            }

            // 获取subDis的subDisMngList
            $subDisMngInstallerList = $this->expireDataControl->commonQuery->getSubDisMngInstallerUUIDList($subDisInfo['UUID']);
            if(!$subDisMngInstallerList) {
                continue;
            }
            $subDisMngInstallerUUIDList = array_column($subDisMngInstallerList, 'InsUUID');

            // 判断subDis是否管理data的Ins
            $subDisEmailDataMap = array();
            foreach ($emailData as $payType => $dataList) {
                foreach ($dataList as $data) {
                    // subdis管理了数据的ins
                    if (in_array($data['InsUUID'], $subDisMngInstallerUUIDList)) {
                        $subDisEmailDataMap[$payType][] = $data;
                    }
                }
            }

            if (!$subDisEmailDataMap) {
                continue;
            }
            
            $emailInfo['oem'] = $oem;
            $emailInfo["web_domain"] = WEB_DOMAIN;
            $emailInfo['before'] = $this->daysBefore;
            $emailInfo['user'] = $subDisInfo['Account'];
            $emailInfo['language'] = $subDisInfo['Language'];
            $emailInfo['email'] = $subDisBillingInfo['Email'];
            $emailInfo['is_show_paylink'] = SHOW_PAYLINK_DISTRIBUTOR;

            if ($this->daysBefore == -1) {
                $emailInfo['email_type'] = "community_has_expired";
            } else {
                $emailInfo['email_type'] = "community_will_expire";
            }

            if (!EmailNotifyRule::IfHasPayPermission($subDisInfo['SubDisMode'])) {
                $email_info['is_show_paylink'] = SHOW_PAYLINK_NONE;
            }

            $emailInfo['list'] = $emailData;
            sendEmailNotifyNew($emailInfo);
        }
    }
}