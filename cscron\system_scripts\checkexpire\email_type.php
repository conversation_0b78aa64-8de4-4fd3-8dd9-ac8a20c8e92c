<?php

/***************************  check_video_record_expire.php ***************************/
// 社区账户，视频存储即将过期，发送给Dis/Ins/PM
const EMAIL_TYPE_COMMUNITY_VIDEO_RECORD_WILL_EXPIRE = "community_video_record_will_expire";
// 社区账户，视频存储已经过期，发送给Ins/PM
const EMAIL_TYPE_COMMUNITY_VIDEO_RECORD_HSA_EXPIRED = "community_video_record_has_expired";
// 个人账户，视频存储即将过期，发送给Dis/Ins
const EMAIL_TYPE_PERSONAL_VIDEO_RECORD_WILL_EXPIRE = "personal_video_record_will_expire";
// 个人账户，视频存储已经过期，发送给Ins
const EMAIL_TYPE_PERSONAL_VIDEO_RECORD_HAS_EXPIRED = "personal_video_record_has_expired";
// 个人账户，视频存储即将过期，发送给Enduser
const EMAIL_TYPE_PERSONAL_VIDEO_RECORD_WILL_EXPIRE_TO_ENDUSER = "personal_video_record_will_expire_to_enduser";
// 个人账户，视频存储已经过期，发送给Enduser
const EMAIL_TYPE_PERSONAL_VIDEO_RECORD_HAS_EXPIRED_TO_ENDUSER = "personal_video_record_has_expired_to_enduser";

/***************************  rent_manager/check_rent_expire.php ***************************/
// RentManager即将过期，发送邮件通知Ins
const EMAIL_TYPE_RENT_CUSTOMER_Will_EXPIRE_TO_INS = "rent_customer_will_expire_to_ins";
// RentManager已经过期，发送邮件通知Ins
const EMAIL_TYPE_RENT_CUSTOMER_HAS_EXPIRED_TO_INS = "rent_customer_has_expired_to_ins";
// RentManager即将过期，发送邮件通知PM
const EMAIL_TYPE_RENT_CUSTOMER_WILL_EXPIRE_TO_PM = "rent_customer_will_expire_to_pm";
// RentManager已经过期，发送邮件通知PM
const EMAIL_TYPE_RENT_CUSTOMER_HAS_EXPIRED_TO_PM = "rent_customer_has_expired_to_pm";
// RentManager过期，发送邮件通知Admin
const EMAIL_TYPE_RENT_CUSTOMER_HAS_EXPIRED_TO_ADMIN = "rent_customer_has_expired_to_admin";
// RentManager付费成功，发送邮件通知Admin
const EMAIL_TYPE_RENT_CUSTOMER_HAS_PAID_TO_ADMIN = "rent_customer_has_paid_to_admin";

/***************************  newoffice/check_admin_expire.php ***************************/
// 门、员工-对讲功能、Admin-App激活邮件
const EMAIL_TYPE_NEWOFFICE_MULTI_ACTIVE = "newoffice_multi_active";
// 门、员工-对讲功能、Admin-App续费邮件
const EMAIL_TYPE_NEWOFFICE_MULTI_REPAY  = "newoffice_multi_repay";
// 门、员工-对讲功能、Admin-App即将过期邮件
const EMAIL_TYPE_NEWOFFICE_MULTI_WILL_EXPIRE = "newoffice_multi_will_expire";
// 门、员工-对讲功能、Admin-App过期邮件
const EMAIL_TYPE_NEWOFFICE_MULTI_HAS_EXPIRED = "newoffice_multi_has_expired";

/***************************  check_third_party_lock_expire.php ***************************/
// 社区账户，三方锁即将过期，发送给Dis/Ins/PM
const EMAIL_TYPE_COMMUNITY_THIRD_PARTY_LOCK_WILL_EXPIRE = "community_third_party_lock_will_expire";
// 社区账户，三方锁已经过期，发送给Ins/PM
const EMAIL_TYPE_COMMUNITY_THIRD_PARTY_LOCK_HSA_EXPIRED = "community_third_party_lock_has_expired";
// 社区账户，三方锁即将过期，发送给EndUser
const EMAIL_TYPE_COMMUNITY_THIRD_PARTY_LOCK_WILL_EXPIRE_TO_ENDUSER = "community_third_party_lock_will_expire_to_enduser";
// 社区账户，三方锁已经过期，发送给EndUser
const EMAIL_TYPE_COMMUNITY_THIRD_PARTY_LOCK_HSA_EXPIRED_TO_ENDUSER = "community_third_party_lock_has_expired_to_enduser";
// 个人账户，三方锁即将过期，发送给Dis/Ins
const EMAIL_TYPE_PERSONAL_THIRD_PARTY_LOCK_WILL_EXPIRE = "personal_third_party_lock_will_expire";
// 个人账户，三方锁已经过期，发送给Ins
const EMAIL_TYPE_PERSONAL_THIRD_PARTY_LOCK_HAS_EXPIRED = "personal_third_party_lock_has_expired";
// 个人账户，三方锁即将过期，发送给Enduser
const EMAIL_TYPE_PERSONAL_THIRD_PARTY_LOCK_WILL_EXPIRE_TO_ENDUSER = "personal_third_party_lock_will_expire_to_enduser";
// 个人账户，三方锁已经过期，发送给Enduser
const EMAIL_TYPE_PERSONAL_THIRD_PARTY_LOCK_HAS_EXPIRED_TO_ENDUSER = "personal_third_party_lock_has_expired_to_enduser";

?>
