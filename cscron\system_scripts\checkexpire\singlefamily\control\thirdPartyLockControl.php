<?php
require_once(dirname(__FILE__) . '/expireDataCheckUtil.php');
require_once(dirname(__FILE__) . '/../../data_confusion.php');
require_once(dirname(__FILE__) . '/../model/thirdPartyLockQuery.php');

// 第三方锁过期检测
class ThirdPartyLockControl extends ExpireDataCheckUtil
{
    public $commonQuery;
    private $thirdPartyLockQuery;

    const DORMAKABA_LOCK_TYPE  = 3;
    const ITEC_LOCK_TYPE       = 6;
    const THIRD_PARTY_LOCK_IN_RESIDENCE_PUB    = 1;
    const THIRD_PARTY_LOCK_IN_RESIDENCE_APT    = 2;
    const AUTO_PAY_ORDER_TYPE_SINGLE_VIDEO_STORAGE = 5;

    public function __construct($db, $medooDb, $commonQuery)
    {
        parent::__construct($db, $medooDb);
        $this->commonQuery = $commonQuery;
        $this->thirdPartyLockQuery = new ThirdPartyLockQuery($db, $medooDb);
    }

    public function getExpireData($daysBefore)
    {
        // 获取过期数据
        $expireData = $this->thirdPartyLockQuery->getExpireData($daysBefore);

        // 过滤掉迁移dis
        $expireData = $this->filterMigrateDistributor($expireData);

        // 过滤掉自动扣费的用户
        $expireData = $this->filterThirdPartyLockAutoPayUser($expireData);   

        // 获取锁信息 
        $expireData = $this->getExpireLockInfo($expireData);

        return $expireData;
    }

    private function filterThirdPartyLockAutoPayUser($expireData)
    {
        $filteredData = [];
        foreach ($expireData as $data) {
            if (!$this->commonQuery->IsUserEnableThirdPartyLockAutoPay($data['PersonalAccountUUID'], self::AUTO_PAY_ORDER_TYPE_SINGLE_VIDEO_STORAGE)) {
                $filteredData[] = $data;
            }
        }
        return $filteredData;
    } 

      // 获取锁的信息
      private function getExpireLockInfo($expireData)
      {
          if (empty($expireData) || !is_array($expireData)) {
              return [];
          }
          
          $result = [];
          foreach ($expireData as $index => $data) {
              $updatedData = $data;
  
              $lockInfo = [];
              if ($data['LockBrand'] == self::DORMAKABA_LOCK_TYPE) {
                  $lockInfo = $this->thirdPartyLockQuery->getDormakabaLockInfo($data['LockUUID']);
              } elseif ($data['LockBrand'] == ITEC_LOCK_TYPE) {
                  $lockInfo = $this->thirdPartyLockQuery->getItecLockInfo($data['LockUUID']);
              }   
              $updatedData['LockName'] = $lockInfo['LockName'] ?? '';

              $result[] = $updatedData;
          }
          return $result;
      }
}