<?php
require_once(dirname(__FILE__).'/notify_check.php');
require_once(dirname(__FILE__).'/check_expire_common_v4500.php');

const AUTO_PAY_ORDER_STATUS_ACTIVE = 0;
const AUTO_PAY_ORDER_STATUS_AMOUNT_CHANGE = 1;
const AUTO_PAY_ORDER_STATUS_PAY_SUCCESS = 2;
const AUTO_PAY_ORDER_STATUS_PAY_ONCE_FAILED = 3;
const AUTO_PAY_ORDER_STATUS_PAY_MULTI_FAILED = 4;
const AUTO_PAY_ORDER_STATUS_CANCELED = 5;

const AUTO_PAY_ORDER_TYPE_SINGLE = 1;
const AUTO_PAY_ORDER_TYPE_COMMUNITY = 2;
const AUTO_PAY_ORDER_TYPE_OFFICE = 3;
const AUTO_PAY_ORDER_TYPE_SINGLE_VIDEO_STORAGE = 5;
const AUTO_PAY_ORDER_TYPE_COMMUNITY_VIDEO_STORAGE = 6;
const AUTO_PAY_ORDER_TYPE_MIX = 7;
const AUTO_PAY_ORDER_TYPE_SINGLE_THIRD_LOCK = 8;
const AUTO_PAY_ORDER_TYPE_COMMUNITY_THIRD_LOCK = 9;

const AUTO_PAY_ORDER_PAYER_TYPE_ENDUSER = 0;
const AUTO_PAY_ORDER_PAYER_TYPE_PM = 1;
const AUTO_PAY_ORDER_PAYER_TYPE_INS = 2;
const AUTO_PAY_ORDER_PAYER_TYPE_DIS = 3;
const AUTO_PAY_ORDER_PAYER_TYPE_SUB_DIS = 4;

const SUBSCRIPTIONLIST_MIXTYPE_SINGLE    = 1;
const SUBSCRIPTIONLIST_MIXTYPE_COMMUNITY = (1 << 1);
const SUBSCRIPTIONLIST_MIXTYPE_OFFICE    = (1 << 2);

// 根据订单类型转换成项目类型
function GetProjectTypeByOrderInfo($order_info)
{
    $order_pay_type = $order_info['Type'];
    $mix_type = $order_info['MixType'];

    //pay_type 转成 project_type
    if ($order_pay_type == AUTO_PAY_ORDER_TYPE_COMMUNITY || $order_pay_type == AUTO_PAY_ORDER_TYPE_COMMUNITY_VIDEO_STORAGE || $order_pay_type == AUTO_PAY_ORDER_TYPE_COMMUNITY_THIRD_LOCK) {
        return PROJECT_TYPE_RESIDENCE;
    } else if ($order_pay_type == AUTO_PAY_ORDER_TYPE_OFFICE) {
        return PROJECT_TYPE_OFFICE;
    } else if ($order_pay_type == AUTO_PAY_ORDER_TYPE_MIX) {
        if ($mix_type & SUBSCRIPTIONLIST_MIXTYPE_COMMUNITY) {
            return PROJECT_TYPE_RESIDENCE;
        } else if ($mix_type & SUBSCRIPTIONLIST_MIXTYPE_OFFICE) {
            return PROJECT_TYPE_OFFICE;
        }
    }
    
    return PROJECT_TYPE_PERSONAL;
}

//用户是否有正常开通自动扣费
function IsUserEnableAutoPay($uuid, $autopay_type)
{
    global $db;

    //用户可能有历史订单，取用户最新的订单
    $sth = $db->prepare("select A.Status from SubscriptionList A left join SubscriptionEndUserList B on A.UUID = B.SubscriptionUUID
    left join PersonalAccount C on B.PersonalAccountUUID = C.UUID where C.UUID = :uuid and B.Type = :auto_pay_type order by A.ID desc limit 1");
    $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    $sth->bindParam(':auto_pay_type', $autopay_type, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result && $result['Status'] == 1)
    {
        LOG_INFO("UUID:$uuid EnableAutoPay");
        return true;
    }
    return false;
}

//单住户终端用户/社区是否有开启视频存储自动扣费
function IsUserEnableVideoStorageAutoPay($uuid, $autopay_type)
{
    global $db;

    //用户可能有历史订单，取用户最新的订单
    $sth = $db->prepare("select A.Status from SubscriptionList A left join SubscriptionEndUserList B on A.UUID = B.SubscriptionUUID
    where B.SiteUUID = :uuid and B.Type = :auto_pay_type order by A.ID desc limit 1");
    $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    $sth->bindParam(':auto_pay_type', $autopay_type, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result && $result['Status'] == 1)
    {
        LOG_INFO("UUID:$uuid EnableAutoPay");
        return true;
    }
    return false;
}

//单住户终端用户/社区是否有开启三方锁自动扣费
function IsUserEnableThirdPartyLockAutoPay($uuid, $autopay_type)
{
    global $db;

    //用户可能有历史订单，取用户最新的订单
    $sth = $db->prepare("select A.Status from SubscriptionList A left join SubscriptionEndUserList B on A.UUID = B.SubscriptionUUID
    where B.LockUUID = :uuid and B.Type = :auto_pay_type order by A.ID desc limit 1");
    $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    $sth->bindParam(':auto_pay_type', $autopay_type, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result && $result['Status'] == 1)
    {
        return true;
    }
    return false;
}

function getWillChargeOrderList($before)
{
/*
    $sth = $db->prepare("select Status,EndReason,TotalPrice,Type,PayerUUID,PayerEmail,PayerType,ProjectUUID,IsBatch,PayPlatform,NextPayTime,LastPayTime,UUID,TimeZone from SubscriptionList
    where (TO_DAYS(NextPayTime) = TO_DAYS(NOW()) + :before) and Status = 1");
    $sth->bindParam(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $order_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    return $order_list;
*/
    global $medooDb;

    return  $medooDb->select("SubscriptionList", ["Status","EndReason","TotalPrice","Type","PayerUUID","PayerEmail","PayerType","ProjectUUID","IsBatch","PayPlatform","NextPayTime","LastPayTime","UUID","TimeZone","MixType" ], 
            $medooDb->raw("where (TO_DAYS(NextPayTime) = TO_DAYS(NOW()) + :before) and Status = 1", [':before' => $before]));
}

function getProjectNameStrByOrderInfo($order_info)
{
    global $db;
    if ($order_info['IsBatch']) {
        return getBatchProjectNames($order_info);
    }

    $project_type = GetProjectTypeByOrderInfo($order_info);
    if ($project_type ==  PROJECT_TYPE_PERSONAL) {
        $str = getSingleAutoPayUsers($order_info['UUID']);
        return $str;
    } else if ($project_type == PROJECT_TYPE_RESIDENCE || $project_type == PROJECT_TYPE_OFFICE) {
        $sth = $db->prepare("select Location from Account where UUID = :uuid");
        $sth->bindParam(':uuid', $order_info['ProjectUUID'], PDO::PARAM_STR);
        $sth->execute();
        $ret = $sth->fetch(PDO::FETCH_ASSOC); 
        return $ret['Location'];
    }

    return '';
}

function getBatchProjectNames($order_info)
{
    global $db;

    $project_name_arr = array();
    $sth = $db->prepare("select DISTINCT ProjectUUID from SubscriptionEndUserList where SubscriptionUUID = :uuid");
    $sth->bindParam(':uuid', $order_info['UUID'], PDO::PARAM_STR);
    $sth->execute();
    $project_uuids = $sth->fetchAll(PDO::FETCH_ASSOC);
    foreach ($project_uuids as $project_uuid) {
        $sth = $db->prepare("select Location from Account where UUID = :uuid");
        $sth->bindParam(':uuid', $project_uuid['ProjectUUID'], PDO::PARAM_STR);
        $sth->execute();
        $ret = $sth->fetch(PDO::FETCH_ASSOC);
        array_push($project_name_arr, $ret['Location']);
    }
	//如果名称本身包含","会有异常，当前因为模板就是通过，作为分隔符展示所以不做修改
    $str = implode(",", $project_name_arr);
    return $str;
}

function getSingleAutoPayUsers($order_uuid)
{
/*
    global $db;
    $sth = $db->prepare("select A.Name,A.Role,A.RoomNumber,A.ParentUUID from PersonalAccount A left join SubscriptionEndUserList B on A.UUID = B.PersonalAccountUUID where B.SubscriptionUUID = :uuid");
    $sth->bindParam(':uuid', $order_uuid, PDO::PARAM_STR);
    $sth->execute();
    $personal_account_infos = $sth->fetchAll(PDO::FETCH_ASSOC);
*/    
    global $medooDb;

    $personalAccountList = $medooDb->select("PersonalAccount", [
        "[>]SubscriptionEndUserList" => ["PersonalAccount.UUID" => "PersonalAccountUUID"]
    ], [
        "PersonalAccount.Name",
        "PersonalAccount.Role",
        "PersonalAccount.RoomNumber",
        "PersonalAccount.ParentUUID",
        "SubscriptionEndUserList.SubscriptionUUID"
    ], [
        "SubscriptionEndUserList.SubscriptionUUID" => $order_uuid
    ]);

    $room_names = [];

    foreach ($personalAccountList as $account_info) {
        if ($account_info['RoomNumber']) {
            array_push($room_names, $account_info['RoomNumber']);
        } else if ($account_info['Role'] == ACCOUNT_ROLE_PERSONNAL_MAIN) {
            array_push($room_names, $account_info['Name']);
        } else if ($account_info['Role'] == ACCOUNT_ROLE_PERSONNAL_ATTENDANT){
            $mainAccount = $medooDb->get("PersonalAccount", ["Name","Role","RoomNumber"], ["UUID" => $account_info['ParentUUID']]);
            if ($mainAccount['RoomNumber']) {
                array_push($room_names, $account_info['RoomNumber']);
            } else {
                array_push($room_names, $account_info['Name']);
            }
        }
    }

	//如果名称本身包含","会有异常，当前因为模板就是通过，作为分隔符展示所以不做修改
    $str = implode(",", $room_names);
    return $str;
}

function getPayerEmailInfoByUUID($uuid, $payer_type, &$email_userinfo)
{
    global $medooDb;

    if ($payer_type == AUTO_PAY_ORDER_PAYER_TYPE_PM) {
        $email_userinfo['user'] = "PropertyManager";
/*
        $sth = $db->prepare("select A.Email,C.Language from AccountUserInfo A left join AccountMap B on A.UUID = B.UserInfoUUID
        left join Account C on C.UUID = B.AccountUUID where C.UUID = :uuid");
        $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
        $sth->execute();
        $ret = $sth->fetch(PDO::FETCH_ASSOC);
*/
        $accounUserInfo = $medooDb->get("AccountUserInfo",
            [ 
                "[>]AccountMap" => ["AccountUserInfo.UUID" => "UserInfoUUID"],
                "[>]Account" => ["AccountMap.AccountUUID" => "UUID"]
            ],[
                "AccountUserInfo.Email",
                "Account.Language"
            ],[ 
                "Account.UUID" => $uuid
            ]
        );

        $email_userinfo['email'] = $accounUserInfo['Email'];
        $email_userinfo['language'] = $accounUserInfo['Language'];
    } else if ($payer_type == AUTO_PAY_ORDER_PAYER_TYPE_DIS || $payer_type == AUTO_PAY_ORDER_PAYER_TYPE_SUB_DIS || $payer_type == AUTO_PAY_ORDER_PAYER_TYPE_INS){
        $accountInfo = $medooDb->get("Account", ["Account", "Language"], ["UUID" => $uuid]);
        $email_userinfo['user'] = $accountInfo['Account'];
        $email_userinfo['language'] = $accountInfo['Language'];
        $email_userinfo['email'] = getDisEmailByDisAccount($accountInfo['Account']);
    }
}
