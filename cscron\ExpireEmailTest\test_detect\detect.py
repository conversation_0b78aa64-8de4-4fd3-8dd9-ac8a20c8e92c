# coding=utf-8

import time
import socket
import threading
import subprocess
from common.db import GetDbConn
from common.handle_ini import *
from common.handle_json import *
from common.define import *
import common.globals
from test_detect.log import *
from test_detect.dbhandle import *
from datetime import datetime, timed<PERSON><PERSON>

def checkExpectResult(one_test, env_ini, log_dir):
    # 将参数格式化成字符串
    test_params_str = ""
    for key, value in one_test.items():
        if isinstance(value, int) or isinstance(value, float) or isinstance(value, str):
            test_params_str += f'{key}={value}, '

    test_result = one_test["TestResult"]
    for one_result in test_result:
        for k, v in one_result.items():
            if isinstance(v, str) and "$" in v:
                new_v = v.replace("$", "")
                if new_v in env_ini:
                    one_result[k] = env_ini[new_v]
        
        if check_log(log_dir, one_result):
            print('Test OK  : ' + test_params_str + 'one_result=' + json.dumps(one_result))
        else:
            print('Test FAIL: ' + test_params_str + 'one_result=' + json.dumps(one_result))

def run_autopay_test_case(env_ini):
    log_dir = "CSCRON_LOG_DIR"
    cmd = "php /usr/local/akcs/cscron/checkexpire/check_autopay_expire.php"
    test_list = get_test_list("./test_cases/autopay/")
    
    for one_test in test_list:
        if one_test['TestName'] == "CommAppAutoPay":
            env_ini['ORDER_UUID'] = env_ini['COMM_USER_ORDER_UUID']
        elif one_test['TestName'] == "SingleAutoPay":
            env_ini['ORDER_UUID'] = env_ini['SINGLE_USER_ORDER_UUID']
        elif one_test['TestName'] == "OfficeAutoPay":
            env_ini['ORDER_UUID'] = env_ini['OFFICE_USER_ORDER_UUID']
        elif one_test['TestName'] == "PMAppAutoPay":
            env_ini['ORDER_UUID'] = env_ini['COMM_PM_ORDER_UUID']
            
        if one_test['PayMode'] == "DIS":
            env_ini['PayerUUID'] = env_ini['DIS_UUID']
            env_ini['PayerType'] = AutoPayerType.DIS
        elif one_test['PayMode'] == "INS":
            env_ini['PayerUUID'] = env_ini['INS_UUID']
            env_ini['PayerType'] = AutoPayerType.INS
        elif one_test['PayMode'] == "PM":
            env_ini['PayerUUID'] = env_ini['PM_UUID']
            env_ini['PayerType'] = AutoPayerType.PM
            
        #设置订单状态为正常订阅状态
        setUserAutoPayOrderActive(env_ini)
        #设置订单即将收费时间
        setUserAutoPayChargeDays(env_ini, one_test["ExpireDays"])
        #设置订单付款人信息
        setUserAutoPayPayerInfo(env_ini)
        
        clean_local_logs(common.globals.g_server_env[log_dir])
        
        #等待子进程执行完毕再进行日志检查
        process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        process.wait()

        checkExpectResult(one_test, env_ini, common.globals.g_server_env[log_dir])
        
def run_test_case(env_ini, expire_type, test_dir, project_type):
    log_dir = "CSCRON_LOG_DIR"

    if project_type == ProjectType.Community:
        env_ini['PROJECT_UUID'] = env_ini['COMM_PROJECT_UUID']
        env_ini['NODE_UUID'] = env_ini['COMM_NODE_UUID']
        env_ini['USER_EMAIL'] = env_ini['COMM_USER_EMAIL']
        env_ini['USER_NAME'] = env_ini['COMM_USER_NAME']
        cmd = "php /usr/local/akcs/cscron/checkexpire/check_resident_app_expire.php"
    elif project_type == ProjectType.Office:
        env_ini['PROJECT_UUID'] = env_ini['OFFICE_PROJECT_UUID']
        env_ini['NODE_UUID'] = env_ini['OFFICE_NODE_UUID']
        env_ini['USER_EMAIL'] = env_ini['OFFICE_USER_EMAIL']
        env_ini['USER_NAME'] = env_ini['OFFICE_USER_NAME']
        cmd = "php /usr/local/akcs/cscron/checkexpire/check_office_expire.php"
    elif project_type == ProjectType.Single:
        env_ini['PROJECT_UUID'] = env_ini['SINGLE_PROJECT_UUID']
        env_ini['NODE_UUID'] = env_ini['SINGLE_NODE_UUID']
        env_ini['USER_EMAIL'] = env_ini['SINGLE_USER_EMAIL']
        env_ini['USER_NAME'] = env_ini['SINGLE_USER_NAME']
        cmd = "php /usr/local/akcs/cscron/checkexpire/check_single_tenant_feature_plan_expire.php"
    elif project_type == ProjectType.PMApp:
        env_ini['PROJECT_UUID'] = env_ini['COMM_PROJECT_UUID']
        env_ini['NODE_UUID'] = env_ini['COMM_PM_APP_UUID']
        cmd = "php /usr/local/akcs/cscron/checkexpire/check_pm_app_expire.php"
    
    test_list = get_test_list(test_dir)
    for one_test in test_list:
        setChargeMode(env_ini, one_test)

        if expire_type == TestingType.Landline:
            setUserLandlineExpireDays(env_ini, one_test["ExpireDays"])
        elif expire_type == TestingType.Feature:
            setOfficeFeatureExpireDays(env_ini, one_test["ExpireDays"])
        elif expire_type == TestingType.AppExpire:
            setUserAppExpireDays(env_ini, one_test["ExpireDays"])

        clean_local_logs(common.globals.g_server_env[log_dir])
    
        #等待子进程执行完毕再进行日志检查
        process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        process.wait()

        checkExpectResult(one_test, env_ini, common.globals.g_server_env[log_dir])

def run_test_video_record(env_ini, expire_type, test_dir, project_type):
    log_dir = "CSCRON_LOG_DIR"
    cmd = "php /usr/local/akcs/cscron/checkexpire/check_video_record_expire.php"
    
    if project_type == ProjectType.Community:
        env_ini['PROJECT_UUID'] = env_ini['COMM_PROJECT_UUID']
        env_ini['NODE_UUID'] = env_ini['COMM_NODE_UUID']
        env_ini['USER_EMAIL'] = env_ini['COMM_USER_EMAIL']
        env_ini['USER_NAME'] = env_ini['COMM_USER_NAME']
    elif project_type == ProjectType.Single:
        env_ini['PROJECT_UUID'] = env_ini['SINGLE_PROJECT_UUID']
        env_ini['NODE_UUID'] = env_ini['SINGLE_NODE_UUID']
        env_ini['USER_EMAIL'] = env_ini['SINGLE_USER_EMAIL']
        env_ini['USER_NAME'] = env_ini['SINGLE_USER_NAME']
    
    test_list = get_test_list(test_dir)
    for one_test in test_list:
        setChargeMode(env_ini, one_test)

        if project_type == ProjectType.Community:
            setVideoRecordExpireDaysByProjectUUID(env_ini, one_test["ExpireDays"])
        elif project_type == ProjectType.Single:
            setVideoRecordExpireDaysByPersonalUUID(env_ini, one_test["ExpireDays"])

        clean_local_logs(common.globals.g_server_env[log_dir])
    
        #等待子进程执行完毕再进行日志检查
        process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        process.wait()

        checkExpectResult(one_test, env_ini, common.globals.g_server_env[log_dir])

def run_test_rent(env_ini, expire_type, test_dir, project_type):
    log_dir = "CSCRON_LOG_DIR"
    cmd = "php /usr/local/akcs/cscron/checkexpire/rent_manager/check_rent_expire.php"
    
    if project_type == ProjectType.Community:
        env_ini['NODE_UUID'] = env_ini['COMM_NODE_UUID']
        env_ini['USER_NAME'] = env_ini['COMM_USER_NAME']
        env_ini['USER_EMAIL'] = env_ini['COMM_USER_EMAIL']
        env_ini['PROJECT_UUID'] = env_ini['COMM_PROJECT_UUID']
    
    test_list = get_test_list(test_dir)
    for one_test in test_list:
        setChargeMode(env_ini, one_test)

        # RentManager设置成过期
        setRentMangerExpireDaysByPMUUID(env_ini, one_test["ExpireDays"])

        clean_local_logs(common.globals.g_server_env[log_dir])
    
        #等待子进程执行完毕再进行日志检查
        process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        process.wait()

        checkExpectResult(one_test, env_ini, common.globals.g_server_env[log_dir])

def run_test_device_offline(env_ini, expire_type, test_dir, project_type):
    log_dir = "CSCRON_LOG_DIR"

    if project_type == ProjectType.Community:
        env_ini['PROJECT_UUID'] = env_ini['COMM_PROJECT_UUID']
        env_ini['NODE_UUID'] = env_ini['COMM_NODE_UUID']
        env_ini['USER_EMAIL'] = env_ini['COMM_USER_EMAIL']
        env_ini['USER_NAME'] = env_ini['COMM_USER_NAME']
        cmd = "php /usr/local/akcs/cscron/checkoffline/check_community_dev_offline.php"
    elif project_type == ProjectType.Office:
        env_ini['PROJECT_UUID'] = env_ini['OFFICE_PROJECT_UUID']
        env_ini['NODE_UUID'] = env_ini['OFFICE_NODE_UUID']
        env_ini['USER_EMAIL'] = env_ini['OFFICE_USER_EMAIL']
        env_ini['USER_NAME'] = env_ini['OFFICE_USER_NAME']
        cmd = "php /usr/local/akcs/cscron/checkoffline/check_office_dev_offline.php"
    
    test_list = get_test_list(test_dir)
    for one_test in test_list:
        # 设置上一次通知时间
        notify_time = datetime.now() - timedelta(minutes=60)
        notify_time_str = notify_time.strftime('%Y-%m-%d  %H:%M:%S')

        # 设置设备离线时间
        delta_minute = one_test['DisconnectMinute'] # 设备已经离线的分钟数
        offline_time = datetime.now() - timedelta(minutes=delta_minute)
        offline_time_str = offline_time.strftime('%Y-%m-%d  %H:%M:%S')

        if project_type == ProjectType.Community:
            updateCommunityInfoSetLastDevOfflineNotifyTime(env_ini['PROJECT_UUID'], notify_time_str)
            setDeviceOfflineTime(env_ini['PROJECT_UUID'],env_ini['COMM_DEV_OFFLINE_MAC'], offline_time_str)
        elif project_type == ProjectType.Office:
            updateOfficeInfoSetLastDevOfflineNotifyTime(env_ini['PROJECT_UUID'], notify_time_str)
            setDeviceOfflineTime(env_ini['PROJECT_UUID'],env_ini['OFFICE_DEV_OFFLINE_MAC'], offline_time_str)
            
        # 先清理日志，等待子进程执行完毕再进行日志检查
        clean_local_logs(common.globals.g_server_env[log_dir])
        process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        process.wait()

        checkExpectResult(one_test, env_ini, common.globals.g_server_env[log_dir])

def testSingle(env_ini):
    run_test_case(env_ini, TestingType.Landline, "./test_cases/single/", ProjectType.Single)
            
def testCommUser(env_ini):
    run_test_case(env_ini, TestingType.AppExpire, "./test_cases/comm/", ProjectType.Community)
        
def testPMApp(env_ini):
    run_test_case(env_ini, TestingType.AppExpire, "./test_cases/pm_app/", ProjectType.PMApp)
        
def testOfficeUser(env_ini):
    run_test_case(env_ini, TestingType.AppExpire, "./test_cases/office/", ProjectType.Office)
    
def testOfficeFeature(env_ini):
    run_test_case(env_ini, TestingType.Feature, "./test_cases/office-feature/", ProjectType.Office)
    
def testAutoPay(env_ini):
    run_autopay_test_case(env_ini)

# 视频存储过期检测
def testVideoRecord(env_ini):
    run_test_video_record(env_ini, TestingType.VideoRecord, "./test_cases/single-videorecord/", ProjectType.Single)
    run_test_video_record(env_ini, TestingType.VideoRecord, "./test_cases/comm-videorecord/", ProjectType.Community)

# RentManager过期检测
def testRentManager(env_ini):
    run_test_rent(env_ini, TestingType.RentManager, "./test_cases/rentmanager/", ProjectType.Community)

# 设备离线通知
def testDeviceOffline(env_ini):
    run_test_device_offline(env_ini, TestingType.DeviceOffline, "./test_cases/office-deviceoffline/", ProjectType.Office)
    run_test_device_offline(env_ini, TestingType.DeviceOffline, "./test_cases/comm-deviceoffline/", ProjectType.Community)
