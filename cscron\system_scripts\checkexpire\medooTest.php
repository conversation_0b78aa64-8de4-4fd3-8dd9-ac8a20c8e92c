<?php
require_once(dirname(__FILE__).'/medoo.php');
require_once(dirname(__FILE__).'/data_confusion.php');

function getMedooDb() {
    $database = new Medoo([
        // 必须的
        'database_type' => 'mysql',
        'database_name' => 'AKCS',
        'server' => "127.0.0.1",
        'username' => 'root',
        'password' => "Ak@56@<EMAIL>",
        'charset' => 'utf8',
        'port' => 3306,
    ]);
    return $database;
}

$medooDb = getMedooDb();
$db = $medooDb->pdo;

function getPersonalEmailByUUID($db, $medooDb)
{
    $userInfoUUID = "na-0693129fa29c611ee9ddac6b0aa61e010";
    $sth = $db->prepare("select Email from PersonalAccountUserInfo where UUID=:uuid");
    $sth->bindParam(':uuid', $userInfoUUID, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);

    $data = $medooDb->get("PersonalAccountUserInfo", ["Email"], ["UUID" => $userInfoUUID])["Email"];
}

// ----------------------
function GetFeaturePlanExpirePersonalAccountInfo($db, $medooDb)
{
    $before = 3;
    $sth = $db->prepare("select Account,Name,UserInfoUUID,ID,ParentID,Switch,Language,ParentUUID,UUID from PersonalAccount where (TO_DAYS(PhoneExpireTime) = TO_DAYS(NOW()) + :before) and Role = 10 and PhoneStatus = 1");
    $sth->bindParam(':before', $before , PDO::PARAM_INT);
    $sth->execute();
    return $sth->fetchALL(PDO::FETCH_ASSOC);

    $before = 3;
    return $medooDb->select("PersonalAccount", 
                            ["ID","Account","Name","UserInfoUUID","ParentID","Switch","Language","ParentUUID","UUID"] , 
                            Medoo::raw("where TO_DAYS(PhoneExpireTime) = TO_DAYS(NOW()) + :before and Role = 10 and PhoneStatus = 1", [':before' => $before]));
}

// ----------------------

function getPersonalUserInfoByUUID($medooDb, $userInfoUUID)
{
    return $medooDb->get("PersonalAccountUserInfo", ["Email","MobileNumber"], ["UUID" => $userInfoUUID]);
}

// ----------------------

function testGetExpirePersonalAccountInfo($db, $medooDb)
{
    $before = 3;
    $ret = $medooDb->select("PersonalAccount", "*",  Medoo::raw("where TO_DAYS(ExpireTime) = TO_DAYS(NOW()) + :before and Role = 20 and Active = 1 and Special = 0", [':before' => $before]));

    foreach ($ret as $key => $value)  {
        echo "name = {$value["Name"]} \n";
    }
}

// ----------------------
function testGetLandlineWillExpireEmailStr($db, $medooDb)
{
    $before = 3;
    $account_uuid = "na-5ef20acc754611edb363c6b0aa61e010";

    $sth = $db->prepare("select UserInfoUUID,Switch,Name,UUID from PersonalAccount where ParentUUID = :UUID and (TO_DAYS(PhoneExpireTime) = TO_DAYS(NOW()) + :before) and (Role = 10) and PhoneStatus = 1");
    $sth->bindParam(':UUID', $account_uuid, PDO::PARAM_STR);
    $sth->bindParam(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $enduser_list = $sth->fetchALL(PDO::FETCH_ASSOC);

    foreach ($enduser_list as $key => $value) {
        echo "UUID = {$value["UUID"]} , name = {$value["Name"]}\n";
    }
    
    echo "-------------------------------------------------------------\n";

    $ret = $medooDb->select("PersonalAccount", ["UserInfoUUID","Switch","Name","UUID"], 
                            Medoo::raw("where ParentUUID = :parentUUID and (TO_DAYS(PhoneExpireTime) = TO_DAYS(NOW()) + :before) and (Role = 10) and PhoneStatus = 1", [':before' => $before, ':parentUUID' => $account_uuid]));
    foreach ($ret as $key => $value) {
        echo "UUID = {$value["UUID"]} , name = {$value["Name"]}\n";
    }
}

function testGetInstallerEmailInfoByInsID($db, $medooDb)
{
    $insID = 21;
    $sth = $db->prepare("select I.Account,I.Email,A.UUID,A.Language from InstallerBillingInfo I left join Account A on I.Account = A.Account where A.ID = :insID");
    //查账单中的installer邮箱
    $sth->bindParam(':insID', $insID, PDO::PARAM_INT);
    $sth->execute();
    $account = $sth->fetch(PDO::FETCH_ASSOC);

    echo "account = {$account["Account"]}, email = {$account["Email"]} \n";

    echo "-------------------------------------------------------------\n";

    $data = $medooDb->get("InstallerBillingInfo", [
        "[>]Account" => ["InstallerBillingInfo.Account" => "Account"]
    ], [
        "InstallerBillingInfo.Account",
        "InstallerBillingInfo.Email",
        "Account.UUID",
        "Account.Language"
    ], [
        "Account.ID" => $insID
    ]);

    echo "account = {$data["Account"]}, email = {$data["Email"]} \n";
}

// getDisEmailByDisAccount
function testGetDisEmailByDisAccount($db, $medooDb)
{
    $account = "czx-c";
    $sth = $db->prepare("select Email from InstallerBillingInfo where Account = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);

    echo "email = {$result["Email"]} \n";

    echo "-------------------------------------------------------------\n";

    $data = $medooDb->get("InstallerBillingInfo", "Email", ["Account" => $account]);
    echo "email = $data \n";
}

// getCommExpirePmAppList
function testGetCommExpirePmAppList($db, $medooDb)
{
    $comm_uuid = "na-ff346a95754911edb363c6b0aa61e010";
    $before = 3;

    $sth = $db->prepare("select Name,UUID from PersonalAccount  where ParentUUID = :comm_uuid and (TO_DAYS(ExpireTime) = TO_DAYS(NOW()) + :before) and Role = 40 and Active = 1");
    $sth->bindParam(':comm_uuid', $comm_uuid, PDO::PARAM_STR);
    $sth->bindParam(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $expire_pm_list = $sth->fetchALL(PDO::FETCH_ASSOC);

    foreach ($expire_pm_list as $key => $value) {
        echo "name = {$value["Name"]} \n";
    }

    echo "-------------------------------------------------------------\n";

    $pmList = $medooDb->select("PersonalAccount", ["Name", "UUID"], 
                    $medooDb->raw("where ParentUUID = :comm_uuid and (TO_DAYS(ExpireTime) = TO_DAYS(NOW()) + :before) and Role = 40 and Active = 1",  [':before' => $before, ':comm_uuid' => $comm_uuid]));

    foreach ($pmList as $key => $value) {
        echo "name = {$value["Name"]} \n";
    }
}

// getOfficeAppExpireList
function testGetOfficeAppExpireList($db, $medooDb)
{
    $officeUUID = "na-1077c9d52d2711ee9ddac6b0aa61e010";

    $sth = $db->prepare("select P.Name, U.UnitName, P.Account, O.EmployeeID, P.UUID 
    from PersonalAccount P 
    left join CommunityUnit U on P.UnitID = U.ID
    left join PersonalAccountOfficeInfo O on O.PersonalAccountUUID = P.UUID
    where P.ParentUUID = :officeUUID and P.Role in (30,31) and (TO_DAYS(ExpireTime) =  TO_DAYS(NOW()) - 1) and P.Active = 1");
    $sth->bindParam(':officeUUID', $officeUUID, PDO::PARAM_STR);
    $sth->execute();
    $expire_enduser_list = $sth->fetchALL(PDO::FETCH_ASSOC);

    foreach ($expire_enduser_list as $key => $value) {
        echo "name = {$value["Name"]} \n";
    }

    echo "-------------------------------------------------------------\n";

    $expireEnduserList = $medooDb->select("PersonalAccount", [
        "[>]CommunityUnit" => ["PersonalAccount.UnitID" => "ID"],
        "[>]PersonalAccountOfficeInfo" => ["PersonalAccount.UUID" => "PersonalAccountUUID"]
    ], [
        "PersonalAccount.Name",
        "CommunityUnit.UnitName",
        "PersonalAccount.Account",
        "PersonalAccountOfficeInfo.EmployeeID",
        "PersonalAccount.UUID"
    ], 
       Medoo::raw("where PersonalAccount.ParentUUID = :officeUUID and PersonalAccount.Role in (30, 31) and PersonalAccount.Active = 1 and (TO_DAYS(PersonalAccount.ExpireTime) = TO_DAYS(NOW()) - 1)", [':officeUUID' => $officeUUID])
    );
    
    foreach ($expireEnduserList as $key => $value) {
        echo "name = {$value["Name"]} \n";
    }
}

// getOfficeAppWillExpireList
function testGetOfficeAppWillExpireList($db, $medooDb)
{
    $before = 3;
    $officeUUID = "na-1077c9d52d2711ee9ddac6b0aa61e010";

    $sth = $db->prepare("select P.Name, U.UnitName, P.Account, O.EmployeeID, P.UUID 
    from PersonalAccount P 
    left join CommunityUnit U on P.UnitID = U.ID
    left join PersonalAccountOfficeInfo O on O.PersonalAccountUUID = P.UUID
    where P.ParentUUID = :officeUUID and P.Role in (30,31) and (TO_DAYS(ExpireTime) = TO_DAYS(NOW()) + :before) and P.Active = 1");
    $sth->bindParam(':officeUUID', $officeUUID, PDO::PARAM_STR);
    $sth->bindParam(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $expire_enduser_list = $sth->fetchALL(PDO::FETCH_ASSOC);

    foreach ($expire_enduser_list as $key => $value) {
        echo "name = {$value["Name"]} \n";
    }

    echo "-------------------------------------------------------------\n";

    $condition = $medooDb->raw("where PersonalAccount.ParentUUID = :officeUUID and PersonalAccount.Role in (30,31) and (TO_DAYS(PersonalAccount.ExpireTime) = TO_DAYS(NOW()) + :before) and PersonalAccount.Active = 1",
                                 [':officeUUID' => $officeUUID, ':before' => $before]);

    $expireEnduserList = $medooDb->select("PersonalAccount", [
        "[>]CommunityUnit" => ["PersonalAccount.UnitID" => "ID"],
        "[>]PersonalAccountOfficeInfo" => ["PersonalAccount.UUID" => "PersonalAccountUUID"]
    ], [
        "PersonalAccount.Name",
        "CommunityUnit.UnitName",
        "PersonalAccount.Account",
        "PersonalAccountOfficeInfo.EmployeeID",
        "PersonalAccount.UUID"
    ], $condition );

    foreach ($expireEnduserList as $key => $value) {
        echo "name = {$value["Name"]} \n";
    }
}

// getWillChargeOrderList
function testGetWillChargeOrderList($db, $medooDb)
{
    $before = 3;
    $sth = $db->prepare("select Status,EndReason,TotalPrice,Type,PayerUUID,PayerEmail,PayerType,ProjectUUID,IsBatch,PayPlatform,NextPayTime,LastPayTime,UUID,TimeZone from SubscriptionList
    where (TO_DAYS(NextPayTime) = TO_DAYS(NOW()) + :before) and Status = 1");
    $sth->bindParam(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $order_list = $sth->fetchALL(PDO::FETCH_ASSOC);

    foreach ($order_list as $key => $value) {
        echo "PayerEmail = {$value["PayerEmail"]} \n";
    }
    echo "-------------------------------------------------------------\n";

    $orderList = $medooDb->select("SubscriptionList", "*", 
                $medooDb->raw(" where (TO_DAYS(NextPayTime) = TO_DAYS(NOW()) + :before) and Status = 1",[':before' => $before]));

    foreach ($orderList as $key => $value) {
        echo "PayerEmail = {$value["PayerEmail"]} \n";
    }
}

// getSingleAutoPayUsers
function testGetSingleAutoPayUsers($db, $medooDb)
{
    $order_uuid = "dv-2177ac7faad211ee8d7500163e047e78";
    $sth = $db->prepare("select A.Name,A.Role,A.RoomNumber,A.ParentUUID from PersonalAccount A left join SubscriptionEndUserList B on A.UUID = B.PersonalAccountUUID where B.SubscriptionUUID = :uuid");
    $sth->bindParam(':uuid', $order_uuid, PDO::PARAM_STR);
    $sth->execute();
    $personal_account_infos = $sth->fetchAll(PDO::FETCH_ASSOC);

    foreach ($personal_account_infos as $key => $value) {
        echo "Name = {$value["Name"]} \n";
    }

    echo "-------------------------------------------------------------\n";

    $personalAccountList = $medooDb->select("PersonalAccount", [
        "[>]SubscriptionEndUserList" => ["PersonalAccount.UUID" => "PersonalAccountUUID"]
    ], [
        "PersonalAccount.Name",
        "PersonalAccount.Role",
        "PersonalAccount.RoomNumber",
        "PersonalAccount.ParentUUID",
        "SubscriptionEndUserList.SubscriptionUUID"
    ], [
        "SubscriptionEndUserList.SubscriptionUUID" => $order_uuid
    ]);

    foreach ($personalAccountList as $key => $value) {
        echo "Name = {$value["Name"]} \n";
    }

    echo "-------------------------------------------------------------\n";
    $uuid = "na-224b090f754a11edb363c6b0aa61e010";
    $sth = $db->prepare("select Name,Role,RoomNumber from PersonalAccount where UUID = :uuid");
    $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    $sth->execute();
    $main_account = $sth->fetch(PDO::FETCH_ASSOC);
    echo "Name = {$main_account["Name"]} \n";  

    echo "-------------------------------------------------------------\n";
    $mainAccount = $medooDb->get("PersonalAccount", ["Name","Role","RoomNumber"], ["UUID" => $uuid]);

    echo "Name = {$mainAccount["Name"]} \n";  
}

// getPayerEmailInfoByUUID
function testGetPayerEmailInfoByUUID($db, $medooDb)
{
    $uuid = "na-9173378e75cc11edb363c6b0aa61e010";
    $sth = $db->prepare("select A.Email,C.Language from AccountUserInfo A left join AccountMap B on A.UUID = B.UserInfoUUID left join Account C on C.UUID = B.AccountUUID where C.UUID = :uuid");
    $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);
    echo "email = {$ret["Email"]} \n";

    echo "-------------------------------------------------------------\n";

    $accounUserInfo = $medooDb->get("AccountUserInfo",
        [ 
            "[>]AccountMap" => ["AccountUserInfo.UUID" => "UserInfoUUID"],
            "[>]Account" => ["AccountMap.AccountUUID" => "UUID"]
        ],[
            "AccountUserInfo.Email",
            "Account.Language"
        ],[ 
            "Account.UUID" => $uuid
        ]
    );

    echo "email = {$accounUserInfo["Email"]} \n";
}


function testGetPMAccountEmailByAccountUUID($db, $medooDb)
{
    $uuid = "na-9173378e75cc11edb363c6b0aa61e010";

    $sth = $db->prepare("select A.Email from AccountUserInfo A join AccountMap B on A.UUID = B.UserInfoUUID where B.AccountUUID=:uuid");
    $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);

    echo "email = {$result["Email"]} \n";
    echo "-------------------------------------------------------------\n";

    $ret = $medooDb->get("AccountUserInfo",
        [
            "[>]AccountMap" => ["AccountUserInfo.UUID" => "UserInfoUUID"]
        ],[
            "AccountUserInfo.Email"
        ],[
           "AccountMap.AccountUUID" => $uuid
        ]);
        
    echo "email = {$ret['Email']} \n";
}

function test()
{
    global $db;
    global $medooDb;

    //testGetPersonalEmailByUUID($db, $medooDb);
    //testGetPersonalUserInfoByUUID($db, $medooDb);
    //testGetExpirePersonalAccountInfo($db, $medooDb);
    //testGetLandlineWillExpireEmailStr($db, $medooDb);
    //testGetInstallerEmailInfoByInsID($db, $medooDb);
    //testGetDisEmailByDisAccount($db, $medooDb);
    //testGetCommExpirePmAppList($db, $medooDb);
    testGetOfficeAppExpireList($db, $medooDb);
    //testGetOfficeAppWillExpireList($db, $medooDb);
    //testGetWillChargeOrderList($db, $medooDb);
    //testGetSingleAutoPayUsers($db, $medooDb);
    //testGetPayerEmailInfoByUUID($db, $medooDb);
    //testGetPMAccountEmailByAccountUUID($db, $medooDb);
}

test();