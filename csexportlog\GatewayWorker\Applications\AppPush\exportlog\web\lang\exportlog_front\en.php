<?php

  global $MSGTEXT_FRONT;

  $MSGTEXT_FRONT = [ 
"TabelUpdateBoxDevice"=>"Device",
"CaptureListTanleAction"=>"Action",
"MulListUpdateListTanleStatus"=>"Status",
"TmpKeyListTanleKey"=>"Key ",
"CaptureListTanleCaptureTime"=>"Happened On",
"CaptureListTanleInitiator"=>"Initiated By",
"LogInHtmlType"=>"Log Type",
"Response"=>"Response",
"RecodeListTanleCallerName"=>"Caller",
"TabelMessageBoxReceiver"=>"Receiver",
"RecodeListTanleCallTime"=>"Call Time",
"ProperAllTextDoorLogs"=>"Door Logs",
"TabelUserInHtmlCompany"=>"Company",
"ProperAllTextTemperatureLogs"=>"Temperature Logs",
"ProperAllTextBodyTemperature"=>"Body Temperature",
"ProperAllTextLow"=>"Low",
"ProperAllTextAbnormal"=>"Abnormal",
"DeviceLocation"=>"Device Location",
"DeviceDetailDetailUnitName"=>"Building",
"ProperAllTextDepartment"=>"Department",
"SelectDoor"=>"Door",
"capture"=>"Capture",
"DeliverySystemVerification"=>"Delivery System Verification",
"RDeviceSearchLabelRoomName"=>"APT",
"NavInHtmlMenuCall"=>"Call History",

];