<?php

class VideoStorageQuery 
{
    private $db;
    private $medooDb;

    const VIDEO_STORE_PROJECT_PERSONAL  = 1;
    const VIDEO_STORE_PROJECT_RESIDENCE = 2;
    const VIDEO_STORE_PROJECT_OFFICE    = 3;
    const ACCOUNT_GRADE_INSTALLER       = 22;

    const VIDEO_STORE_BELONG_TYPE_COMMUNITY = 1;
    const VIDEO_STORE_BELONG_TYPE_APARTMENT = 2;

    public function __construct($db, $medooDb) {
        $this->db = $db;
        $this->medooDb = $medooDb;
    }

    public function getExpireData($daysBefore)
    {
        $sth = $this->db->prepare("select AAA.UUID as DisUUID, AAA.Account as DisAccount, AA.UUID as InsUUID, AA.Account as InsAccount, 
                                A.UUID AS CommunityUUID, A.Location AS Community, A.ID as CommunityID, <PERSON><PERSON>TimeZone,
                                U.UnitName as Building, <PERSON><PERSON> as Apt, P<PERSON>UUI<PERSON> as PersonalAccountUUID,
                                <PERSON><PERSON>Room<PERSON>umber as AptName, S.DistributorUUID AS SubDisUUID, V.ExpireTime
                            from VideoStorage V 
                            left join Account A on A.UUID = V.AccountUUID 
                            left join Account AA on AA.ManageGroup = A.ManageGroup
                            left join Account AAA on AA.ParentUUID = AAA.UUID
                            left join SubDisMngList S on S.InstallerUUID = V.InstallerUUID 
                            left join PersonalAccount P on P.UUID = V.PersonalAccountUUID
                            left join CommunityRoom R on R.UUID = P.CommunityRoomUUID
                            left join CommunityUnit U on U.UUID = R.CommunityUnitUUID
                            where V.IsEnable = 1 
                            and (V.ExpireTime > CURDATE() + INTERVAL :daysBefore DAY) 
                            and (V.ExpireTime <= CURDATE() + INTERVAL (:daysBefore + 1) DAY) 
                            and V.ProjectType=:projectType and AA.Grade = :insAccountGrade");
        $sth->bindValue(':projectType', self::VIDEO_STORE_PROJECT_RESIDENCE, PDO::PARAM_INT);
        $sth->bindValue(':daysBefore', $daysBefore, PDO::PARAM_INT);
        $sth->bindValue(':insAccountGrade', self::ACCOUNT_GRADE_INSTALLER, PDO::PARAM_INT);
        $sth->execute();
        $expireList = $sth->fetchALL(PDO::FETCH_ASSOC);
        return $expireList;
    }

    public function IsCommunityEnableVideoStorageAutoPay($communityUUID, $autopayType)
    {
        //用户可能有历史订单，取用户最新的订单
        $sth = $this->db->prepare("select A.Status from SubscriptionList A left join SubscriptionEndUserList B on A.UUID = B.SubscriptionUUID
        where B.SiteUUID = :uuid and B.Type = :auto_pay_type order by A.ID desc limit 1");
        $sth->bindParam(':uuid', $communityUUID, PDO::PARAM_STR);
        $sth->bindParam(':auto_pay_type', $autopayType, PDO::PARAM_INT);
        $sth->execute();
        $result = $sth->fetch(PDO::FETCH_ASSOC);

        if ($result && $result['Status'] == 1)
        {
            LOG_INFO("CommunityUUID : $communityUUID Enable VideoStorage AutoPay");
            return true;
        }
        return false;
    }



    
}