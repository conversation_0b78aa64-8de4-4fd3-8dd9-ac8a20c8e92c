<?php
require_once __DIR__ . '/../../../common/email_common.php';

class DistributorHandler 
{
    private $daysBefore;
    private $expireDataMap;
    private $personalExpireInfoList;
    private $personalChargeModeMap;
    public $expireDataControl;

    public function __construct() {
    }
    
    public function setExpireParams($daysBefore, $expireDataMap, $personalExpireInfoList, $personalChargeModeMap, $expireDataControl) {
        $this->daysBefore = $daysBefore;
        $this->expireDataMap = $expireDataMap;
        $this->personalExpireInfoList = $personalExpireInfoList;
        $this->personalChargeModeMap = $personalChargeModeMap;
        $this->expireDataControl = $expireDataControl;
    }

    public function filterData() {
        // 判断社区的付费模式 是否要发送给Distributor
        $filteredExpireData = $this->filterPersonalChargeMode();
        return $filteredExpireData;
    }

    private function filterPersonalChargeMode()
    {
         // 判断单住户的付费模式 是否要发送给Distributor
         $payTypeList = array(
            EmailNotifyRule::PAY_ITEM_USER_APP,
            EmailNotifyRule::PAY_ITEM_FEATURE_PLAN,
            EmailNotifyRule::PAY_ITEM_VIDEO_RECORD,
            EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT,
        );

        // 获取每种付费类型发送给Dis的用户列表
        $payTypePersonalAccountUUIDMap = array(); 
        foreach ($payTypeList as $payType) {
            foreach ($this->personalExpireInfoList as $personalAccountInfo) {
                $personalChargeMode = $this->personalChargeModeMap[$personalAccountInfo['PersonalAccountUUID']];
                if ($personalChargeMode && PersonalCheck::needNotifyDis($payType, $personalChargeMode, $this->daysBefore)) {
                    $payTypePersonalAccountUUIDMap[$payType][] = $personalAccountInfo['PersonalAccountUUID'];

                }
            }
        }

        // 获取每种付费类型发送给Dis的用户列表
        $filteredExpireData = array();
        foreach ($this->expireDataMap as $payType => $expireData) {
            foreach ($expireData as $expireInfo) {
                // payTypePersonalAccountUUIDMap
                if (isset($payTypePersonalAccountUUIDMap[$payType]) && in_array($expireInfo['PersonalAccountUUID'], $payTypePersonalAccountUUIDMap[$payType])) {
                    $filteredExpireData[$payType][] = $expireInfo;
                }
            }
        }
        return $filteredExpireData;
    }

    // 发送邮件
    public function sendEmail($emailDataMap, $recevierInfoMap) {
        // 发给每个DIS
        foreach ($emailDataMap as $disUUID => $emailData) {
            $disEmail = $recevierInfoMap[$disUUID]['email'];
            if (empty($disEmail)) {
                LOG_INFO("disEmail is empty, skip, disUUID: " . $disUUID);
                continue;
            }

            $emailInfo['email'] = $disEmail;
            $emailInfo["web_ip"] = WEB_DOMAIN;
            $emailInfo['before'] = $this->daysBefore;
            $emailInfo['oem'] = $recevierInfoMap[$disUUID]['oem'];
            $emailInfo['user'] = $recevierInfoMap[$disUUID]['user'];
            $emailInfo['language'] = $recevierInfoMap[$disUUID]['language'];
            $emailInfo['is_show_paylink'] = SHOW_PAYLINK_DISTRIBUTOR;
            if ($this->daysBefore == -1) {
                $emailInfo['email_type'] = "single_family_has_expired";
            } else {
                $emailInfo['email_type'] = "single_family_will_expire";
            }

            $emailInfo['list'] = $emailData;
            sendEmailNotifyNew($emailInfo);

            $this->sendSubDisEmail($disUUID, $recevierInfoMap[$disUUID]['oem'], $emailData);
        }
    }

    // 发送邮件给subdis
    private function sendSubDisEmail($disUUID, $oem, $emailData)
    {
        // 获取dis下的所有subDis
        $subDisInfoList = $this->expireDataControl->commonQuery->getSubDisListInfoByDisUUID($disUUID);

        // 遍历subDis发送邮件
        foreach ($subDisInfoList as $row => $subDisInfo) {
            // 获取subDis的billingInfo
            $subDisBillingInfo = $this->expireDataControl->commonQuery->getBillingInfoByAccountUUID($subDisInfo['UUID']);
            if (!$subDisBillingInfo || empty($subDisBillingInfo['Email'])) {
                continue;
            }

            // 获取subDis的subDisMngList
            $subDisMngInstallerList = $this->expireDataControl->commonQuery->getSubDisMngInstallerUUIDList($subDisInfo['UUID']);
            if(!$subDisMngInstallerList) {
                continue;
            }
            $subDisMngInstallerUUIDList = array_column($subDisMngInstallerList, 'InsUUID');

            // 判断subDis是否管理data的Ins
            $subDisEmailDataMap = array();
            foreach ($emailData as $payType => $dataList) {
                LOG_INFO("-------------- data = " . json_encode($dataList));
                foreach ($dataList as $data) {
                    // subdis管理了数据的ins
                    if (in_array($data['InsUUID'], $subDisMngInstallerUUIDList)) {
                        $subDisEmailDataMap[$payType][] = $data;
                    }
                }
            }

            if (!$subDisEmailDataMap) {
                continue;
            }
            
            $emailInfo['oem'] = $oem;
            $emailInfo["web_ip"] = WEB_DOMAIN;
            $emailInfo['before'] = $this->daysBefore;
            $emailInfo['user'] = $subDisInfo['Account'];
            $emailInfo['language'] = $subDisInfo['Language'];
            $emailInfo['email'] = $subDisBillingInfo['Email'];
            $emailInfo['is_show_paylink'] = SHOW_PAYLINK_DISTRIBUTOR;

            if ($this->daysBefore == -1) {
                $emailInfo['email_type'] = "single_family_has_expired";
            } else {
                $emailInfo['email_type'] = "single_family_will_expire";
            }

            if (!EmailNotifyRule::IfHasPayPermission($subDisInfo['SubDisMode'])) {
                $email_info['is_show_paylink'] = SHOW_PAYLINK_NONE;
            }

            $emailInfo['list'] = $emailData;
            sendEmailNotifyNew($emailInfo);
        }
    }
}