<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: AK.CrontabOffice.proto

namespace GPBMetadata;

class AKCrontabOffice
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0a94010a16414b2e43726f6e7461624f66666963652e70726f746f121041" .
            "4b2e43726f6e7461624f666669636522600a0b504d417070457870697265" .
            "12110a09636f6d6d756e697479180120012809120d0a05656d61696c1802" .
            "20012809120c0a046e616d6518032001280912130a0b6163636f756e745f" .
            "6e756d180420012805120c0a046c697374180520012809620670726f746f" .
            "33"
        ));

        static::$is_initialized = true;
    }
}

