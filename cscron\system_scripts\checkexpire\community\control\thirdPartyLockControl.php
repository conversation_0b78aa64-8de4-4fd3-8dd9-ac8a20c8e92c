<?php
require_once(dirname(__FILE__) . '/expireDataCheckUtil.php');
require_once(dirname(__FILE__) . '/../model/thirdPartyLockQuery.php');

class ThirdPartyLockControl extends ExpireDataCheckUtil
{
    public $commonQuery;
    private $thirdPartyLockQuery;

    const DORMAKABA_LOCK_TYPE  = 3;
    const ITEC_LOCK_TYPE       = 6;
    const THIRD_PARTY_LOCK_IN_RESIDENCE_PUB    = 1;
    const THIRD_PARTY_LOCK_IN_RESIDENCE_APT    = 2;
    const SUBSCRIPTIONENDUSERLIST_COMMUNITY_THIRD_PARTY_LOCK_TYPE = 8;

    public function __construct($db, $medooDb, $commonQuery)
    {
        parent::__construct($db, $medooDb);
        $this->commonQuery = $commonQuery;
        $this->thirdPartyLockQuery = new ThirdPartyLockQuery($db, $medooDb);
    }

    public function getExpireData($daysBefore)
    {
        // 获取过期数据
        $expireData = $this->thirdPartyLockQuery->getExpireData($daysBefore);

        // 过滤掉迁移dis的社区
        $expireData = $this->filterMigrateDistributor($expireData);

        // 过滤掉自动扣费社区
        $expireData = $this->filterAutoPayLock($expireData);

        // 获取锁信息 
        $expireData = $this->getExpireLockInfo($expireData);

        return $expireData;
    }

    // 过滤掉自动扣费的锁
    private function filterAutoPayLock($expireData)
    {
        $filteredData = [];
        foreach ($expireData as $data) {
            if (!$this->commonQuery->IsUserEnableThirdPartyLockAutoPay($data['LockUUID'], self::SUBSCRIPTIONENDUSERLIST_COMMUNITY_THIRD_PARTY_LOCK_TYPE)) {
                $filteredData[] = $data;
            }
        }
        return $filteredData;
    }  

    // 构造邮件数据
    public function prepareEmailData($expireData) {
        if (empty($expireData)) {
            return [];
        }

        $emailDataList = [];
        foreach ($expireData as $data) {
            $emailData['Community'] = $data['Community'] ?? '';
            $emailData['Building'] = $data['Building'] ?? '';
            $emailData['Apt'] = $data['Apt'] ?? '';
            $emailData['LockName'] = $data['LockName'] ?? '';
            $emailDataList[] = $emailData;
        }
        return $emailDataList;
    }

    private function getExpireLockInfo($expireData)
    {
        if (empty($expireData) || !is_array($expireData)) {
            return [];
        }
        
        $result = [];
        foreach ($expireData as $index => $data) {
            $updatedData = $data;

            $lockInfo = [];
            if ($data['LockBrand'] == self::DORMAKABA_LOCK_TYPE) {
                $lockInfo = $this->thirdPartyLockQuery->getDormakabaLockInfo($data['LockUUID']);
            } elseif ($data['LockBrand'] == ITEC_LOCK_TYPE) {
                $lockInfo = $this->thirdPartyLockQuery->getItecLockInfo($data['LockUUID']);
            }   
            $updatedData['LockName'] = $lockInfo['LockName'] ?? '';
            
            // 根据锁的等级处理不同信息
            if ($lockInfo['Grade'] == self::THIRD_PARTY_LOCK_IN_RESIDENCE_PUB) {

            } else {
                $updatedData['Building'] = $this->commonQuery->getCommunityUnitName($data['CommunityUnitUUID']);
                if ($lockInfo['Grade'] == self::THIRD_PARTY_LOCK_IN_RESIDENCE_APT) {
                    $updatedData['Apt'] = $this->commonQuery->getCommunityAptNumber($data['PersonalAccountUUID']);
                }
            }
            $result[] = $updatedData;
        }
        return $result;
    }
}