<?php
date_default_timezone_set("PRC");

function Create_front($src, $dest)
{
    $fields = "\"NavInHtmlMenuCall\"|\"RecodeListTanleCallTime\"|\"TabelMessageBoxReceiver\"|\"RecodeListTanleCallerName\"|\"Response\"|\"TmpKeyListTanleKey\"|\"LogInHtmlType\"|\"capture:\"|\"CaptureListTanleAction\"|\"CaptureListTanleInitiator\"|\"TabelUpdateBoxDevice\"|\"CaptureListTanleCaptureTime\"|\"MulListUpdateListTanleStatus\"|\"RDeviceSearchLabelRoomName\"|\"DeviceDetailDetailUnitName\"|\"ProperAllTextDoorLogs\"|\"ProperAllTextTemperatureLogs\"|\"ProperAllTextBodyTemperature\"|\"ProperAllTextLow\"|\"ProperAllTextAbnormal\"|\"ProperAllTextDepartment\"|\"DeviceLocation\"|\"SelectDoor\"|\"TabelUserInHtmlCompany\"|\"DeliverySystemVerification\"";
    #$ret = shell_exec($cmd);
    $cmd = "grep -w -E \"$fields\" \"$src\"";
    $myfile = fopen($dest, "w") or die("Unable to open file! $dest");
    $head = "<?php\n
  global \$MSGTEXT_FRONT;\n
  \$MSGTEXT_FRONT = [ \n";
    fwrite($myfile, $head);

    $handle=popen($cmd, "r");
    if ($handle) {
        while (!feof($handle)) {
            $buffer = fgets($handle);
            $line = explode(":", $buffer, 2);
            if (count($line) == 2) {
                $txt = "\"".trim($line[0])."\"=>".$line[1];
                fwrite($myfile, $txt);
            }
        }
        fclose($handle);
    }

    fwrite($myfile, "\n];");
    fclose($myfile);
}

function Create_backend($src, $dest)
{
    $fields = "\"noAnswer\"|\"doorRelease\"|\"success\"|\"failed\"|\"abnormal\"|\"normal\"|\"low\"|\"call\"|\"unlockTempKey\"|\"unlockPrivateKey\"|\"unlockCard\"|\"unlockFACE\"|\"unlockApp\"|\"unlockApp\"|\"unlockIndoor\"|\"unlockNFC\"|\"unlockBLE\"|\"captureSmartPlus\"|\"unlockDelivery\"|\"unlockBooking\"|\"unlockIDAccess\"|\"entry\"|\"exit\"|\"entryViolation\"|\"exitViolation\"|\"auditCodeManuallyUnlock\"|\"auditCodeManuallyLock\"|\"automaticallyUnlock\"|\"unlockHandset\"|\"unlockLicensePlate\"|\"unlockExitButton\"";
    $cmd = "grep -w -E \"$fields\" \"$src\"";
    $ret = shell_exec($cmd);

    $myfile = fopen($dest, "w") or die("Unable to open file! $dest");

    $head = "<?php\n
  global \$MSGTEXT_BACK;\n
  \$MSGTEXT_BACK = [ \n";

    fwrite($myfile, $head);
    fwrite($myfile, $ret);
    fwrite($myfile, "\n];");
    fclose($myfile);
}

function searchDir($path, &$files)
{
    if (is_dir($path)) {
        $opendir = opendir($path);
 
        while ($file = readdir($opendir)) {
            if ($file != '.' && $file != '..') {
                searchDir($path.'/'.$file, $files);
            }
        }
        closedir($opendir);
    }
    if (!is_dir($path)) {
        $files[] = $path;
    }
}

$files = [];
searchDir("web_back_end", $files);
foreach ($files as $value) {
    $lang = basename($value);
    $filepath = "exportlog_back_end/".$lang;
    Create_backend($value, $filepath);
    echo "create $filepath\n";
}

$files = [];
searchDir("web_front", $files);
foreach ($files as $value) {
    $lang = basename($value);
    $lang = explode(".", $lang)[0].".php";
    $filepath = "exportlog_front/".$lang;
    Create_front($value, $filepath);
    echo "create $filepath\n";
}
