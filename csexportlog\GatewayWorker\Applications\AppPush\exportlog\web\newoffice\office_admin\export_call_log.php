<?php

namespace newoffice\office_admin\call;

date_default_timezone_set("PRC");
include_once __DIR__ . "/../../util/time.php";
require_once(__DIR__ . '/../../../common/define.php');
require_once(__DIR__ . '/../../../common/comm.php');
require_once(__DIR__ . '/../../../common/log_db_comm.php');
require_once(__DIR__ . '/../common/call_history_common.php');

function getTableCallHistoryDatas($log_tables, $datas, $limit)
{
    $result = [];
    if ($log_tables != null) {
        $db_log_data = getLOGTableCallHistoryDatas($log_tables, $datas, $limit, $result);
        $result = array_merge($result, $db_log_data);
    }

    return $result;
}

function getLOGTableCallHistoryDatas($log_tables, $datas, &$left, $result)
{
    $dbNode = getLogDbNode($datas['project_uuid']);
    $db = getLogDB($dbNode);
    $count = 0;
    foreach ($log_tables as $table) {
        if (!LOGDBTableExist($table)) {
            continue;
        }
        if ($left <= 0) {
            LOG_WARING("1.export LOG.CallHistory cloumn limit exceeded.");
            break;
        }

        //查询出token是否存在
        $one_table = getCallHistorylogDatas($db, $table, $count, $left, $datas);
        $result = array_merge($result, $one_table);
    }
    if ($left <= 0) {
        LOG_WARING("2.export LOG.CallHistory cloumn limit exceeded."); #不考虑刚好就是5000张得零界点
    }

    $db = null;
    return $result;
}

function getCallHistorylogDatas($db, $table, &$count, &$left, $datas)
{
    //查询出token是否存在
    $sth = $db->prepare("select * from $table where OfficeCompanyUUID = :OfficeCompanyUUID and StartTime >= :StartTime and StartTime <= :EndTime and CallType != 8 order by ID desc limit $left");
    $sth->bindParam(':OfficeCompanyUUID', $datas["company_uuid"], \PDO::PARAM_STR);
    $sth->bindParam(':StartTime', $datas["export_startime"], \PDO::PARAM_STR);
    $sth->bindParam(':EndTime', $datas["export_endtime"], \PDO::PARAM_STR);

    $ret = $sth->execute();
    $data = $sth->fetchALL(\PDO::FETCH_ASSOC);
    $count = $count + count($data);
    $left = $left - $count;

    return $data;
}

/**
 * @brief NewOffice导出呼叫记录
 */
function NewofficeAdminExportCallHistory($datas, $s3_cfg)
{
    $export_type = $datas["export_type"];
    $project_uuid = $datas["project_uuid"];
    
    // 从AKCS数据库和LOG数据库 获取分表表名
    $log_tables = [];
    getSqlTables("CallHistory", $project_uuid, $log_tables);

    // 从每个分表中查找数据
    $sql_data = \newoffice\office_admin\call\getTableCallHistoryDatas($log_tables, $datas, DOWNLOAD_CSV_LIMIT);
    $sql_datas = array_chunk($sql_data, DOWNLOAD_CSV_LIMIT);
    
    return NewOfficeExportLogCallHistoryCommon($datas, $s3_cfg, $sql_datas);
}
