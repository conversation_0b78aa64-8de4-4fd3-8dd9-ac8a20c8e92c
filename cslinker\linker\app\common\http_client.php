<?php

class HttpClient
{
    private $curl;
    private $url;
    private $res;
    private $httpCode;
    public function __construct($ssl = true)
    {
        $this->curl = curl_init();
        curl_setopt($this->curl, CURLOPT_HEADER, 0);
        curl_setopt($this->curl, CURLOPT_RETURNTRANSFER, 1);
        if ($ssl) {
            curl_setopt($this->curl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($this->curl, CURLOPT_SSL_VERIFYPEER, false);
        }
        curl_setopt($this->curl, CURLOPT_TIMEOUT, 15);
    }

    public function get($url)
    {
        $this->url = $url;
        curl_setopt($this->curl, CURLOPT_URL, $url);
        return $this;
    }

    public function post($url, $params = [], $timeout = 15)
    {
        $this->url = $url;
        curl_setopt($this->curl, CURLOPT_URL, $url);
        curl_setopt($this->curl, CURLOPT_POST, 1);
        curl_setopt($this->curl, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($this->curl, CURLOPT_POSTFIELDS, $params);
        return $this;
    }

    public function put($url, $params = [], $timeout = 15)
    {
        $this->url = $url;
        curl_setopt($this->curl, CURLOPT_URL, $url);
        curl_setopt($this->curl, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($this->curl, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($this->curl, CURLOPT_POSTFIELDS, $params);
        return $this;
    }

    public function delete($url, $params = [])
    {
        $this->url = $url;
        curl_setopt($this->curl, CURLOPT_URL, $url);
        curl_setopt($this->curl, CURLOPT_CUSTOMREQUEST, "DELETE");
        curl_setopt($this->curl, CURLOPT_POSTFIELDS, $params);
        return $this;
    }

    public function headers($header)
    {
        curl_setopt($this->curl, CURLOPT_HTTPHEADER, $header);
        return $this;
    }

    public function exec()
    {
        $this->res = curl_exec($this->curl);
        $this->httpCode = curl_getinfo($this->curl, CURLINFO_HTTP_CODE);
        $error = curl_error($this->curl);
        curl_close($this->curl);

        if ($this->httpCode != 200 && $this->httpCode != 202) {
            throw new \Exception("request url:{$this->url}, http code is {$this->httpCode} not 200. error is ".$error);
        }

        return $this;
    }

    public function result()
    {
        $result = $this->res;
        $decodedResult = json_decode($result, true);

        // 检查解码是否成功
        if (json_last_error() === JSON_ERROR_NONE) {
            return $decodedResult;
        } else {
            return $result; // 返回原始字符串
        }
    }

    public function code()
    {
        $code = $this->httpCode;
        return $code;
    }
}