<?php

require_once(dirname(__FILE__).'/../time.php');
require_once(dirname(__FILE__).'/../check_expire_common_v4500.php');

const ORDER_STATUS_ON = 0; //订单状态：进行中
const ORDER_STATUS_SUC = 1; //订单状态：成功
const ORDER_STATUS_FAIL = 2; //订单状态：失败

const ORDER_TYPE_AUTO_SEND = 1;
const ORDER_TYPE_MANUAL_SEND = 0;

const OEM_TYPE_AK = 0;
const OEM_TYPE_MYSMART = 1;
const OEM_TYPE_FASTTEL = 2;
const OEM_TYPE_BELAHOME = 3;
const OEM_TYPE_AZER = 4; //阿塞拜疆OEM类型
const OEM_NAME_AK = "Akuvox";
const OEM_NAME_AZER = "Azerbaijan";

const ACCOUNT_GRADE_DIS = 11;

//获取满足自动发送条件的相关公维金信息
function GetAutoSendRoomList($before)
{
    global $db;
    $sql = "select R.PersonalAccountUUID,R.CommunalFee,R.Area,R.EffectTime,R.ExpireTime,R.ProjectUUID,R.IsDefaultCommunalFee, C.CommunalFee as CommFee
                from RoomCommunalFeeInfo R Left join Account A on R.ProjectUUID = A.UUID
                Left join CommunityCommunalFeeInfo C on A.UUID = C.ProjectUUID 
                where R.AutoSendBills = 1 and R.Area > 0 and 
                TO_DAYS(" . GetUserTimeNowSQL("A.Timezone") . ") + :before = TO_DAYS(R.ExpireTime)";
    LOG_INFO("azer check expire, execute sql: " . $sql . ":before =" . $before);
    $sth = $db->prepare($sql);
    $sth->bindValue(':before', $before, PDO::PARAM_INT);
    $sth->execute();
    $expire_room_list = $sth->fetchAll(pdo::FETCH_ASSOC);
    return $expire_room_list;
}

//根据UUID获取主账号信息
function GetMasterAccountByUUID($uuid)
{
    global $db;
    $sth = $db->prepare("select Account,UserInfoUUID,ParentUUID,Language,UUID from PersonalAccount where UUID = :uuid and Role = :role");
    $sth->bindValue(':uuid', $uuid, PDO::PARAM_STR);
    $sth->bindValue(':role', ACCOUNT_ROLE_COMMUNITY_MAIN, PDO::PARAM_INT);
    $sth->execute();
    $main_account = $sth->fetch(PDO::FETCH_ASSOC);
    return $main_account;
}

//根据UserInfoUUID获取邮箱
function GetEmailByUUID($uuid)
{
    global $medooDb;
    return $medooDb->get("PersonalAccountUserInfo", ["Email"] , ["UUID" => $uuid])["Email"];
}

//根据ProjectUUID获取DisUUID
function GetDisUUIDByProjectUUID($project_uuid)
{
    global $db;
    $sth = $db->prepare("select D.UUID from Account D left join Account P on P.ParentUUID = D.UUID where P.UUID = :uuid And D.Grade = :grade_dis");
    $sth->bindValue(':grade_dis', ACCOUNT_GRADE_DIS, PDO::PARAM_INT);
    $sth->bindValue(':uuid', $project_uuid, PDO::PARAM_STR);
    $sth->execute();
    $dis_account = $sth->fetch(PDO::FETCH_ASSOC);
    return $dis_account['UUID'];
}

//将订单加入数据库
function AddAutoSendOrder($room_info, $account_info, $communal_fee)
{
    global $server_tag;
    global $db;

    $order_uuid = GenerateUUID($server_tag);
    $order_type_auto_send = 1;//自动发送
    $area = intval($room_info['Area']);

    //自动发送 时间构造
    $currentDate = new DateTime($room_info['ExpireTime']);
    // 增加一个月
    $currentDate->modify('+1 month');
    // 获取下一个月同一天的时间戳
    $nextMonthSameDayTimestamp = $currentDate->format('Y-m-d H:i:s'); //转化为数据库时间形式

    $dis_uuid = GetDisUUIDByProjectUUID($room_info['ProjectUUID']);

    $sth = $db->prepare("insert into CommunalFeeOrderList (OrderNumber, Status, Area,
                               CommunalFee, Amount, PersonalAccountUUID, ProjectUUID,
                               Payor, OrderType, EffectTime, ExpireTime, UUID, DisUUID) values
                               (:ordernum, :status, :area, :fee, :amount, :account_uuid, :project_uuid,
                               :payor, :order_type, :effect_time, :expire_time, :uuid, :dis_uuid)");
    $sth->bindValue(':ordernum', GetOrderNum($order_type_auto_send), PDO::PARAM_STR);
    $sth->bindValue(':status', ORDER_STATUS_ON, PDO::PARAM_INT);
    $sth->bindValue(':area', $area, PDO::PARAM_INT);
    $sth->bindValue(':fee', $communal_fee, PDO::PARAM_INT);
    //amount * 100= area * 100 * fee * 100 / 100 
    $amount = round($area * $communal_fee / 100);
    $sth->bindValue(':amount', $amount, PDO::PARAM_INT);
    $sth->bindValue(':account_uuid', $account_info['UUID'], PDO::PARAM_STR);
    $sth->bindValue(':project_uuid', $room_info['ProjectUUID'], PDO::PARAM_STR);
    //自动发送 有效期向后推一个月
    $sth->bindValue(':effect_time', $room_info['ExpireTime'], PDO::PARAM_STR);
    $sth->bindValue(':expire_time', $nextMonthSameDayTimestamp, PDO::PARAM_STR);
    $sth->bindValue(':payor', "", PDO::PARAM_STR); //未支付，获取不到付款账号
    $sth->bindValue(':order_type', $order_type_auto_send, PDO::PARAM_INT);
    $sth->bindValue(':uuid', $order_uuid, PDO::PARAM_STR);
    $sth->bindValue('dis_uuid', $dis_uuid, PDO::PARAM_STR);

    $sth->execute();

    return $order_uuid;
}

//获取自动发送对应的订单，返回订单UUID
function GetAutoSendOrderUUID($room_info, $account_info, $communal_fee)
{
    global $db;
    //获取该房间对应最近的一条订单记录
    $sth = $db->prepare("select UUID, Status from CommunalFeeOrderList where PersonalAccountUUID = :account_uuid 
                                        and OrderType = :order_type order by ID desc limit 1");
    $sth->bindValue(':account_uuid', $account_info['UUID'], PDO::PARAM_STR);
    $sth->bindValue(':order_type', ORDER_TYPE_AUTO_SEND, PDO::PARAM_INT);
    $sth->execute();
    $order_info = $sth->fetch(PDO::FETCH_ASSOC);
    //找不到旧订单或前一条订单状态为成功/失败，则说明当前周期内无有效订单，需要新增。否则就用旧订单的UUID即可
    if (!$order_info || $order_info['Status'] == ORDER_STATUS_SUC || $order_info['Status'] == ORDER_STATUS_FAIL)
    {
        return AddAutoSendOrder($room_info, $account_info, $communal_fee);
    }
    return $order_info['UUID'];
}

//获取公维金支付链接
function GetPayLink($uuid)
{
    return "https://" . WEB_DOMAIN . "/smartplus/BankPayRedirect.html?UUID=" . $uuid;
}
//获取订单号, 规则为00/10(自动/手动发送的订单)+time()+rand(10000,99999)共17位
//如*****************
function GetOrderNum($auto_send)
{
    if($auto_send)
    {
        return "00" . time() . rand(10000, 99999);
    }
    return "10" . time() . rand(10000, 99999);
}

//根据timestamp类型的Date获取日期
function GetDay($date)
{
    $time_stamp = strtotime($date);
    return intval(date('d', $time_stamp));
}
//根据timestamp类型的Date获取月份，1-12对应1月-12月
function GetMonth($date)
{
    $time_stamp = strtotime($date);
    return intval(date('n', $time_stamp));
}

//根据timestamp类型的Date获取四位数年份
function GetYear($date)
{
    $time_stamp = strtotime($date);
    return intval(date('Y', $time_stamp));
}

//阿塞拜疆定制云 发送邮件通知消息
function SendAzEmailNotify($email_info)
{
    $payload = [
        "ver" => "1",
        "OEM" => OEM_NAME_AK,
        "SUBOEM"=>OEM_NAME_AZER,
        "app_type" => "email",
        "data" => json_encode($email_info)
    ];

    LOG_INFO("Send Azerbaijan Paylink Email, email type." . $email_info['email_type']. ",email:" . $email_info['email']);
    $data[] = $email_info['email'];
    $data[] = json_encode($payload);

    $sendEmailNotifySocket = new CSendEmailNotifySocket();
    $sendEmailNotifySocket->setMsgID(MSG_P2A_SEND_EMAIL_CRONTAB_NOTIFY);
    $sendEmailNotifySocket->setMsgFrom(PROJECT_TYPE_RESIDENCE);
    $sendEmailNotifySocket->copy($data);
}

//阿塞拜疆定制云 发送Message通知消息
function SendAzMessageNotify($message_info)
{
    $payload = [
        "OEM" => OEM_NAME_AZER,
        "data" => json_encode($message_info)
    ];
    LOG_INFO("Send Azerbaijan Paylink Message, User Account:" . $message_info['account']);
    $data[] = $message_info['account'];
    $data[] = json_encode($payload);

    $sendMessageNotifySocket = new CSendMessageNotifySocket();
    $sendMessageNotifySocket->setMsgID(MSG_P2A_SEND_MESSAGE_CRONTAB_NOTIFY);
    $sendMessageNotifySocket->setMsgFrom(PROJECT_TYPE_RESIDENCE);
    $sendMessageNotifySocket->setMsgOEM(OEM_TYPE_AZER);
    $sendMessageNotifySocket->copy($data);
}