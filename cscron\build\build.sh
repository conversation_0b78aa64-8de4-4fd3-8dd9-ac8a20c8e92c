#!/bin/bash

WORK_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
AKCS_SRC_ROOT=${WORK_DIR}/../..
AKCS_SRC_CRONTAB=${AKCS_SRC_ROOT}/cscron
AKCS_SRC_CSBP=${AKCS_SRC_ROOT}/csbp
AKCS_SRC_CSBASE=${AKCS_SRC_ROOT}/csbase

#在源码包的同一路径下生成安装包
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/akcs_cscron_packeg

build() {
	#先清理上一次的安装包
	if [ -d $AKCS_PACKAGE_ROOT ]
	then
		rm -rf $AKCS_PACKAGE_ROOT
	fi
	mkdir -p $AKCS_PACKAGE_ROOT
	chmod -R 777 $AKCS_PACKAGE_ROOT/

	#copy crontab
	echo "coping crontab files..."
	cp -rf $AKCS_SRC_CRONTAB/* $AKCS_PACKAGE_ROOT/

	#个组件均编译成功
	echo "-----------------------all build successful-------------------------"

	#打成tar包
	cd ${AKCS_PACKAGE_ROOT}/../ || exit 1
	rm -rf akcs_cscron_packeg.tar.gz
	tar zcvf akcs_cscron_packeg.tar.gz akcs_cscron_packeg
	echo "${AKCS_PACKAGE_ROOT}/akcs-packages.tar.gz is created successful."
}

clean() {
	echo "clean successful."
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean crontab application, eg : $0 clean "
        echo "  $0 build ---  build crontab application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac
