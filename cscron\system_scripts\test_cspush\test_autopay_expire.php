<?php
require_once(dirname(__FILE__) . '/../checkexpire/time.php');
require_once(dirname(__FILE__) . '/../checkexpire/check_autopay_common.php');
require_once(dirname(__FILE__) . '/../checkexpire/check_expire_common_v4500.php');

// 模拟 `check_autopay_expire.php` 的发送数据到cspush
function testAutoPayNotify()
{
    $is_batch = true;
    $order_pay_type = AUTO_PAY_ORDER_TYPE_OFFICE;

    $email_info = array();
    if (!$is_batch && ($order_pay_type == AUTO_PAY_ORDER_TYPE_COMMUNITY || $order_pay_type == AUTO_PAY_ORDER_TYPE_OFFICE)) {
        $email_info['community'] = "community";
    }

    //pay_type转成project_type
    if ($order_pay_type == AUTO_PAY_ORDER_TYPE_COMMUNITY) {
        $email_info['project_type'] = PROJECT_TYPE_RESIDENCE;
    } else if ($order_pay_type == AUTO_PAY_ORDER_TYPE_OFFICE) {
        $email_info['project_type'] = PROJECT_TYPE_OFFICE;
    } else {
        $email_info['project_type'] = PROJECT_TYPE_PERSONAL;
    }

    $email_info['content_project'] = "content_project";
    $email_info['pay_amount'] = 123456.789;
    $email_info['next_pay_date'] = "2024-10-10 10:00:00";
    $email_info['last_pay_date'] = "2024-09-10 10:00:00";
    $email_info['pay_method'] = (1 == 0 ? "Paypal" : "Stripe");
    $email_info['email_type'] = "auto_pay_remind";
    $email_info['before'] = 123;
    $email_info['project_uuid'] = "project_uuid123456";
    $email_info['user'] = "PropertyManager111";
    $email_info['language'] = "en";
    $email_info['email'] = "<EMAIL>";

    sendEmailNotify($email_info);
}

testAutoPayNotify();
