# 支持cron的PHP镜像构建 镜像名：cscron_php8.0
FROM php:8.0
LABEL version="0.1" maintainer="zhenxing.chen"

RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.aliyun.com\/debian-security/g' /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y --no-install-recommends \
    net-tools vim openssl libssl-dev zip python3 python3-pip libcurl4-openssl-dev cron librdkafka-dev && \
    docker-php-source extract && \
    pip3 install --no-cache-dir pymysql && \
    pecl install rdkafka && \
    docker-php-ext-enable rdkafka && \
    pecl install protobuf-3.19.4 && \
    docker-php-ext-enable protobuf && \
    pecl install redis && \
    docker-php-ext-enable redis && \
    docker-php-ext-install pdo_mysql mysqli pcntl posix curl && \
    docker-php-ext-enable pdo_mysql mysqli pcntl posix && \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# 设置工作目录
WORKDIR /usr/local/bin

