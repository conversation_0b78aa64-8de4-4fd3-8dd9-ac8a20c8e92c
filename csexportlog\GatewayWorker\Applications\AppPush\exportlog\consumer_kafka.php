<?php

require_once(dirname(__FILE__) . '/akcs_log.php');
require_once(dirname(__FILE__) . '/akcs_monitor.php');
require_once(dirname(__FILE__) . '/exportlog.php');
require_once(dirname(__FILE__) . '/common/comm.php');
require_once(dirname(__FILE__) . '/common/define.php');
require_once(dirname(__FILE__) . '/s3_handle.php');

class AkKafkaConsumer
{
    private $consumer;
    private $consumer_config;
    private $kafka_conf;
    private $work_num;
    private $topic;
    private $consumer_group;
    private $s3_cfg;
    public function __construct($topic, $consumer_group)
    {
        $this->topic = $topic;
        $this->consumer_group = $consumer_group;
        $this->s3_cfg = new S3Handle();

        #$this->consumer_config = parse_ini_file($consumer_config_path, true);
        #$this->work_num = $this->consumer_config['csexportlog']['work_number'];
        #$this->work_queue_path = $this->consumer_config['csexportlog']['work_queue_path'];

        //https://arnaud.le-blanc.net/php-rdkafka-doc/phpdoc/book.rdkafka.html  接口手册
        $this->kafka_conf = new RdKafka\Conf();
        // Set a rebalance callback to log partition assignments (optional)
        // 当有新的消费进程加入或者退出消费组时，kafka 会自动重新分配分区给消费者进程，这里注册了一个回调函数，当分区被重新分配时触发
        $this->kafka_conf->setRebalanceCb(function (RdKafka\KafkaConsumer $kafka, $err, array $partitions = null) {
            switch ($err) {
                case RD_KAFKA_RESP_ERR__ASSIGN_PARTITIONS:
                    echo "Assign: ";
                    var_dump($partitions);
                    $kafka->assign($partitions);
                    break;

                case RD_KAFKA_RESP_ERR__REVOKE_PARTITIONS:
                    echo "Revoke: ";
                    var_dump($partitions);
                    $kafka->assign(null);
                    break;

                default:
                    throw new \Exception($err);
            }
        });

        // 配置groud.id 具有相同 group.id 的consumer 将会处理不同分区的消息，
        //所以同一个组内的消费者数量如果订阅了一个topic， 那么消费者进程的数量多于这个topic 分区的数量是没有意义的。
        $this->kafka_conf->set('group.id', $this->consumer_group);
        $this->kafka_conf->set('enable.auto.commit', 'false');
        $this->kafka_conf->set('max.poll.interval.ms', 3600000);
        //添加 kafka集群服务器地址
        $this->kafka_conf->set('metadata.broker.list', KAFKA_IP);
        $topicConf = new RdKafka\TopicConf();
        //当没有初始偏移量时，从哪里开始读取
        $topicConf->set('auto.offset.reset', 'earliest');
        // Set the configuration to use for subscribed/assigned topics
        #$this->kafka_conf->setDefaultTopicConf($topicConf);
        $this->consumer = new RdKafka\KafkaConsumer($this->kafka_conf);
        // 让消费者订阅log 主题
        $this->consumer->subscribe([$this->topic]);

        $this->producer = new RdKafka\Producer();
        $this->producer->addBrokers(KAFKA_IP);
        $this->kafka_producer_conf = new RdKafka\TopicConf();
        // -1必须等所有brokers同步完成的确认 1当前服务器确认 0不确认，这里如果是0回调里的offset无返回，如果是1和-1会返回offset
        // 我们可以利用该机制做消息生产的确认，不过还不是100%，因为有可能会中途kafka服务器挂掉
        $this->kafka_producer_conf->set('request.required.acks', 0);
    }
    
    public function Start()
    {
        $topic = $this->producer->newTopic(KAFKA_PUSH_EMAIL_TOPIC, $this->kafka_producer_conf);

        while (true) {
            $message = $this->consumer->consume(120 * 1000);
            switch ($message->err) {
                case RD_KAFKA_RESP_ERR_NO_ERROR:
                    $data = $message->payload;
                    $message_data = json_decode($data, true);
                    $message_data['kafka_offset'] = $message->offset;
                    $message_data['kafka_partition'] = $message->partition;
                    LOG_INFO("AkKafkaConsumer consume partition=" . $message->partition);

                    // 初始化Kafka主题
                    $ret = ExportLog($message_data, $this->s3_cfg);
                    if ($ret['result'] == 0 && $ret["export_type"] != EXPORTLOG_TYPE_EXCEL_ONLY) {
                        $msg = "";
                        $emailType = "";
                        $partitionEnd = 0;

                        if ($ret["export_type"] == EXPORTLOG_TYPE_TRIGGER) {
                            $partitionEnd = 4;
                            $msg = $ret['msg'];
                            $ret['name'] = "TRIGGER";
                            $ret['language'] = "en";
                            $ret['community'] = "TRIGGER";
                            $emailType = "trigger_csexportlog_chain";
                        } else {
                            $emailType = "exportlog";
                        }

                        // 设置email信息
                        $emailinfo = [
                            "email"      => $ret["email"],
                            "name"       => $ret["name"],
                            "language"   => $ret["language"],
                            "urls"       => $ret["urls"],
                            "community"  => $ret["community"],
                            "email_type" => $emailType,
                            "msg"        => $msg
                        ];

                        // 设置推送信息
                        $push = [
                            "app_type" => "email",
                            'OEM' => "Akuvox",
                            'data' => json_encode($emailinfo)
                        ];

                        // 执行produce操作
                        for ($partition = 0; $partition <= $partitionEnd; ++$partition) {
                            $topic->produce($partition, 0, json_encode($push), $emailinfo["email"]);
                        }

                        $this->producer->flush(1000);
                    } elseif ($ret['result'] == -1) {
                        $mesg = "partition=" . $message->partition . " offset=" . $message->offset . " group" . $this->consumer_group . "\n";
                        LOG_ERROR($mesg);
                    }

                    $this->consumer->commit();
                    unset($message_data);
                    unset($data);
                    unset($ret);
                    break;
                case RD_KAFKA_RESP_ERR__PARTITION_EOF:
                    LOG_INFO("No more messages; will wait for more " . $this->consumer_group);
                    break;
                case RD_KAFKA_RESP_ERR__TIMED_OUT:
                    // LOG_INFO("Timed out " . $this->consumer_group);
                    break;
                default:
                    LOG_ERROR("Exception: errstr=" . $message->errstr() . ", err=" . $message->err);
                    throw new \Exception($message->errstr(), $message->err);
                    break;
            }

            unset($message);
        }
    }
}
