<?php
require_once __DIR__ . '/../../../common/email_common.php';

class InstallerHandler 
{
    private $daysBefore;
    private $expireDataMap;
    private $personalExpireInfoList;
    private $personalChargeModeMap;
    public $expireDataControl;

    public function __construct() {
    }
    
    public function setExpireParams($daysBefore, $expireDataMap, $personalExpireInfoList, $personalChargeModeMap, $expireDataControl) {
        $this->daysBefore = $daysBefore;
        $this->expireDataMap = $expireDataMap;
        $this->personalExpireInfoList = $personalExpireInfoList;
        $this->personalChargeModeMap = $personalChargeModeMap;
        $this->expireDataControl = $expireDataControl;
    }

    public function filterData() {
        // 判断社区的付费模式 是否要发送给Installer
        $filteredExpireData = $this->filterPersonalChargeMode();
        return $filteredExpireData;
    }

    private function filterPersonalChargeMode()
    {
         // 判断单住户的付费模式 是否要发送给Installer
         $payTypeList = array(
            EmailNotifyRule::PAY_ITEM_USER_APP,
            EmailNotifyRule::PAY_ITEM_FEATURE_PLAN,
            EmailNotifyRule::PAY_ITEM_VIDEO_RECORD,
            EmailNotifyRule::PAY_ITEM_THIRDLOCK_PUBLIC_AND_APT,
        );

        // 获取每种付费类型发送给Ins的用户列表
        $payTypePersonalAccountUUIDMap = array(); 
        foreach ($payTypeList as $payType) {
            foreach ($this->personalExpireInfoList as $personalAccountInfo) {
                $personalChargeMode = $this->personalChargeModeMap[$personalAccountInfo['PersonalAccountUUID']];
                if ($personalChargeMode && PersonalCheck::needNotifyIns($payType, $personalChargeMode, $this->daysBefore)) {
                    $payTypePersonalAccountUUIDMap[$payType][] = $personalAccountInfo['PersonalAccountUUID'];
                }
            }
        }

        // 获取每种付费类型发送给Ins的用户列表
        $filteredExpireData = array();
        foreach ($this->expireDataMap as $payType => $expireData) {
            foreach ($expireData as $expireInfo) {
                if (isset($payTypePersonalAccountUUIDMap[$payType]) && in_array($expireInfo['PersonalAccountUUID'], $payTypePersonalAccountUUIDMap[$payType])) {
                    $filteredExpireData[$payType][] = $expireInfo;
                }
            }
        }
        return $filteredExpireData;
    }

    // 发送邮件给Ins
    public function sendEmail($emailDataMap, $recevierInfoMap) {

        foreach ($emailDataMap as $insUUID => $emailData) {
            $disEmail = $recevierInfoMap[$insUUID]['email'];
            if (empty($disEmail)) {
                LOG_INFO("disEmail is empty, skip, disUUID: " . $insUUID);
                continue;
            }

            $emailInfo['email'] = $disEmail;
            $emailInfo["web_ip"] = WEB_DOMAIN;
            $emailInfo['before'] = $this->daysBefore;
            $emailInfo['oem'] = $recevierInfoMap[$insUUID]['oem'];
            $emailInfo['user'] = $recevierInfoMap[$insUUID]['user'];
            $emailInfo['language'] = $recevierInfoMap[$insUUID]['language'];
            $emailInfo['is_show_paylink'] = SHOW_PAYLINK_NONE;
            if ($this->daysBefore == -1) {
                $emailInfo['email_type'] = "single_family_has_expired";
            } else {
                $emailInfo['email_type'] = "single_family_will_expire";
            }

            $emailInfo['list'] = $emailData;
            sendEmailNotifyNew($emailInfo);
        }
    }
}