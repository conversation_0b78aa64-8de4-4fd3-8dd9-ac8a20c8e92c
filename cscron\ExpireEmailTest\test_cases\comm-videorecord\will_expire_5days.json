[{"TestName": "VideoRecordWillExpire5Days", "PayMode": "DIS", "ExpireDays": "5", "SendType": "SendToDisOrIns", "TestResult": [{"EmailType": "community_video_record_will_expire", "Email": "$DIS_EMAIL"}]}, {"TestName": "VideoRecordWillExpire5Days", "PayMode": "DIS", "ExpireDays": "5", "SendType": "SendToDisOrIns", "TestResult": [{"EmailType": "community_video_record_will_expire", "Email": "$SUBDIS_EMAIL"}]}, {"TestName": "VideoRecordWillExpire5Days", "PayMode": "DIS", "ExpireDays": "5", "SendType": "SendToPMOrEndUser", "TestResult": [{"EmailType": "community_video_record_will_expire", "Email": "$PM_EMAIL"}]}, {"TestName": "VideoRecordWillExpire5Days", "PayMode": "INS", "ExpireDays": "5", "SendType": "SendToDisOrIns", "TestResult": [{"EmailType": "community_video_record_will_expire", "Email": "$INS_EMAIL"}]}, {"TestName": "VideoRecordWillExpire5Days", "PayMode": "INS", "ExpireDays": "5", "SendType": "SendToPMOrEndUser", "TestResult": [{"EmailType": "community_video_record_will_expire", "Email": "$PM_EMAIL"}, {"EmailType": "community_video_record_will_expire", "Email": "$INS_EMAIL"}]}, {"TestName": "VideoRecordWillExpire5Days", "PayMode": "PM", "ExpireDays": "5", "SendType": "SendToPMOrEndUser", "TestResult": [{"EmailType": "community_video_record_will_expire", "Email": "$PM_EMAIL"}]}]