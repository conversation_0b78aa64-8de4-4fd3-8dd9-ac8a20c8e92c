<?php
/* RentManager功能过期检测逻辑（邮件数据格式参考: test_rent_expire.php） */
require_once(dirname(__FILE__) . '/rent_common.php');
require_once(dirname(__FILE__) . '/../email_type.php');
require_once(dirname(__FILE__) . '/../check_expire_common_v4500.php');

const ACCOUNT_ROLE_TYPE_INS = 22;
const ACCOUNT_ROLE_TYPE_PM = 31;

// 查找$remainDays天后要过期的项目, $remainDays<0表示已经过期的项目
function GetExpireRecordFromRentManagerCustomer($remainDays)
{
    global $db;
    $sql = "SELECT R.RentManagerCompanyUUID,R.RentManagerCompanyName,R.RentManagerCompanyCode,R.PmUUID,R.InstallerUUID, 
            PI.FirstName AS PmFirstName,PI.LastName AS PmLastName, AU.Email AS PmEmail, PM.Language AS PmLanguage 
            FROM RentManagerCustomer R 
            INNER JOIN Account PM ON PM.UUID = R.PmUUID 
            INNER JOIN PropertyInfo PI ON PI.AccountID=PM.ID 
            INNER JOIN AccountMap AP ON AP.AccountUUID=PM.UUID 
            INNER JOIN AccountUserInfo AU ON AU.UUID=AP.UserInfoUUID 
            WHERE (R.ExpiredTime >= CURDATE() + INTERVAL (:remainDays) DAY) 
            AND (R.ExpiredTime < CURDATE() + INTERVAL (:remainDays + 1) DAY) 
            ORDER BY InstallerUUID;";

    $sth = $db->prepare($sql);
    $sth->bindParam(':remainDays', $remainDays, PDO::PARAM_INT);
    $sth->execute();

    $expire_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach($expire_list as $row => $item){
        $expire_list[$row]['PmEmail'] = DataConfusion::getInstance()->decrypt($item['PmEmail']);
    }

    return $expire_list;
}

function CheckRentCustomerExpire($remainDays)
{
    // 查找$remainDays天后要过期的项目
    $expire_list = GetExpireRecordFromRentManagerCustomer($remainDays);
    foreach ($expire_list as $row => $item) {

        // 跳过迁移的项目
        if (checkAppIsMigrateDisByUUID($item['InstallerUUID'])) {
            $expire_list[$row] = array();
            continue;
        }
    }

    // 邮件通知
    SendEmail($expire_list, $remainDays);
}

function SendEmail($expire_list, $remainDays)
{
    if ($remainDays == 5 || $remainDays == 15) {
        // RentManager即将过期，发送邮件通知Ins
        SendWillExpireEmailToInstaller(EMAIL_TYPE_RENT_CUSTOMER_Will_EXPIRE_TO_INS, $expire_list, $remainDays);
    } elseif ($remainDays == 3) {
        // RentManager即将过期，发送邮件通知PM
        SendWillExpireEmailToPM(EMAIL_TYPE_RENT_CUSTOMER_WILL_EXPIRE_TO_PM, $expire_list, $remainDays);
    } elseif ($remainDays == -1) {
        // RentManager已经过期，发送邮件通知Ins
        SendHasExpiredEmailToInstaller(EMAIL_TYPE_RENT_CUSTOMER_HAS_EXPIRED_TO_INS, $expire_list, $remainDays);
        // RentManager已经过期，发送邮件通知PM
        SendHasExpiredEmailToPM(EMAIL_TYPE_RENT_CUSTOMER_HAS_EXPIRED_TO_PM, $expire_list, $remainDays);
        // RentManager过期，发送邮件通知超管的运营人员
        SendHasExpiredEmailToSuperAdmin(EMAIL_TYPE_RENT_CUSTOMER_HAS_EXPIRED_TO_ADMIN, $expire_list, $remainDays);
    }
}

// RentManager即将过期，发送邮件通知Ins
function SendWillExpireEmailToInstaller($email_type, $expire_list, $remainDays)
{
    global $db;
    $emailInfo = array(
        'email_type' => $email_type,
        'remaining_days' => $remainDays
    );

    $pm_list = array();
    $pre_install_uuid = "";
    array_push($expire_list, array('InstallerUUID' => ''));     /// 末尾插入一个空数组，便于处理最后一条数据
    foreach ($expire_list as $row => $item) {
        $install_uuid = empty($item['InstallerUUID']) ? "" : $item['InstallerUUID'];

        // 和上条记录的 install_uuid 不一样，把上一批次的邮件发送出去
        if ($pre_install_uuid != $install_uuid) {
            if (count($pm_list) > 0) {
                $pre_installer_info = getAccountInfoByAccountUUID($db, $pre_install_uuid);
                $ins_binling_info = getInstallerEmailInfoByInsID($pre_installer_info['ID']);

                // 构造邮件数据
                $emailInfo['user'] = $pre_installer_info['Account'];
                $emailInfo['account'] = $pre_installer_info['Account'];
                $emailInfo['email'] = $ins_binling_info['Email'];
                $emailInfo['language'] = $pre_installer_info['Language'];
                $emailInfo['web_ip'] = WEB_DOMAIN;
                $emailInfo['pm_list'] = $pm_list;


                // 检查付费模式（dis开启下级付费即可）
                $emailInfo["is_show_paylink"] = SHOW_PAYLINK_NONE;
                if (PersonalCheck::ifInsHasPayPermission($pre_install_uuid)) {
                    $emailInfo["is_show_paylink"] = SHOW_PAYLINK_INSTALLER;
                }

                SendRentEmailNotify($emailInfo);
                usleep(100 * 1000);
            }

            // 重置变量
            $pm_list = array();
            $pre_install_uuid = $install_uuid;
        }

        // 插入PM列表
        if (!empty($item['PmEmail'])) {
            array_push($pm_list, array(
                'company_name' => $item['RentManagerCompanyName'],
                'company_code' => $item['RentManagerCompanyCode'],
                'pm_name' => $item['PmFirstName'] . ' ' . $item['PmLastName'],
                'pm_email' => $item['PmEmail']
            ));
        }
    }
}

// RentManager已经过期，发送邮件通知Ins
function SendHasExpiredEmailToInstaller($email_type, $expire_list, $remainDays)
{
    SendWillExpireEmailToInstaller($email_type, $expire_list, $remainDays);
}

// RentManager即将过期，发送邮件通知PM
function SendWillExpireEmailToPM($email_type, $expire_list, $remainDays)
{
    global $db;
    $emailInfo = array();
    $emailInfo['email_type'] = $email_type;
    $emailInfo['remaining_days'] = $remainDays;
    foreach ($expire_list as $row => $item) {
        if (empty($item['PmUUID']) || empty($item['PmEmail'])) {
            continue;
        }

        // 获取pm信息
        $emailInfo['user'] = $item['PmFirstName'] . ' ' . $item['PmLastName'];
        $emailInfo['language'] = $item['PmLanguage'];
        $emailInfo['email'] = $item['PmEmail'];
        SendRentEmailNotify($emailInfo);
        usleep(100 * 1000);
    }
}

// RentManager已经过期，发送邮件通知PM
function SendHasExpiredEmailToPM($email_type, $expire_list, $remainDays)
{
    SendWillExpireEmailToPM($email_type, $expire_list, $remainDays);
}

// RentManager过期，发送邮件通知Admin
function SendHasExpiredEmailToSuperAdmin($email_type, $expire_list, $remainDays)
{
    global $db;
    $pm_list = array();
    foreach ($expire_list as $row => $item) {
        if (!isset($item['PmUUID']) || !isset($item['InstallerUUID'])) {
            continue;
        }

        // 插入PM列表
        array_push($pm_list, array(
            'company_name' => $item['RentManagerCompanyName'],
            'company_code' => $item['RentManagerCompanyCode'],
            'pm_name' => $item['PmFirstName'] . ' ' . $item['PmLastName'],
            'pm_email' => $item['PmEmail']
        ));
    }

    $admin_info = getSuperAdministrator($db);

    // 构造邮件数据
    if (!empty($admin_info['EmailForRentManager']) && count($pm_list) > 0) {

        // 配置了多个客服邮箱，每个邮箱都单独发送（逗号分割）
        $mail_items = explode(",", $admin_info['EmailForRentManager']);
        foreach ($mail_items as $item) {
            $emailInfo = array();
            $emailInfo['email'] = $item;
            $emailInfo['user'] = 'Administrator';
            $emailInfo['email_type'] = $email_type;
            $emailInfo['remaining_days'] = $remainDays;
            $emailInfo['language'] = $admin_info['Language'];
            $emailInfo['pm_list'] = $pm_list;
            SendRentEmailNotify($emailInfo);
        }
    }
}

$medooDb = getMedooDb();
$db = $medooDb->pdo;

CheckRentCustomerExpire(15);
sleep(1);

CheckRentCustomerExpire(5);
sleep(1);

CheckRentCustomerExpire(3);
sleep(1);

CheckRentCustomerExpire(-1);
