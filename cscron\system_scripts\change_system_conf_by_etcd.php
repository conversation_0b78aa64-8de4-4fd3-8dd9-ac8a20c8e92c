<?php

function cmd_usage($cmd){
	echo("usage: ".$cmd. " <etcd_addr> <key>\n");  
	exit(0);
}

if ($argc < 3){  
	cmd_usage($argv[0]);
}
$etcd_addr = $argv[1];
$key = $argv[2];

if ($key == "/akconf/db_addr"){
    $retval =  "";
    $value = "";
    exec("/bin/etcdctl --endpoints=\"http://$etcd_addr\" get $key", $output, $return_val);
    if ($return_val == 0){
       $value = $output[1];
    }
    if($value){
        echo "change_system: /akconf/db_addr change ".$value;
        db_change($value);
    }
}

function db_change($value){
    $value_arr = explode(":",$value);
    $dbhost = $value_arr[0];
    $dbport = $value_arr[1];

    $app_exec[0] = "sed -i 's/^.*dbhost =.*/\$dbhost = \"$dbhost\";/g' /usr/local/akcs/cscron/check_expire_common_v4500.php";
    $app_exec[1] = "sed -i 's/^.*dbport =.*/\$dbport = \"$dbport\";/g' /usr/local/akcs/cscron/check_expire_common_v4500.php";
    $app_exec[2] = "sed -i 's/^.*dbhost =.*/\$dbhost = \"$dbhost\";/g' /usr/local/akcs/cscron/clearCapture2.php";
    $app_exec[3] = "sed -i 's/^.*dbport =.*/\$dbport = \"$dbport\";/g' /usr/local/akcs/cscron/clearCapture2.php";
    $app_exec[4] = "sed -i 's/^.*dbhost =.*/\$dbhost = \"$dbhost\";/g' /usr/local/akcs/cscron/clearModel.php";
    $app_exec[5] = "sed -i 's/^.*dbport =.*/\$dbport = \"$dbport\";/g' /usr/local/akcs/cscron/clearModel.php";
    $app_exec[6] = "sed -i 's/^.*dbhost =.*/\$dbhost = \"$dbhost\";/g' /usr/local/akcs/cscron/data_check.php";
    $app_exec[7] = "sed -i 's/^.*dbport =.*/\$dbport = \"$dbport\";/g' /usr/local/akcs/cscron/data_check.php";
    $app_exec[8] = "sed -i 's/^.*dbhost =.*/\$dbhost = \"$dbhost\";/g' /usr/local/akcs/cscron/common/db_common.php";
    $app_exec[9] = "sed -i 's/^.*dbport =.*/\$dbport = \"$dbport\";/g' /usr/local/akcs/cscron/common/db_common.php";
    $app_exec[10] = "service cron reload";

    foreach($app_exec as $app_exec_value){
        exec($app_exec_value);
    }
}
